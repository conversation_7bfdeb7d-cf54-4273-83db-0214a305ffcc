/**
 * Section Layout Styles
 * Common section layouts and spacing
 */

/* Section Layouts */

/* General section styling */
section {
  padding: 60px 0;
  border-radius: var(--border-radius);
}

.section-title {
  font-size: 28px;
  margin-bottom: 20px;
  color: var(--primary-dark);
  position: relative;
  display: inline-block;
  padding-bottom: 10px;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  border-radius: 2px;
}

/* Hero section */
.hero {
  padding: 120px 0 80px;
  text-align: center;
  position: relative;
  overflow: hidden;
  margin-bottom: 80px;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  perspective: 1000px;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    125deg,
    var(--primary-dark) 0%,
    var(--primary) 25%,
    var(--primary-light) 50%,
    var(--secondary) 75%,
    var(--primary-dark) 100%
  );
  background-size: 400% 400%;
  animation: gradientAnimation 15s ease infinite;
  z-index: 0;
  transform-style: preserve-3d;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at center,
    transparent 0%,
    rgba(0, 0, 0, 0.4) 100%
  );
  z-index: 1;
}

.hero-glow {
  position: absolute;
  width: 150%;
  height: 150%;
  top: -25%;
  left: -25%;
  background: radial-gradient(
    ellipse at center,
    rgba(67, 97, 238, 0.3) 0%,
    transparent 70%
  );
  opacity: 0.6;
  filter: blur(60px);
  animation: glow-pulse 8s ease-in-out infinite alternate;
  z-index: 1;
  pointer-events: none;
}

.hero-shape {
  position: absolute;
  border-radius: 50%;
  opacity: 0.2;
  filter: blur(40px);
  transform-style: preserve-3d;
  will-change: transform;
}

.hero-shape-1 {
  width: 300px;
  height: 300px;
  background: rgba(255, 255, 255, 0.3);
  top: -100px;
  right: 10%;
  animation: floatAnimation 8s ease-in-out infinite;
  transform: translateZ(20px);
}

.hero-shape-2 {
  width: 400px;
  height: 400px;
  background: rgba(255, 255, 255, 0.2);
  bottom: -150px;
  left: -100px;
  animation: floatAnimation 12s ease-in-out infinite reverse;
  transform: translateZ(40px);
}

.hero-shape-3 {
  width: 200px;
  height: 200px;
  background: rgba(255, 255, 255, 0.25);
  top: 30%;
  left: 10%;
  animation: floatAnimation 10s ease-in-out infinite 2s;
  transform: translateZ(30px);
}

.hero-shape-4 {
  width: 250px;
  height: 250px;
  background: rgba(67, 97, 238, 0.2);
  top: 60%;
  right: 15%;
  animation: floatAnimation 9s ease-in-out infinite 1s;
  transform: translateZ(25px);
}

.hero-shape-5 {
  width: 180px;
  height: 180px;
  background: rgba(255, 255, 255, 0.15);
  top: 20%;
  right: 25%;
  animation: floatAnimation 11s ease-in-out infinite 3s;
  transform: translateZ(15px);
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  transform-style: preserve-3d;
}

.hero-text-container {
  max-width: 800px;
  margin-bottom: 50px;
  transform: translateZ(50px);
}

.badge-container {
  margin-bottom: 24px;
}

.hero-badge {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: 8px 16px;
  border-radius: 30px;
  font-size: 0.9rem;
  font-weight: 500;
  color: white;
  letter-spacing: 1px;
  text-transform: uppercase;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: inline-block;
  transform: translateZ(5px);
  transition: all 0.3s ease;
}

.hero-badge:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateZ(10px) scale(1.05);
}

.hero-title {
  font-family: "Montserrat", sans-serif;
  font-size: 3.5rem;
  margin-bottom: 24px;
  text-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  color: white;
  font-weight: 800;
  line-height: 1.6;
  position: relative;
  transform: translateZ(10px);
}

.hero-title-separator {
  color: rgba(255, 255, 255, 0.8);
  margin: 0 10px;
  position: relative;
}

.hero-title-main {
  position: relative;
  display: inline-block;
}

.gradient-text {
  background: linear-gradient(90deg, #fff, #a5b4fc, #fff);
  background-size: 200% auto;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: textShine 3s linear infinite;
  text-shadow: none;
}

.hero-description {
  font-size: 1.3rem;
  margin-bottom: 35px;
  opacity: 0.9;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
  transform: translateZ(5px);
}

.cta-buttons {
  transform: translateZ(15px);
}

.btn-glow {
  position: relative;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.4);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.btn-glow:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(67, 97, 238, 0.6);
}

.btn-hover-effect {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.3) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none;
  mix-blend-mode: overlay;
}

.btn:hover .btn-hover-effect {
  opacity: 1;
}

/* Hero scroll indicator */
.hero-scroll-indicator {
  position: absolute;
  bottom: 25px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
}

.scroll-arrow {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  animation: bounce 2s infinite;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.scroll-arrow:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-5px);
}

.scroll-ripple {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  animation: ripple 2s infinite ease-out;
  z-index: -1;
}

/* Hero Stats Cards */
.hero-stats-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 28px; /* Increased gap for better breathing room */
  margin-top: 30px;
  width: 100%;
  max-width: 1100px; /* Slightly wider to accommodate better spacing */
  transform: translateZ(30px);
}

.stat-card {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-radius: 20px;
  padding: 28px 24px; /* Better internal padding */
  min-width: 180px;
  border: 1px solid rgba(255, 255, 255, 0.12);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15), 0 0 1px rgba(255, 255, 255, 0.2);
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  transform: translateY(20px);
  opacity: 0;
  animation: fadeInUp 0.8s forwards;
}

.stat-card:hover {
  transform: translateY(-10px) scale(1.05);
  background: rgba(255, 255, 255, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2), 0 0 15px rgba(255, 255, 255, 0.1);
}

.stat-icon {
  width: 64px;
  height: 64px;
  background: rgba(67, 97, 238, 0.2);
  border-radius: 16px; /* Squared with rounded corners for modern look */
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.6rem;
  color: white;
  position: relative;
  z-index: 2;
  transform: translateZ(10px);
  transition: all 0.4s ease;
  margin-bottom: 20px;
  box-shadow: 0 6px 20px rgba(67, 97, 238, 0.15);
}

.stat-card:hover .stat-icon {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  transform: translateZ(20px) scale(1.08);
  box-shadow: 0 8px 25px rgba(67, 97, 238, 0.4);
}

.icon-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: -1;
}

.stat-content {
  text-align: center;
  color: white;
  position: relative;
  z-index: 2;
  transform: translateZ(5px);
}

.stat-number {
  font-size: 2.8rem;
  font-weight: 700;
  margin-bottom: 8px;
  background: linear-gradient(90deg, #fff, #a5b4fc);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
  letter-spacing: -0.5px;
  position: relative;
}

.hero .stat-number{
  margin-right: 10px;
}

.stat-label {
  font-size: 0.92rem;
  opacity: 0.85;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.stat-decoration {
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--primary), transparent);
  opacity: 0.5;
  transform-origin: center;
  transition: all 0.5s ease;
}

.stat-card:hover .stat-decoration {
  opacity: 0.8;
  height: 3px;
  transform: scaleX(0.85);
}

.stat-glow {
  position: absolute;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: radial-gradient(
    circle,
    rgba(67, 97, 238, 0.25) 0%,
    transparent 70%
  );
  top: -30px;
  right: -30px;
  opacity: 0.4;
  filter: blur(20px);
  transition: all 0.8s ease;
  z-index: 0;
}

.stat-card:hover .stat-glow {
  width: 140px;
  height: 140px;
  opacity: 0.6;
  transform: translate(-10px, 10px);
}

/* Animation for stat cards */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes glow-pulse {
  0%,
  100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}

/* Section intro content */
section p {
  color: var(--text-light);
  line-height: 1.7;
}

/* Intro paragraphs */
.team-intro,
.roadmap-intro,
.doc-intro,
.sprint-intro,
.testimonials-intro,
.progress-intro,
.gallery-intro,
.contact-intro {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 30px;
  color: var(--text-light);
}

@media (max-width: 768px) {
  section {
    padding: 40px 0;
  }

  .hero {
    padding: 100px 0 60px;
    min-height: auto;
  }

  .hero h1 {
    font-size: 2.5rem;
  }

  .hero p {
    font-size: 1.1rem;
  }
}
