/**
 * Footer Layout Styles
 */

footer {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  padding: 40px 0 20px;
  position: relative;
  overflow: hidden;
  margin-top: 50px;
}

footer::before {
  content: "";
  position: absolute;
  bottom: -50%;
  left: -50%;
  width: 100%;
  height: 200%;
  background: rgba(255, 255, 255, 0.05);
  transform: rotate(45deg);
  pointer-events: none;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  position: relative;
  z-index: 1;
}

.footer-column {
  margin-bottom: 30px;
}

.footer-column h3 {
  font-size: 1.2rem;
  margin-bottom: 20px;
  position: relative;
  padding-bottom: 10px;
  display: inline-block;
}

.footer-column h3::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  background: white;
}

.footer-column p {
  margin-bottom: 15px;
  opacity: 0.8;
  line-height: 1.6;
}

.footer-links a {
  display: block;
  color: white;
  text-decoration: none;
  margin-bottom: 10px;
  opacity: 0.8;
  transition: var(--transition);
}

.footer-links a:hover {
  opacity: 1;
  transform: translateX(5px);
}

.footer-links a i {
  margin-right: 8px;
}

.copyright {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.9rem;
  opacity: 0.7;
  position: relative;
  z-index: 1;
}
