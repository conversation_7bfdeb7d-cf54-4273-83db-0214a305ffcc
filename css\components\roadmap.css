/*--------------------------------------------------------------
# Base Structure & Layout
--------------------------------------------------------------*/
.roadmap-section {
  /* Define component-specific custom properties */
  --icon-color: var(--primary, #4361ee);
  --delay: 0s;
  --tx: 20px;
  --ty: -30px;
  --tr: 10deg;
  --translateX: 20px;
  --translateY: -30px;
  --rotate: 10deg;
  --progress-value: 67%;

  position: relative;
  padding: 90px 0 70px;
  margin-bottom: 50px;
  background: var(--bg-light);
  background-image: linear-gradient(
    135deg,
    rgba(236, 245, 255, 0.9) 0%,
    rgba(220, 230, 250, 0.7) 50%,
    rgba(230, 225, 245, 0.8) 100%
  );
  overflow: hidden;
  animation: gradientShift 15s ease infinite alternate;
}

/* Enhanced background with subtle pattern */
.roadmap-section::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='30' height='30' viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15 0C6.716 0 0 6.716 0 15c0 8.284 6.716 15 15 15 8.284 0 15-6.716 15-15 0-8.284-6.716-15-15-15zm0 27.5c-6.904 0-12.5-5.596-12.5-12.5S8.096 2.5 15 2.5 27.5 8.096 27.5 15 21.904 27.5 15 27.5z' fill='%234a6cf7' fill-opacity='0.03' fill-rule='nonzero'/%3E%3C/svg%3E"),
    url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noise'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noise)' opacity='0.05'/%3E%3C/svg%3E"),
    linear-gradient(
      135deg,
      rgba(236, 245, 255, 0.9) 0%,
      rgba(220, 230, 250, 0.7) 50%,
      rgba(230, 225, 245, 0.8) 100%
    );
  opacity: 0.6;
  z-index: 0;
  pointer-events: none;
}

/* Decorative shapes */
.roadmap-section .decorative-shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-light), var(--primary));
  opacity: 0.1;
  z-index: 0;
  pointer-events: none;
  box-shadow: 0 0 40px rgba(67, 97, 238, 0.2);
}

.roadmap-section .shape-1 {
  width: 300px;
  height: 300px;
  top: -100px;
  right: -50px;
  animation: float-complex 20s ease-in-out infinite;
  backdrop-filter: blur(5px);
}

.roadmap-section .shape-2 {
  width: 200px;
  height: 200px;
  bottom: -50px;
  left: -50px;
  animation: float-complex 15s ease-in-out infinite reverse;
  backdrop-filter: blur(3px);
}

.roadmap-section .shape-3 {
  width: 150px;
  height: 150px;
  top: 40%;
  right: 15%;
  animation: float-complex 25s ease-in-out infinite 2s;
  backdrop-filter: blur(4px);
}

.roadmap-section .shape-4 {
  width: 120px;
  height: 120px;
  bottom: 20%;
  left: 20%;
  animation: float-complex 18s ease-in-out infinite 1s;
  opacity: 0.07;
}

/* Introduction text */
.roadmap-intro {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 35px;
  color: var(--text-dark);
  position: relative;
  z-index: 2;
  font-weight: 500;
}

/*--------------------------------------------------------------
# Icons & Floating Elements
--------------------------------------------------------------*/
.roadmap-icon {
  position: absolute;
  z-index: 5;
  color: var(--icon-color, var(--primary));
  opacity: 0;
  filter: drop-shadow(0 3px 5px rgba(0, 0, 0, 0.2));
  animation: icon-float 15s ease-in-out infinite;
  animation-delay: var(--delay, 0s);
  will-change: transform, opacity;
  transition: transform 0.3s ease;
  cursor: pointer;
}

.roadmap-icon::after {
  content: "";
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.roadmap-icon:hover::after {
  opacity: 1;
  animation: pulse 1.5s infinite;
}

.roadmap-icon:hover {
  transform: scale(1.5);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

/* Particle effects */
.explosion-particle {
  position: absolute;
  background-color: var(--icon-color, var(--primary));
  width: 5px;
  height: 5px;
  pointer-events: none;
  will-change: transform, opacity;
  transform-origin: center center;
  z-index: 10000 !important;
  mix-blend-mode: screen;
}

.particle-container {
  pointer-events: none;
  isolation: isolate;
  overflow: visible;
  z-index: 10000;
}

/*--------------------------------------------------------------
# Roadmap Summary
--------------------------------------------------------------*/
.roadmap-summary {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 50px;
  margin-bottom: 50px;
  position: relative;
  z-index: 2;
  padding: 30px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 30px;
  box-shadow: 0 20px 50px rgba(67, 97, 238, 0.12),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  overflow: hidden;
}

.roadmap-summary:hover {
  box-shadow: 0 25px 60px rgba(67, 97, 238, 0.2),
    0 0 0 2px rgba(255, 255, 255, 0.4);
  transform: translateY(-8px);
}

/* Particles container for background effects */
.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: -1;
  opacity: 0.6;
}

/*--------------------------------------------------------------
# Circular Progress
--------------------------------------------------------------*/
/* Main circular progress container */
.progress-circular {
  width: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px;
  border-radius: 24px;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 15px 35px rgba(67, 97, 238, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.5);
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  overflow: hidden;
  transform-style: preserve-3d;
  transform: perspective(1000px);
}

.progress-circular:hover {
  transform: perspective(1000px) translateY(-10px) scale(1.05);
  box-shadow: 0 25px 50px rgba(67, 97, 238, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.6);
}

/* Glow effect behind circular progress */
.progress-glow {
  position: absolute;
  width: 150px;
  height: 150px;
  border-radius: 50%;
  background: radial-gradient(
    circle,
    rgba(67, 97, 238, 0.4) 0%,
    rgba(67, 97, 238, 0.1) 40%,
    transparent 70%
  );
  top: 50%;
  left: 50%;
  transform: translate(-50%, -60%);
  filter: blur(15px);
  opacity: 0.7;
  z-index: 0;
  animation: glow-pulse 3s infinite alternate;
}

/* Shine effect */
.progress-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shine-sweep 4s infinite;
  pointer-events: none;
  z-index: 10;
}

/* Circular progress outer component */
.progress-circular-outer {
  position: relative;
  width: 150px;
  height: 150px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  filter: drop-shadow(0 10px 15px rgba(67, 97, 238, 0.3));
  z-index: 1;
  transform-style: preserve-3d;
  transform: translateZ(10px);
  --progress-value: 67%;
  background: conic-gradient(
    from 0deg,
    var(--primary) 0%,
    var(--primary-light) 20%,
    var(--secondary) 40%,
    var(--primary) var(--progress-value),
    transparent var(--progress-value)
  );
  animation: progress-fill 2s cubic-bezier(0.34, 1.56, 0.64, 1) forwards,
    rotate-slow 15s linear infinite;
}

/* Inner circle with 3D effect - add counter-rotation to stabilize content */
.progress-circular-inner {
  width: calc(100% - 20px);
  height: calc(100% - 20px);
  background: linear-gradient(145deg, #ffffff, #f5f7ff);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: inset 0 2px 5px rgba(255, 255, 255, 1),
    inset 0 -2px 5px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  z-index: 3;
  transform: translateZ(5px);
  /* Add counter-rotation to keep text stable */
  animation: counter-rotate-slow 15s linear infinite;
}

/* Percentage text styling */
.progress-circular-text {
  font-size: 42px;
  font-weight: 800;
  background: linear-gradient(135deg, var(--primary-dark), var(--secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  margin-bottom: 5px;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transform: translateZ(5px);
  letter-spacing: -1px;
}

/* Animated dots */
.progress-circular-dots {
  display: flex;
  gap: 5px;
  margin-top: -5px;
}

.dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--primary);
  opacity: 0.7;
}

.dot-1 {
  animation: dot-pulse 1.5s infinite 0s;
}

.dot-2 {
  animation: dot-pulse 1.5s infinite 0.3s;
}

.dot-3 {
  animation: dot-pulse 1.5s infinite 0.6s;
}

.progress-label {
  font-size: 1rem;
  color: var(--text-dark);
  margin-top: 18px;
  text-align: center;
  font-weight: 600;
  background: linear-gradient(135deg, var(--primary-dark), var(--primary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(10px);
  animation: slide-up 1s ease forwards 1.5s;
}

/* Dark mode support */
body.dark-mode .progress-circular-outer {
  background: conic-gradient(
    from 0deg,
    #4f46e5 0%,
    #6366f1 20%,
    #4f46e5 40%,
    #4f46e5 var(--progress-value, 67%),
    rgba(255, 255, 255, 0.08) var(--progress-value, 67%)
  );
}

body.dark-mode .progress-circular-inner {
  background-color: var(--bg-dark);
}

/*--------------------------------------------------------------
# Stats Section
--------------------------------------------------------------*/
.roadmap-stats {
  display: flex;
  justify-content: center;
  gap: 40px;
  margin: 20px 0 0;
  flex-wrap: wrap;
  position: relative;
  z-index: 2;
}

.stat-item {
  text-align: center;
  position: relative;
  padding: 25px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20px;
  min-width: 180px;
  box-shadow: 0 15px 35px rgba(67, 97, 238, 0.12),
    0 0 0 1px rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.7);
  overflow: hidden;
  transform-style: preserve-3d;
  transform: perspective(1000px);
}

.stat-item:hover {
  transform: perspective(1000px) translateY(-10px) scale(1.03);
  box-shadow: 0 25px 50px rgba(67, 97, 238, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.6);
  background: white;
}

/* Glow effect for stats */
.stat-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle at 50% 30%,
    rgba(67, 97, 238, 0.15) 0%,
    transparent 70%
  );
  opacity: 0.5;
  z-index: -1;
  transition: opacity 0.5s ease;
}

.stat-item:hover .stat-glow {
  opacity: 0.8;
  animation: glow-shift 3s infinite alternate;
}

/* Metric icons with particle effects */
.metric-icon {
  margin-bottom: 20px;
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-light), var(--primary));
  color: white;
  font-size: 1.6rem;
  box-shadow: 0 10px 25px rgba(67, 97, 238, 0.25);
  position: relative;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  transform: translateZ(20px);
}

.icon-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-item:hover .icon-particles {
  opacity: 1;
}

.metric-icon::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0.9) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: opacity 0.5s ease;
}

.stat-item:hover .metric-icon::before {
  opacity: 0.6;
  animation: pulse-fade 2s infinite;
}

.stat-item:hover .metric-icon {
  transform: translateZ(30px) scale(1.1) rotate(5deg);
  box-shadow: 0 15px 35px rgba(67, 97, 238, 0.35);
}

/* Stat content typography */
.stat-content {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 10px;
  transform: translateZ(15px);
}

.roadmap-stats .metric-value {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, var(--primary-dark), var(--secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  letter-spacing: -1px;
  line-height: 1;
}

.metric-separator {
  font-size: 2rem;
  font-weight: 300;
  color: var(--text-light);
  margin: 0 5px;
}

.metric-total {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--text-light);
}

.milestone {
  font-size: 1rem;
  color: var(--text-dark);
  font-weight: 600;
  transition: color 0.3s ease;
  margin-bottom: 15px;
  transform: translateZ(10px);
}

/* Progress bar for stats */
.stat-progress {
  width: 100%;
  margin-top: 5px;
  transform: translateZ(10px);
}

.stat-progress-bar {
  height: 6px;
  width: 100%;
  background: rgba(230, 230, 230, 0.5);
  border-radius: 10px;
  overflow: hidden;
}

.stat-progress-fill {
  height: 100%;
  background: linear-gradient(to right, var(--primary-light), var(--primary));
  border-radius: 10px;
  transform-origin: left;
  transform: scaleX(0);
  animation: progress-animate 1.5s cubic-bezier(0.26, 1.36, 0.74, 1) forwards
    0.5s;
}

/* Badge for upcoming items */
.stat-badge {
  position: absolute;
  bottom: 15px;
  right: 15px;
  padding: 5px 10px;
  background: linear-gradient(135deg, var(--primary-light), var(--primary));
  color: white;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.2);
  transform: translateZ(20px);
}

.pulse-badge {
  animation: badge-pulse 2s infinite;
}

/*--------------------------------------------------------------
# Navigation Controls
--------------------------------------------------------------*/
/* View toggle buttons */
.roadmap-view-toggle {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 20px;
}

.view-toggle {
  background: transparent;
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: var(--text-light);
  padding: 8px 16px;
  border-radius: 30px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s;
}

.view-toggle.active,
.view-toggle:hover {
  background: white;
  color: var(--primary);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

/* Filters */
.roadmap-filters {
  display: flex;
  justify-content: center;
  margin-bottom: 25px;
  flex-wrap: wrap;
  gap: 12px;
  position: relative;
  z-index: 2;
}

.roadmap-filter {
  background: transparent;
  border: 1px solid var(--primary);
  color: var(--primary);
  padding: 10px 20px;
  border-radius: 30px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  gap: 8px;
}

.roadmap-filter.active,
.roadmap-filter:hover {
  background: var(--primary);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(67, 97, 238, 0.2);
}

/* View containers */
.roadmap-container {
  position: relative;
  max-width: 1000px;
  margin: 0 auto;
  padding: 50px 0;
  z-index: 2;
  display: none;
}

.roadmap-container.active {
  display: block;
}

/*--------------------------------------------------------------
# Timeline View
--------------------------------------------------------------*/
.timeline-view {
  position: relative;
}

.roadmap-timeline-wrapper {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 0;
  bottom: 0;
  width: 8px;
  overflow: hidden;
}

.roadmap-line {
  position: absolute;
  width: 100%;
  top: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.5),
    rgba(230, 230, 230, 0.5) 20%,
    rgba(200, 200, 200, 0.5) 40%,
    rgba(200, 200, 200, 0.5) 60%,
    rgba(230, 230, 230, 0.5) 80%,
    rgba(255, 255, 255, 0.5)
  );
  border-radius: 4px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.roadmap-progress-line {
  position: absolute;
  width: 100%;
  top: 0;
  background: linear-gradient(to bottom, var(--primary-light), var(--primary));
  border-radius: 4px;
  box-shadow: 0 1px 6px rgba(67, 97, 238, 0.3);
  transition: height 1.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Roadmap items */
.roadmap-item {
  display: flex;
  margin-bottom: 80px;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.roadmap-item.visible {
  opacity: 1;
  transform: translateY(0);
}

.roadmap-item:last-child {
  margin-bottom: 20px;
}

.roadmap-item:nth-child(odd) {
  justify-content: flex-start;
  padding-right: 52%;
  padding-left: 30px;
}

.roadmap-item:nth-child(even) {
  justify-content: flex-end;
  padding-left: 52%;
  padding-right: 30px;
  flex-direction: row-reverse;
}

/* Markers */
.roadmap-marker {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 26px;
  height: 26px;
  border-radius: 50%;
  background: white;
  border: 4px solid var(--primary);
  z-index: 3;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 3px 10px rgba(67, 97, 238, 0.25);
}

.roadmap-marker::before {
  content: "";
  position: absolute;
  top: 50%;
  width: 22px;
  height: 3px;
  background: linear-gradient(to right, var(--primary-light), var(--primary));
  z-index: 0;
}

.roadmap-item:nth-child(odd) .roadmap-marker::before {
  right: 22px;
  transform: translateY(-50%);
}

.roadmap-item:nth-child(even) .roadmap-marker::before {
  left: 22px;
  transform: translateY(-50%);
}

.roadmap-marker:hover {
  transform: translateX(-50%) scale(1.2);
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.4);
}

/* Dates */
.roadmap-date {
  position: absolute;
  top: 0;
  display: inline-block;
  padding: 8px 18px;
  background: linear-gradient(135deg, var(--primary-light), var(--primary));
  color: white;
  border-radius: 30px;
  font-size: 0.95rem;
  font-weight: 500;
  z-index: 5;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.2);
  transition: all 0.3s ease;
  white-space: nowrap;
  transform: translateY(-50%);
}

.roadmap-date:hover {
  transform: translateY(-60%);
  box-shadow: 0 8px 20px rgba(67, 97, 238, 0.3);
}

.roadmap-item:nth-child(odd) .roadmap-date {
  right: calc(52% + 30px);
}

.roadmap-item:nth-child(even) .roadmap-date {
  left: calc(52% + 30px);
}

/* Content cards */
.roadmap-content {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  width: 100%;
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  border-top: 5px solid transparent;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(5px);
}

.roadmap-content:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(67, 97, 238, 0.15);
  border-top-color: var(--primary);
}

.roadmap-content::before {
  content: "";
  position: absolute;
  top: -5px;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(to right, var(--primary-light), var(--primary));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.roadmap-content:hover::before {
  opacity: 1;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
}

.roadmap-content h3 {
  margin: 0;
  font-size: 1.3rem;
  color: var(--text-dark);
  position: relative;
  display: inline-block;
}

.roadmap-content h3::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 40px;
  height: 3px;
  background: var(--primary);
  transition: width 0.3s ease;
}

.roadmap-content:hover h3::after {
  width: 100%;
}

.roadmap-content p {
  color: var(--text-light);
  margin-bottom: 18px;
  line-height: 1.6;
}

/* Status badges */
.roadmap-status {
  display: inline-block;
  padding: 6px 14px;
  border-radius: 30px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 5px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.roadmap-status::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 30px;
  opacity: 0.15;
  z-index: -1;
}

.roadmap-complete .roadmap-status {
  color: var(--success);
}

.roadmap-complete .roadmap-status::before {
  background: var(--success);
}

.roadmap-current .roadmap-status {
  color: var(--primary);
}

.roadmap-current .roadmap-status::before {
  background: var(--primary);
}

.roadmap-item:not(.roadmap-complete):not(.roadmap-current) .roadmap-status {
  color: var(--text-light);
}

.roadmap-item:not(.roadmap-complete):not(.roadmap-current)
  .roadmap-status::before {
  background: var(--text-light);
}

.roadmap-current .roadmap-marker {
  background: var(--primary);
  box-shadow: 0 0 0 4px rgba(67, 97, 238, 0.3), 0 3px 10px rgba(0, 0, 0, 0.2);
  animation: milestone-pulse 2s infinite;
}

/* Progress indicators */
.roadmap-progress {
  margin-bottom: 16px;
}

.mini-progress {
  height: 8px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 6px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.07);
}

.mini-bar {
  height: 100%;
  background: linear-gradient(to right, var(--primary-light), var(--primary));
  border-radius: 6px;
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 1s cubic-bezier(0.25, 1, 0.5, 1);
  box-shadow: 0 0 10px rgba(67, 97, 238, 0.3);
}

/* Expandable details */
.roadmap-details {
  display: flex;
  gap: 18px;
  flex-wrap: wrap;
  font-size: 0.9rem;
  color: var(--text-light);
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.detail-item {
  display: flex;
  align-items: center;
  transition: transform 0.3s ease;
}

.detail-item:hover {
  transform: translateY(-2px);
  color: var(--text-dark);
}

.detail-item i {
  margin-right: 8px;
  color: var(--primary);
}

.expand-details {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  background: transparent;
  border: none;
  padding: 10px;
  margin-top: 10px;
  color: var(--primary);
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.expand-details:hover {
  color: var(--primary-dark);
}

.expand-details i {
  transition: transform 0.3s ease;
}

.expand-details.active i {
  transform: rotate(180deg);
}

.expanded-content {
  max-height: 0;
  overflow: hidden;
  opacity: 0;
  transition: all 0.4s ease;
  margin-top: 0;
}

.expanded-content.active {
  max-height: 500px;
  opacity: 1;
  margin-top: 20px;
}

.expanded-content h4 {
  color: var(--text-dark);
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.achievement-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.achievement-list li {
  margin-bottom: 10px;
  padding-left: 25px;
  position: relative;
  color: var(--text-light);
}

.achievement-list li i {
  position: absolute;
  left: 0;
  color: var(--success);
}

/*--------------------------------------------------------------
# Cards View
--------------------------------------------------------------*/
.cards-view {
  display: none;
}

.cards-view.active {
  display: block;
}

.roadmap-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
}

.roadmap-card {
  background: white;
  border-radius: 16px;
  padding: 28px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.07);
  position: relative;
  overflow: hidden;
  transition: all 0.4s ease;
  opacity: 0;
  transform: translateY(30px);
  backdrop-filter: blur(5px);
}

.roadmap-card.visible {
  opacity: 1;
  transform: translateY(0);
}

.roadmap-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(67, 97, 238, 0.15);
}

.card-status-indicator {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(
    to right,
    rgba(200, 200, 200, 0.3),
    rgba(150, 150, 150, 0.3)
  );
}

.roadmap-complete .card-status-indicator {
  background: linear-gradient(
    to right,
    var(--success),
    rgba(76, 217, 100, 0.7)
  );
}

.roadmap-current .card-status-indicator {
  background: linear-gradient(to right, var(--primary-light), var(--primary));
  box-shadow: 0 2px 10px rgba(67, 97, 238, 0.2);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
}

.card-date {
  font-size: 0.95rem;
  color: var(--text-light);
  font-weight: 500;
}

.roadmap-card h3 {
  margin: 0 0 15px 0;
  font-size: 1.25rem;
  color: var(--text-dark);
  position: relative;
  display: inline-block;
}

.roadmap-card h3::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 40px;
  height: 3px;
  background: var(--primary);
  transition: width 0.3s ease;
}

.roadmap-card:hover h3::after {
  width: 100%;
}

.view-card-details {
  margin-top: 18px;
  background: transparent;
  border: 1px solid var(--primary);
  color: var(--primary);
  padding: 10px 18px;
  border-radius: 30px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.view-card-details::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 0;
  background: linear-gradient(to right, var(--primary-light), var(--primary));
  transition: width 0.3s ease;
  z-index: -1;
}

.view-card-details:hover {
  color: white;
  border-color: transparent;
}

.view-card-details:hover::before {
  width: 100%;
}

/*--------------------------------------------------------------
# Legend
--------------------------------------------------------------*/
.roadmap-legend {
  display: flex;
  justify-content: center;
  margin-top: 40px;
  gap: 25px;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 0.95rem;
  color: var(--text-light);
  padding: 8px 16px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.7);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  z-index: 2;
}

.legend-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  background: white;
}

.legend-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.legend-dot.completed {
  background-color: var(--success);
}

.legend-dot.current {
  background-color: var(--primary);
}

.legend-dot.upcoming {
  background-color: var(--text-light);
}

/*--------------------------------------------------------------
# Modal
--------------------------------------------------------------*/
.roadmap-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease;
  pointer-events: auto;
}

.roadmap-modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

.roadmap-modal {
  background: white;
  border-radius: 12px;
  padding: 30px;
  max-width: 600px;
  width: 90%;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
  transform: translateY(20px) scale(0.95);
  transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  overflow: hidden;
}

.roadmap-modal-overlay.active .roadmap-modal {
  transform: translateY(0) scale(1);
}

.roadmap-modal::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: var(--text-light);
}

.roadmap-modal.complete::before {
  background: var(--success);
}

.roadmap-modal.current::before {
  background: var(--primary);
}

.roadmap-modal.upcoming::before {
  background: var(--warning);
}

.modal-close {
  position: absolute;
  top: 15px;
  right: 15px;
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #888;
  transition: color 0.3s;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.modal-close:hover {
  color: #333;
  background: rgba(0, 0, 0, 0.05);
}

.modal-content {
  margin-top: 10px;
}

.modal-content h3 {
  color: var(--text-dark);
  margin-bottom: 15px;
  font-size: 1.4rem;
}

.modal-content p {
  color: var(--text-light);
  line-height: 1.6;
  margin-bottom: 15px;
}

.modal-content ul {
  list-style-type: none;
  padding: 0;
  margin: 20px 0;
}

.modal-content ul li {
  padding: 8px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  color: var(--text-light);
  position: relative;
  padding-left: 25px;
}

.modal-content ul li:before {
  content: "\f00c";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  position: absolute;
  left: 0;
  color: var(--success);
}

.progress-indicator {
  margin-top: 20px;
}

.progress-bar {
  height: 8px;
  background: var(--primary);
  border-radius: 4px;
  margin-bottom: 8px;
  animation: progress-grow 1.5s ease-out forwards;
  transform-origin: left;
}

/*--------------------------------------------------------------
# Animations
--------------------------------------------------------------*/
@keyframes gradientShift {
  0% {
    background-position: 0 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}

@keyframes float-complex {
  0% {
    transform: translatey(0) rotate(0deg) scale(1);
  }
  25% {
    transform: translatey(-20px) rotate(3deg) scale(1.02);
  }
  50% {
    transform: translatey(5px) rotate(5deg) scale(0.98);
  }
  75% {
    transform: translatey(-10px) rotate(2deg) scale(1.01);
  }
  100% {
    transform: translatey(0) rotate(0deg) scale(1);
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.7;
  }
}

@keyframes particle-fallback {
  0% {
    transform: translate(0, 0) scale(1) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: translate(var(--tx, 0), var(--ty, 0)) scale(0.5)
      rotate(var(--tr, 180deg));
    opacity: 0.5;
  }
  100% {
    transform: translate(calc(var(--tx, 0) * 2), calc(var(--ty, 0) * 2))
      scale(0) rotate(calc(var(--tr, 180deg) * 2));
    opacity: 0;
  }
}

@keyframes icon-float {
  0%,
  100% {
    transform: translate(0, 0) rotate(0deg);
    opacity: 0;
  }
  10%,
  90% {
    opacity: 0.7;
  }
  50% {
    transform: translate(var(--translateX, 20px), var(--translateY, -30px))
      rotate(var(--rotate, 10deg));
    opacity: 0.9;
  }
}

@keyframes float {
  0% {
    transform: translatey(0) rotate(0deg);
  }
  50% {
    transform: translatey(-20px) rotate(5deg);
  }
  100% {
    transform: translatey(0) rotate(0deg);
  }
}

@keyframes progress-fill {
  0% {
    clip-path: circle(0% at center);
    opacity: 0.5;
  }
  100% {
    clip-path: circle(100% at center);
    opacity: 1;
  }
}

@keyframes rotate-slow {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Add counter-rotation animation to keep text stable */
@keyframes counter-rotate-slow {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(-360deg);
  }
}

@keyframes rotate-reflection {
  0% {
    transform: rotate(-45deg);
  }
  100% {
    transform: rotate(315deg);
  }
}

@keyframes dot-pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.5);
    opacity: 1;
  }
}

@keyframes glow-pulse {
  0% {
    opacity: 0.5;
    transform: scale(0.95);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
  100% {
    opacity: 0.5;
    transform: scale(0.95);
  }
}

@keyframes glow-shift {
  0% {
    background-position: 0 50%;
    opacity: 0.5;
  }
  50% {
    background-position: 100% 50%;
    opacity: 0.8;
  }
  100% {
    background-position: 0 50%;
    opacity: 0.5;
  }
}

@keyframes shine-sweep {
  0% {
    left: -100%;
    opacity: 0;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}

@keyframes progress-animate {
  0% {
    transform: scaleX(0);
  }
  100% {
    transform: scaleX(1);
  }
}

@keyframes pulse-fade {
  0%,
  100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

@keyframes badge-pulse {
  0%,
  100% {
    transform: translateZ(20px) scale(1);
    box-shadow: 0 5px 15px rgba(67, 97, 238, 0.2);
  }
  50% {
    transform: translateZ(20px) scale(1.1);
    box-shadow: 0 8px 20px rgba(67, 97, 238, 0.4);
  }
}

@keyframes number-celebrate {
  0% {
    transform: scale(1);
    text-shadow: 0 0 0 rgba(67, 97, 238, 0);
  }
  50% {
    transform: scale(1.2);
    text-shadow: 0 0 20px rgba(67, 97, 238, 0.5);
  }
  100% {
    transform: scale(1);
    text-shadow: 0 0 0 rgba(67, 97, 238, 0);
  }
}

@keyframes slide-up {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes milestone-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.6), 0 3px 10px rgba(0, 0, 0, 0.2);
  }
  70% {
    box-shadow: 0 0 0 12px rgba(67, 97, 238, 0), 0 3px 10px rgba(0, 0, 0, 0.2);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0), 0 3px 10px rgba(0, 0, 0, 0.2);
  }
}

@keyframes progress-grow {
  from {
    transform: scaleX(0);
  }
  to {
    transform: scaleX(1);
  }
}

@keyframes progress-circle {
  0% {
    background: conic-gradient(
      from 0deg,
      var(--primary) 0%,
      var(--primary-light) 0%,
      var(--primary) 0%,
      rgba(0, 0, 0, 0.05) 0%
    );
  }
  100% {
    background: conic-gradient(
      from 0deg,
      var(--primary) 0%,
      var(--primary-light) 20%,
      var(--primary) 40%,
      var(--primary) var(--progress-value, 67%),
      rgba(0, 0, 0, 0.05) var(--progress-value, 67%)
    );
  }
}

/*--------------------------------------------------------------
# Responsive Styles
--------------------------------------------------------------*/
@media (max-width: 992px) {
  .roadmap-summary {
    flex-direction: column;
    align-items: center;
  }

  .stat-item:not(:last-child)::after {
    display: none;
  }

  .roadmap-item {
    margin-bottom: 60px;
  }

  .roadmap-content {
    padding: 25px;
  }
}

@media (max-width: 768px) {
  /* Timeline adjustments */
  .roadmap-timeline-wrapper {
    left: 30px;
    transform: none;
  }

  .roadmap-item {
    padding-left: 60px !important;
    padding-right: 0 !important;
    justify-content: flex-start !important;
    flex-direction: column !important;
    margin-bottom: 50px;
  }

  .roadmap-marker {
    left: 30px;
    transform: none;
    margin-bottom: 10px;
  }

  .roadmap-marker:hover {
    transform: scale(1.2);
  }

  /* Date adjustments */
  .roadmap-date {
    position: relative;
    top: auto;
    left: auto !important;
    right: auto !important;
    margin-bottom: 10px;
    margin-top: -5px;
    padding: 6px 15px;
    font-size: 0.9rem;
    display: inline-block;
    transform: translateY(0) !important;
    white-space: normal;
    max-width: 100%;
    text-align: center;
  }

  .roadmap-date:hover {
    transform: translateY(-3px) !important;
  }

  /* Content adjustments */
  .roadmap-content {
    width: 100%;
    padding: 20px;
  }

  .roadmap-stats {
    gap: 20px;
  }

  .stat-item:not(:last-child)::after {
    display: none;
  }

  .content-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .roadmap-details {
    flex-wrap: wrap;
    gap: 8px;
  }

  .expanded-content {
    padding: 10px 0;
  }

  .achievement-list li {
    padding: 5px 0;
  }

  .roadmap-progress {
    margin: 10px 0;
  }

  .expand-details {
    padding: 8px 12px;
    font-size: 0.9rem;
  }

  /* Modal adjustments */
  .roadmap-modal {
    padding: 20px;
    width: 95%;
  }
}

@media (max-width: 480px) {
  .roadmap-timeline-wrapper {
    left: 20px;
  }

  .roadmap-item {
    padding-left: 40px !important;
    margin-bottom: 40px;
  }

  .roadmap-marker {
    left: 20px;
    width: 20px;
    height: 20px;
  }

  .roadmap-date {
    font-size: 0.8rem;
    padding: 4px 12px;
    margin-bottom: 8px;
    margin-top: -3px;
  }

  .roadmap-content {
    padding: 15px;
  }

  .roadmap-content h3 {
    font-size: 1.1rem;
  }

  .roadmap-status {
    padding: 4px 10px;
    font-size: 0.8rem;
  }

  .roadmap-details {
    flex-wrap: wrap;
    gap: 5px;
  }

  .detail-item {
    font-size: 0.8rem;
  }

  .expand-details {
    padding: 6px 10px;
    font-size: 0.8rem;
  }

  .expanded-content h4 {
    font-size: 1rem;
    margin-bottom: 8px;
  }

  .achievement-list li {
    padding: 4px 0;
    font-size: 0.9rem;
  }
}
