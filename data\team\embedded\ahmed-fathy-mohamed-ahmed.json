{"id": "ahmed-fathy-mohamed-ahmed", "name": "<PERSON>", "role": "Embedded Systems Engineer", "team": "embedded", "contact": {"phone": "+201554007699", "email": "<EMAIL>"}, "bio": "<PERSON> is a skilled embedded systems engineer with expertise in hardware-software integration. He specializes in developing efficient firmware solutions for microcontroller-based systems.", "education": ["B.S. Computer Engineering, Cairo University, 2023", "Embedded Systems Professional Certification, 2022"], "skills": [{"name": "C/C++", "level": 90}, {"name": "Microcontrollers", "level": 85}, {"name": "PCB Design", "level": 75}, {"name": "RTOS", "level": 80}, {"name": "IoT Protocols", "level": 70}], "projects": [{"name": "Sensor Integration System", "description": "Developed a comprehensive sensor integration system for environmental monitoring with real-time data processing capabilities."}, {"name": "Firmware Optimization", "description": "Improved system performance by optimizing firmware code, resulting in 30% reduction in power consumption."}], "social": {"github": "https://github.com/ahmedfathy21", "linkedin": "https://www.linkedin.com/in/ahmed-fathy0/", "twitter": "", "instagram": "", "facebook": ""}}