/* Utility styles */

/* Improve animation smoothness */
.member-card:hover,
.team-tab:hover,
.btn:hover,
.feature-card:hover {
    will-change: transform;
}

/* Enhanced focus styles for accessibility */
.btn:focus,
.team-tab:focus,
.gallery-tab:focus,
.toc-item:focus,
.close-btn:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

/* Common animation keyframes */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes fillAnimation {
    to {
        transform: scaleX(1);
    }
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

@keyframes bounce {
    0%,
    100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-5px);
    }
}

/* Print styles for better document printing */
@media print {
    .toc-container,
    .theme-toggle,
    header nav,
    footer {
        display: none !important;
    }

    .container {
        width: 100%;
        max-width: none;
        margin: 0;
        padding: 0;
    }

    body {
        background-color: white;
        color: black;
    }

    .project-overview,
    .team-content,
    .documentation-section,
    .feature-card {
        box-shadow: none;
        break-inside: avoid;
        page-break-inside: avoid;
    }

    a {
        color: black;
    }
}

/* Common responsive adjustments */
@media (max-width: 768px) {
    /* Responsive adjustments for smaller screens */
}
