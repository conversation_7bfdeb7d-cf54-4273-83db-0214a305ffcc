/**
 * Enhanced Progress Visualization
 * Adds advanced animations and interactive elements to the progress section
 */
function enhanceProgressVisualization() {
  // Get the progress section
  const progressSection = document.getElementById("progress");

  if (!progressSection) {
    console.error("Progress section not found");
    return;
  }

  // Initialize circular progress animations
  initCircularProgress();

  // Initialize velocity rings
  initVelocityRings();

  // Add animation trigger for linear progress bars
  initProgressBars();

  // Add AOS-like animations if AOS is not available
  initScrollAnimations();
}

/**
 * Initialize circular progress animations
 */
function initCircularProgress() {
  const circularProgress = document.querySelector(".circular-progress");
  if (!circularProgress) return;

  const circle = document.querySelector(".progress-ring-circle");
  if (!circle) return;

  const percentage = parseInt(
    circularProgress.getAttribute("data-percentage") || "0"
  );
  const radius = circle.getAttribute("r");
  const circumference = 2 * Math.PI * radius;

  // Set the stroke-dasharray to the circumference
  circle.style.strokeDasharray = `${circumference} ${circumference}`;

  // Initially set to full circumference (no progress)
  circle.style.strokeDashoffset = circumference;

  // Adjust SVG viewBox for mobile responsiveness
  adjustCircularProgressForMobile();

  // Create an observer to trigger animation when in view
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          // Calculate the offset based on the percentage
          const offset = circumference - (percentage / 100) * circumference;

          // Animate the progress after a small delay
          setTimeout(() => {
            circle.style.strokeDashoffset = offset;

            // Animate the percentage counter
            animateCounter(
              document.querySelector(".percentage-value"),
              percentage
            );
          }, 300);

          observer.unobserve(entry.target);
        }
      });
    },
    { threshold: 0.2 }
  );

  observer.observe(circularProgress);
}

/**
 * Adjust circular progress SVG for mobile screens
 */
function adjustCircularProgressForMobile() {
  const progressRing = document.querySelector(".progress-ring");
  if (!progressRing) return;

  // Function to update SVG size based on screen width
  const updateSVGSize = () => {
    if (window.innerWidth <= 576) {
      // For extra small screens
      progressRing.setAttribute("width", "150");
      progressRing.setAttribute("height", "150");
    } else if (window.innerWidth <= 768) {
      // For small screens
      progressRing.setAttribute("width", "180");
      progressRing.setAttribute("height", "180");
    } else {
      // Reset to default for larger screens
      progressRing.setAttribute("width", "200");
      progressRing.setAttribute("height", "200");
    }
  };

  // Initial call
  updateSVGSize();

  // Update on resize
  window.addEventListener("resize", updateSVGSize);
}

/**
 * Initialize velocity ring animations
 */
function initVelocityRings() {
  const velocityRings = document.querySelectorAll(".velocity-ring-fill");

  velocityRings.forEach((ring) => {
    const circle = ring;
    const valueElement = ring
      .closest(".velocity-circle")
      .querySelector(".velocity-value");
    const percentageText = valueElement.textContent;
    const percentage = parseInt(percentageText);

    if (isNaN(percentage)) return;

    const radius = circle.getAttribute("r");
    const circumference = 2 * Math.PI * radius;

    // Set the stroke-dasharray to the circumference
    circle.style.strokeDasharray = `${circumference} ${circumference}`;

    // Initially set to full circumference (no progress)
    circle.style.strokeDashoffset = circumference;

    // Create an observer to trigger animation when in view
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // Calculate the offset based on the percentage
            const offset = circumference - (percentage / 100) * circumference;

            // Animate the progress after a small delay
            setTimeout(() => {
              circle.style.strokeDashoffset = offset;

              // Animate the percentage counter
              animateCounter(valueElement, percentage);
            }, 300);

            observer.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.2 }
    );

    observer.observe(ring.closest(".velocity-circle"));
  });
}

/**
 * Initialize progress bar animations
 */
function initProgressBars() {
  // Add animation trigger for chart fills and mini bars
  const progressBars = document.querySelectorAll(".chart-fill, .mini-bar");

  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          requestAnimationFrame(() => {
            entry.target.style.transform = "scaleX(1)";
          });
          observer.unobserve(entry.target);
        }
      });
    },
    { threshold: 0.2 }
  );

  progressBars.forEach((bar) => observer.observe(bar));
}

/**
 * Initialize scroll-based animations (similar to AOS)
 */
function initScrollAnimations() {
  // Get all elements with data-aos attribute
  const animatedElements = document.querySelectorAll("[data-aos]");

  if (animatedElements.length === 0) return;

  // Check if AOS is already loaded
  if (typeof AOS !== "undefined") return;

  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("aos-animate");
          observer.unobserve(entry.target);
        }
      });
    },
    { threshold: 0.1 }
  );

  animatedElements.forEach((element) => {
    observer.observe(element);
  });
}

/**
 * Animate counter from 0 to target value
 * @param {HTMLElement} element - The element to update
 * @param {number} targetValue - The target value to count to
 */
function animateCounter(element, targetValue) {
  if (!element) return;

  const duration = 2000; // 2 seconds
  const step = (timestamp) => {
    if (!startTime) startTime = timestamp;
    const progress = Math.min((timestamp - startTime) / duration, 1);
    const currentValue = Math.floor(progress * targetValue);

    element.textContent = `${currentValue}`;

    if (progress < 1) {
      window.requestAnimationFrame(step);
    } else {
      element.textContent = `${targetValue}`;
    }
  };

  let startTime = null;
  window.requestAnimationFrame(step);
}

// Run the function when the DOM is fully loaded
document.addEventListener("DOMContentLoaded", function () {
  enhanceProgressVisualization();
});
