/**
 * Header Layout Styles
 */

header {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  padding: 20px 0;
  position: relative;
  overflow: hidden;
}

header::before {
  content: "";
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 200%;
  background: rgba(255, 255, 255, 0.1);
  transform: rotate(45deg);
  pointer-events: none;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo {
  font-family: "Montserrat", sans-serif;
  font-size: 28px;
  font-weight: 800;
  background: linear-gradient(90deg, #fff, #e0e0e0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-center {
  flex-grow: 1;
  text-align: center;
}

nav ul {
  display: flex;
  list-style: none;
  justify-content: center;
}

nav ul li {
  margin-left: 30px;
}

nav ul li a {
  color: white;
  text-decoration: none;
  font-weight: 500;
  position: relative;
  padding-bottom: 5px;
  transition: var(--transition);
}

nav ul li a::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: white;
  transition: var(--transition);
}

nav ul li a:hover::after {
  width: 100%;
}

.header-right {
  display: flex;
  align-items: center;
}

/* Theme Toggle */
.theme-toggle {
  margin-left: 20px;
  position: relative;
  width: 65px;
  height: 32px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 6px;
  position: relative;
  cursor: pointer;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transform-style: preserve-3d;
  perspective: 500px;
}

.theme-toggle::before {
  content: "";
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.4) 0%,
    rgba(255, 255, 255, 0) 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
  border-radius: 35px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.theme-toggle:hover::before {
  opacity: 1;
}

.theme-toggle i {
  color: white;
  font-size: 15px;
  z-index: 1;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.toggle-ball {
  position: absolute;
  width: 24px;
  height: 24px;
  background: white;
  border-radius: 50%;
  left: 3px;
  transition: transform 0.3s ease;
  z-index: 1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
  }

  nav ul {
    margin-top: 15px;
    flex-wrap: wrap;
  }

  nav ul li {
    margin-left: 0;
    margin-right: 20px;
    margin-bottom: 10px;
  }
}
