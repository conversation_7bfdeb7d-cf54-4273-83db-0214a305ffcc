/**
 * Helper Utility Classes
 */

/* Display utilities */
.d-block {
  display: block;
}
.d-inline {
  display: inline;
}
.d-inline-block {
  display: inline-block;
}
.d-flex {
  display: flex;
}
.d-none {
  display: none;
}

/* Text alignment */
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}

/* Flex utilities */
.flex-row {
  flex-direction: row;
}
.flex-column {
  flex-direction: column;
}
.justify-content-center {
  justify-content: center;
}
.justify-content-between {
  justify-content: space-between;
}
.align-items-center {
  align-items: center;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-grow {
  flex-grow: 1;
}

/* Margin & Padding utilities */
.m-0 {
  margin: 0;
}
.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.my-3 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.my-4 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}
.my-5 {
  margin-top: 3rem;
  margin-bottom: 3rem;
}

.mt-1 {
  margin-top: 0.25rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-3 {
  margin-top: 1rem;
}
.mt-4 {
  margin-top: 1.5rem;
}
.mt-5 {
  margin-top: 3rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-3 {
  margin-bottom: 1rem;
}
.mb-4 {
  margin-bottom: 1.5rem;
}
.mb-5 {
  margin-bottom: 3rem;
}

.p-0 {
  padding: 0;
}
.p-1 {
  padding: 0.25rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-3 {
  padding: 1rem;
}
.p-4 {
  padding: 1.5rem;
}
.p-5 {
  padding: 3rem;
}

/* Screen reader only class */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* Button utilities */
.btn {
  padding: 12px 24px;
  border-radius: 30px;
  font-weight: 600;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: white;
  color: var(--primary);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn-outline {
  color: var(--primary);
  border: 2px solid var(--primary);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.btn:hover {
  transform: translateY(-3px);
}

.btn-primary:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn-outline:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: white;
}

.btn-sm {
  padding: 8px 16px;
  font-size: 0.9rem;
}

/* Card styles */
.glass-card {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px);
  border-radius: 20px;
  padding: 28px 24px;
  min-width: 180px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-card:hover {
  transform: translateY(-8px) scale(1.03);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.15);
}

.stat-icon {
  width: 64px;
  height: 64px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 18px;
  font-size: 1.6rem;
  color: white;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  position: relative;
  transition: all 0.4s ease;
}

.stat-card:hover .stat-icon {
  background: rgba(255, 255, 255, 0.25);
  transform: rotateY(180deg);
}

.stat-content {
  text-align: center;
  z-index: 2;
  width: 100%;
}

.stat-number {
  font-size: 2.8rem;
  font-weight: 700;
  margin-bottom: 8px;
  background: linear-gradient(90deg, #fff, #a5b4fc);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  min-height: 2.8rem;
  line-height: 1.1;
  letter-spacing: -0.5px;
}

.stat-label {
  font-size: 0.92rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.85);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-decoration {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  opacity: 0.5;
  transition: all 0.5s ease;
}

.stat-card:hover .stat-decoration {
  transform: scaleX(0.85);
  opacity: 0.8;
  height: 3px;
}

.hero-stats-container {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 28px;
  width: 100%;
  max-width: 1100px;
}

.btn-documentation-hero {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

/* Counter celebration animation */
@keyframes number-celebrate {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.12);
  }
  100% {
    transform: scale(1);
  }
}

/* Print styles */
@media print {
  .toc-container,
  .theme-toggle,
  header nav,
  footer {
    display: none !important;
  }

  .container {
    width: 100%;
    max-width: none;
    margin: 0;
    padding: 0;
  }

  body {
    background-color: white;
    color: black;
  }

  .project-overview,
  .team-content,
  .documentation-section,
  .feature-card {
    box-shadow: none;
    break-inside: avoid;
    page-break-inside: avoid;
  }

  a {
    color: black;
  }
}
