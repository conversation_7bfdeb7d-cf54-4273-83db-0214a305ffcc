{"id": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON> <PERSON>", "role": "Frontend Developer", "team": "web", "contact": {"phone": "+201202671449", "email": "<EMAIL>"}, "bio": "<PERSON><PERSON><PERSON> is a frontend developer with expertise in creating responsive and user-friendly web interfaces. He specializes in modern JavaScript frameworks and UI/UX design.", "education": ["B.S. Computer Science, Cairo University, 2022", "Frontend Web Development Certification, 2021"], "skills": [{"name": "JavaScript", "level": 90}, {"name": "React.js", "level": 85}, {"name": "HTML/CSS", "level": 95}, {"name": "UI/UX Design", "level": 80}, {"name": "Responsive Design", "level": 90}], "projects": [{"name": "Interactive Dashboard", "description": "Designed and implemented an interactive dashboard with real-time data visualization and responsive layout."}, {"name": "UI Component Library", "description": "Developed a reusable UI component library that improved development efficiency by 40%."}], "social": {"github": "https://github.com/abdo-ibrahim", "linkedin": "https://www.linkedin.com/in/abdo-ibrahim", "twitter": "", "instagram": "", "facebook": ""}, "tasks": [{"title": "Implement Dark Mode Toggle", "description": "Create a seamless dark mode toggle feature with smooth transitions and persistent user preferences across sessions.", "status": "Completed", "startDate": "03/05/2025", "endDate": "03/10/2025", "hours": 20, "category": "Frontend", "icon": "moon", "complexity": "Medium"}, {"title": "Optimize Image Loading", "description": "Implement lazy loading and responsive image techniques to improve page load performance and user experience on all devices.", "status": "In Progress", "startDate": "03/15/2025", "endDate": "03/22/2025", "hours": 15, "category": "Performance", "icon": "tachometer-alt", "complexity": "Medium"}, {"title": "Create Animated Chart Components", "description": "Develop reusable chart components with smooth animations and interactive tooltips for data visualization.", "status": "Pending", "startDate": "03/25/2025", "endDate": "04/02/2025", "hours": 25, "category": "Frontend", "icon": "chart-line", "complexity": "High"}, {"title": "Refactor CSS Architecture", "description": "Restructure CSS codebase to use a more maintainable methodology like BEM or SMACSS, improving code organization and reducing specificity issues.", "status": "Completed", "startDate": "02/20/2025", "endDate": "03/01/2025", "hours": 30, "category": "Frontend", "icon": "code", "complexity": "High"}]}