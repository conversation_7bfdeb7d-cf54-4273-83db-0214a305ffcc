/**
 * Roadmap Particles and Enhanced Animations
 * Adds interactive particle effects and animations to the roadmap summary
 */

document.addEventListener('DOMContentLoaded', function() {
  // Initialize the enhanced roadmap summary
  initRoadmapSummary();
});

function initRoadmapSummary() {
  // Initialize particle background
  initParticleBackground();
  
  // Initialize counter animations
  initCounterAnimations();
  
  // Initialize tilt effect if vanilla-tilt.js is available
  initTiltEffect();
  
  // Initialize icon particle effects
  initIconParticles();
}

/**
 * Initialize particle background for the roadmap summary
 */
function initParticleBackground() {
  const particlesContainer = document.getElementById('roadmap-particles');
  if (!particlesContainer) return;
  
  // Create canvas for particles
  const canvas = document.createElement('canvas');
  canvas.width = particlesContainer.offsetWidth;
  canvas.height = particlesContainer.offsetHeight;
  particlesContainer.appendChild(canvas);
  
  const ctx = canvas.getContext('2d');
  const particles = [];
  
  // Create particles
  const particleCount = Math.min(Math.floor((canvas.width * canvas.height) / 15000), 40);
  
  for (let i = 0; i < particleCount; i++) {
    particles.push({
      x: Math.random() * canvas.width,
      y: Math.random() * canvas.height,
      radius: Math.random() * 3 + 1,
      color: `rgba(67, 97, 238, ${Math.random() * 0.2 + 0.1})`,
      speedX: Math.random() * 0.5 - 0.25,
      speedY: Math.random() * 0.5 - 0.25,
      opacity: Math.random() * 0.5 + 0.3
    });
  }
  
  // Animation loop
  function animateParticles() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    particles.forEach(particle => {
      // Move particle
      particle.x += particle.speedX;
      particle.y += particle.speedY;
      
      // Bounce off edges
      if (particle.x < 0 || particle.x > canvas.width) particle.speedX *= -1;
      if (particle.y < 0 || particle.y > canvas.height) particle.speedY *= -1;
      
      // Draw particle
      ctx.beginPath();
      ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2);
      ctx.fillStyle = particle.color;
      ctx.globalAlpha = particle.opacity;
      ctx.fill();
      ctx.globalAlpha = 1;
    });
    
    requestAnimationFrame(animateParticles);
  }
  
  // Handle resize
  function handleResize() {
    canvas.width = particlesContainer.offsetWidth;
    canvas.height = particlesContainer.offsetHeight;
  }
  
  window.addEventListener('resize', handleResize);
  animateParticles();
}

/**
 * Initialize counter animations for metric values
 */
function initCounterAnimations() {
  const counters = document.querySelectorAll('.counter-value');
  
  counters.forEach(counter => {
    const target = parseInt(counter.getAttribute('data-target'));
    if (isNaN(target)) return;
    
    const observer = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting) {
        animateCounter(counter, target);
        observer.unobserve(counter);
      }
    }, { threshold: 0.5 });
    
    observer.observe(counter);
  });
}

/**
 * Animate counter from 0 to target value
 */
function animateCounter(element, target) {
  let count = 0;
  const duration = 2000; // 2 seconds
  const frameDuration = 1000 / 60; // 60fps
  const totalFrames = Math.round(duration / frameDuration);
  const increment = target / totalFrames;
  
  const animate = () => {
    count += increment;
    if (count >= target) {
      element.textContent = target;
      return;
    }
    
    element.textContent = Math.floor(count);
    requestAnimationFrame(animate);
  };
  
  animate();
}

/**
 * Initialize tilt effect for 3D cards
 */
function initTiltEffect() {
  if (typeof VanillaTilt === 'undefined') {
    // Load vanilla-tilt.js if not already loaded
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/vanilla-tilt@1.7.2/dist/vanilla-tilt.min.js';
    script.onload = applyTiltEffect;
    document.head.appendChild(script);
  } else {
    applyTiltEffect();
  }
}

/**
 * Apply tilt effect to elements with data-tilt attribute
 */
function applyTiltEffect() {
  if (typeof VanillaTilt !== 'undefined') {
    VanillaTilt.init(document.querySelectorAll('[data-tilt]'), {
      max: 15,
      speed: 400,
      glare: true,
      'max-glare': 0.3,
      scale: 1.05
    });
  }
}

/**
 * Initialize particle effects for metric icons
 */
function initIconParticles() {
  const iconContainers = document.querySelectorAll('.icon-particles');
  
  iconContainers.forEach(container => {
    const parent = container.closest('.metric-icon');
    if (!parent) return;
    
    // Get icon color
    const iconColor = window.getComputedStyle(parent).backgroundColor;
    
    // Create particles
    for (let i = 0; i < 5; i++) {
      createParticle(container, iconColor);
    }
    
    // Add event listener to create more particles on hover
    parent.addEventListener('mouseenter', () => {
      for (let i = 0; i < 8; i++) {
        setTimeout(() => {
          createParticle(container, iconColor);
        }, i * 100);
      }
    });
  });
}

/**
 * Create a single particle in the container
 */
function createParticle(container, color) {
  const particle = document.createElement('span');
  particle.className = 'icon-particle';
  
  // Random position within container
  const size = Math.random() * 6 + 2;
  const posX = Math.random() * 100;
  const posY = Math.random() * 100;
  const rotation = Math.random() * 360;
  const duration = Math.random() * 2 + 1;
  const delay = Math.random();
  
  // Set particle styles
  particle.style.cssText = `
    position: absolute;
    width: ${size}px;
    height: ${size}px;
    background-color: ${color};
    border-radius: 50%;
    top: ${posY}%;
    left: ${posX}%;
    opacity: 0;
    transform: rotate(${rotation}deg) scale(0);
    animation: particle-float ${duration}s ease-out ${delay}s forwards;
    pointer-events: none;
  `;
  
  // Add particle to container
  container.appendChild(particle);
  
  // Remove particle after animation completes
  setTimeout(() => {
    particle.remove();
  }, (duration + delay) * 1000 + 100);
}

// Add particle animation to CSS
function addParticleStyles() {
  if (!document.getElementById('particle-styles')) {
    const style = document.createElement('style');
    style.id = 'particle-styles';
    style.textContent = `
      @keyframes particle-float {
        0% {
          opacity: 0;
          transform: rotate(0) scale(0);
        }
        20% {
          opacity: 0.8;
          transform: rotate(45deg) scale(1);
        }
        100% {
          opacity: 0;
          transform: translate(var(--tx, 20px), var(--ty, -30px)) rotate(var(--tr, 90deg)) scale(0);
        }
      }
    `;
    document.head.appendChild(style);
  }
}

// Call this function to add the particle styles
addParticleStyles();
