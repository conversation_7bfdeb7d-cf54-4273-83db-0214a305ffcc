/**
 * Dark Theme Styles
 */

body.dark-mode {
  /* Dark mode color overrides - premium palette */
  --bg-light: #151c2c; /* Richer navy blue background */
  --bg-white: #1f2937; /* Deeper blue-gray for cards */
  --text-dark: #f0f4f8; /* Crisp white with slight blue tint */
  --text-light: #c3cbdc; /* Brighter secondary text for better readability */
  --card-bg-dark: #1f2937; /* Match bg-white */
  --border-dark: #2e3846; /* Refined border color */
  --text-white: #ffffff;

  /* Enhanced shadows and effects */
  --card-shadow: 0 8px 24px rgba(0, 0, 0, 0.25), 0 2px 4px rgba(0, 0, 0, 0.3);
  --subtle-highlight: rgba(
    103,
    151,
    255,
    0.08
  ); /* Slightly brighter blue highlight */

  /* Premium gradients */
  --card-highlight: linear-gradient(145deg, #242f42, #1c2534);
  --accent-gradient: linear-gradient(135deg, #4361ee, #3a51db);
  --success-gradient: linear-gradient(135deg, #10b981, #059669);
  --warning-gradient: linear-gradient(135deg, #f59e0b, #d97706);
  --danger-gradient: linear-gradient(135deg, #ef4444, #dc2626);

  /* Glass effects */
  --glass-bg: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  --glass-highlight: rgba(255, 255, 255, 0.03);

  /* Button and interactive states */
  --hover-transition: 250ms cubic-bezier(0.25, 0.8, 0.25, 1);
  --hover-elevation: 0 10px 28px rgba(0, 0, 0, 0.3);

  background-color: var(--bg-light);
  color: var(--text-white);
}

/* Theme Toggle - Base Styling */
.theme-toggle {
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 6px;
  border-radius: 30px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

/* Toggle gradient border effect */
.theme-toggle::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 30px;
  padding: 1px;
  background: linear-gradient(
    135deg,
    rgba(67, 97, 238, 0.15) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%
  );
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
  opacity: 0.7;
}

/* Icon styling */
.theme-toggle i {
  position: relative;
  z-index: 2;
  font-size: 16px;
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Toggle ball styling */
.toggle-ball {
  position: absolute;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  left: 3px;
  z-index: 1;
  transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275),
    background 0.3s ease, box-shadow 0.3s ease;
}

/* Inner highlight for toggle ball */
.toggle-ball::after {
  content: "";
  position: absolute;
  inset: 4px;
  border-radius: 50%;
  opacity: 0.9;
}

/* Light mode specific styling */
.theme-toggle i.fa-sun {
  color: #ff9500;
  filter: drop-shadow(0 0 3px rgba(255, 149, 0, 0.5));
  transform: rotate(0deg) scale(1.2);
  opacity: 1;
  animation: sunPulse 4s ease-in-out infinite;
}

.theme-toggle i.fa-moon {
  color: #6e7787;
  filter: drop-shadow(0 0 2px rgba(110, 119, 135, 0.3));
  transform: rotate(-120deg) scale(0.7);
  opacity: 0.5;
}

.theme-toggle:hover i.fa-sun {
  color: #ff8000;
  filter: drop-shadow(0 0 5px rgba(255, 128, 0, 0.6));
}

.theme-toggle:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

/* Light mode toggle ball */
.toggle-ball {
  background: linear-gradient(145deg, #ffffff, #f0f0f0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15),
    inset 0 -2px 5px rgba(0, 0, 0, 0.05),
    inset 0 2px 5px rgba(255, 255, 255, 0.8);
}

.toggle-ball::after {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 1) 0%,
    rgba(240, 240, 240, 0.5) 50%
  );
}

/* Dark mode overrides */
body.dark-mode .theme-toggle {
  background: linear-gradient(
    145deg,
    rgba(30, 41, 59, 0.7),
    rgba(15, 23, 42, 0.9)
  );
  border-color: rgba(255, 255, 255, 0.12);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3), 0 5px 15px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(67, 97, 238, 0.05);
  backdrop-filter: blur(10px);
}

body.dark-mode .theme-toggle:active {
  transform: translateY(0);
  transition: transform 0.1s;
}

body.dark-mode .theme-toggle i.fa-sun {
  color: #fcd34d;
  filter: drop-shadow(0 0 3px rgba(252, 211, 77, 0.5));
  opacity: 0.6;
  transform: rotate(120deg) scale(0.7);
  animation: none;
}

body.dark-mode .theme-toggle i.fa-moon {
  color: #a5b4fc;
  filter: drop-shadow(0 0 5px rgba(165, 180, 252, 0.6));
  opacity: 1;
  transform: rotate(0deg) scale(1.2);
  animation: moonPulse 3s ease-in-out infinite;
}

body.dark-mode .toggle-ball {
  background: linear-gradient(145deg, #334155, #1e293b);
  transform: translateX(33px);
  box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2), 0 0 10px rgba(67, 97, 238, 0.2),
    inset 0 -2px 5px rgba(0, 0, 0, 0.2),
    inset 0 2px 5px rgba(255, 255, 255, 0.1);
  animation: darkModeGlow 4s infinite;
}

body.dark-mode .toggle-ball::after {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.07) 0%,
    transparent 50%
  );
}

/* Connect SVG gradient based on theme */
body:not(.dark-mode) .circle {
  stroke: url(#gradient-light);
}

body.dark-mode .circle {
  stroke: url(#gradient-dark);
}

/* Animations */
@keyframes sunPulse {
  0%,
  100% {
    transform: scale(1.2) rotate(0);
  }
  50% {
    transform: scale(1.25) rotate(5deg);
  }
}

@keyframes moonPulse {
  0%,
  100% {
    transform: scale(1.2);
  }
  50% {
    transform: scale(1.25);
  }
}

@keyframes darkModeGlow {
  0%,
  100% {
    box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.1), 0 0 5px rgba(67, 97, 238, 0.2),
      inset 0 -2px 5px rgba(0, 0, 0, 0.2),
      inset 0 2px 5px rgba(255, 255, 255, 0.1);
  }
  50% {
    box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.3),
      0 0 15px rgba(67, 97, 238, 0.4), inset 0 -2px 5px rgba(0, 0, 0, 0.2),
      inset 0 2px 5px rgba(255, 255, 255, 0.1);
  }
}

/* Sprint Progress Section Dark Mode */
body.dark-mode .sprint-progress-section {
  background: var(--bg-light);
  border-bottom: 1px solid var(--border-dark);
}

body.dark-mode .sprint-chart-container,
body.dark-mode .metric-card:not(.highlight-card),
body.dark-mode .sprint-details-container,
body.dark-mode .sprint-detail,
body.dark-mode .sprint-insights {
  background: var(--card-highlight);
  border-color: var(--border-dark);
  box-shadow: var(--card-shadow);
  border-radius: 8px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

body.dark-mode .sprint-details-container {
  background: #1e293a;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

body.dark-mode .sprint-chart-container:hover,
body.dark-mode .metric-card:not(.highlight-card):hover,
body.dark-mode .sprint-insights:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 25px rgba(0, 0, 0, 0.25);
}

body.dark-mode .chart-controls select {
  background-color: #2a3548;
  border-color: var(--border-dark);
  color: var(--text-light);
  border-radius: 6px;
}

body.dark-mode .sprint-tabs-wrapper {
  background: linear-gradient(to right, #2a3548, #1e293a);
  border-radius: 8px;
  overflow: hidden;
  padding: 5px 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 -5px 15px rgba(0, 0, 0, 0.2);
}

body.dark-mode .sprint-nav-button {
  background: linear-gradient(135deg, #2a3548, #1a2234);
  color: var(--primary-light);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}

body.dark-mode .sprint-nav-button:hover {
  background: linear-gradient(135deg, #3a4558, #2a3548);
  color: white;
}

body.dark-mode .sprint-nav-button.disabled {
  background: #1a1a1a;
  color: rgba(255, 255, 255, 0.3);
  box-shadow: none;
}

body.dark-mode .sprint-tabs {
  background-color: transparent;
}

body.dark-mode .sprint-tab {
  border-radius: 8px;
  transition: all 0.2s ease;
  color: rgba(255, 255, 255, 0.7);
  background-color: transparent;
}

body.dark-mode .sprint-tab:hover:not(.active):not(.disabled) {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

body.dark-mode .sprint-tab.active {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  box-shadow: 0 2px 10px rgba(67, 97, 238, 0.3);
  transform: translateY(-2px);
}

body.dark-mode .tab-status {
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
}

body.dark-mode .tab-status.upcoming {
  background: rgba(255, 255, 255, 0.3);
}

body.dark-mode .task-distribution,
body.dark-mode .key-achievements,
body.dark-mode .sprint-health,
body.dark-mode .sprint-stat {
  background: var(--card-highlight);
  border-color: var(--border-dark);
  border-radius: 8px;
  box-shadow: var(--card-shadow);
}

body.dark-mode .document-link {
  background-color: #2a3548;
  border: 1px solid #3a4860;
  border-radius: 6px;
  transition: all 0.2s ease;
}

body.dark-mode .document-link:hover {
  background-color: var(--primary);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.2);
}

body.dark-mode .achievement-item {
  border-color: rgba(255, 255, 255, 0.1);
  padding: 10px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

body.dark-mode .achievement-item:hover {
  background-color: var(--subtle-highlight);
}

body.dark-mode .insights-list li {
  border-color: rgba(176, 184, 200, 0.15);
  padding: 12px 0;
  transition: padding 0.2s ease;
}

body.dark-mode .insights-list li:hover {
  padding-left: 5px;
}

/* Sprint Health Dashboard Dark Mode Styles */
body.dark-mode .gauge-bg {
  stroke: rgba(255, 255, 255, 0.1);
}

body.dark-mode .gauge-value {
  fill: var(--text-white);
  font-weight: 600;
}

body.dark-mode .gauge-label {
  color: var(--text-light);
}

body.dark-mode .sprint-insights {
  background: var(--card-highlight);
  border: 1px solid var(--border-dark);
  box-shadow: var(--card-shadow);
  border-radius: 8px;
}

body.dark-mode .insight-title {
  color: var(--text-white);
  font-weight: 600;
}

body.dark-mode .insights-list li {
  color: var(--text-light);
  border-bottom: 1px dashed rgba(176, 184, 200, 0.15);
}

body.dark-mode .insight-badge.positive {
  background-color: rgba(114, 196, 151, 0.15);
  color: #98ebc0;
  font-weight: 600;
  border-radius: 4px;
}

body.dark-mode .insight-badge.neutral {
  background-color: rgba(76, 201, 240, 0.15);
  color: #8adeff;
  font-weight: 600;
  border-radius: 4px;
}

body.dark-mode .insight-badge.negative {
  background-color: rgba(242, 92, 84, 0.15);
  color: #ffb1ad;
  font-weight: 600;
  border-radius: 4px;
}

/* Ensure gauge colors are visible in dark mode */
body.dark-mode .gauge-fill {
  stroke-width: 12;
}

body.dark-mode .gauge-fill.quality {
  stroke: #6cd9ff;
}

body.dark-mode .gauge-fill.velocity {
  stroke: #ff7b75;
}

body.dark-mode .gauge-fill.satisfaction {
  stroke: #98ebc0;
}

/* Generic component overrides for dark mode */
body.dark-mode .feature-card,
body.dark-mode .member-card,
body.dark-mode .team-tech,
body.dark-mode .project-overview,
body.dark-mode .doc-card,
body.dark-mode .contact-card,
body.dark-mode .testimonial-content {
  background: var(--card-highlight);
  border-color: var(--border-dark);
  box-shadow: var(--card-shadow);
  border-radius: 8px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

body.dark-mode .feature-card:hover,
body.dark-mode .member-card:hover,
body.dark-mode .doc-card:hover,
body.dark-mode .contact-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 25px rgba(0, 0, 0, 0.25);
}

body.dark-mode .team-tab,
body.dark-mode .gallery-tab {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--text-light);
  border-radius: 6px;
  transition: all 0.2s ease;
}

body.dark-mode .team-tab.active,
body.dark-mode .gallery-tab.active {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  box-shadow: 0 2px 10px rgba(67, 97, 238, 0.3);
}

body.dark-mode .social-icon {
  background-color: rgba(255, 255, 255, 0.08);
  color: var(--text-light);
  transition: all 0.2s ease;
  border-radius: 50%;
}

body.dark-mode .social-icon:hover {
  background-color: var(--primary);
  color: white;
  transform: translateY(-2px);
}

body.dark-mode .tech-item {
  background-color: rgba(67, 97, 238, 0.15);
  border-radius: 4px;
  padding: 6px 12px;
  transition: all 0.2s ease;
}

body.dark-mode .tech-item:hover {
  background-color: rgba(67, 97, 238, 0.25);
  transform: translateY(-2px);
}

/* Table of Contents in dark mode */
body.dark-mode .toc-container {
  background: var(--card-highlight);
  border-color: var(--border-dark);
  border-radius: 8px;
  box-shadow: var(--card-shadow);
}

body.dark-mode .toc-header {
  background: transparent;
  border-color: rgba(176, 184, 200, 0.15);
  font-weight: 600;
}

body.dark-mode .toc-item {
  color: var(--text-light);
  border-radius: 4px;
  transition: all 0.2s ease;
  margin: 2px 0;
  padding: 8px 12px;
}

body.dark-mode .toc-item:hover {
  background: var(--subtle-highlight);
  color: var(--text-white);
}

body.dark-mode .toc-item.active {
  background: rgba(67, 97, 238, 0.15);
  color: #a0b3ff;
  font-weight: 500;
}

/* Forms in dark mode */
body.dark-mode .form-group input,
body.dark-mode .form-group textarea {
  background: rgba(255, 255, 255, 0.05);
  border-color: var(--border-dark);
  color: var(--text-white);
  border-radius: 6px;
  transition: all 0.2s ease;
}

body.dark-mode .form-group input:focus,
body.dark-mode .form-group textarea:focus {
  border-color: var(--primary-light);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
  background: rgba(255, 255, 255, 0.07);
}

/* Dark mode scrollbar customization */
body.dark-mode ::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

body.dark-mode ::-webkit-scrollbar-track {
  background: var(--bg-light);
}

body.dark-mode ::-webkit-scrollbar-thumb {
  background: var(--border-dark);
  border-radius: 5px;
}

body.dark-mode ::-webkit-scrollbar-thumb:hover {
  background: #4a5568;
}

/* Enhanced Project Overview Dark Mode Styles */
body.dark-mode .project-overview {
  background: linear-gradient(135deg, #1e2637 0%, #151c2c 100%);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.35), 0 1px 2px rgba(0, 0, 0, 0.2),
    inset 0 1px 1px rgba(255, 255, 255, 0.03);
  padding: 45px;
  margin-bottom: 60px;
}

/* Add subtle background pattern/effect */
body.dark-mode .project-overview::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(
      circle at 20% 30%,
      rgba(67, 97, 238, 0.08) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 80%,
      rgba(76, 201, 240, 0.08) 0%,
      transparent 50%
    );
  pointer-events: none;
  z-index: 0;
}

/* Enhance overview header */
body.dark-mode .overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 35px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  padding-bottom: 20px;
  position: relative;
}

body.dark-mode .overview-badge {
  background: linear-gradient(
    135deg,
    var(--primary) 0%,
    var(--primary-dark) 100%
  );
  box-shadow: 0 4px 12px rgba(67, 97, 238, 0.3);
  font-weight: 600;
  letter-spacing: 0.5px;
  padding: 8px 16px;
  border-radius: 30px;
  font-size: 0.9rem;
}

/* Enhance overview container */
body.dark-mode .overview-container {
  display: grid;
  grid-template-columns: 3fr 2fr;
  gap: 40px;
  margin-bottom: 40px;
}

/* Enhance overview content */
body.dark-mode .overview-content {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  padding: 25px;
  border: 1px solid rgba(255, 255, 255, 0.03);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

body.dark-mode .overview-intro {
  color: var(--text-white);
  font-size: 1.15rem;
  line-height: 1.8;
  margin-bottom: 25px;
}

body.dark-mode .overview-detail {
  color: var(--text-light);
  line-height: 1.8;
}

body.dark-mode .highlight-text {
  color: #a0b3ff;
  font-weight: 600;
  position: relative;
  display: inline-block;
}

body.dark-mode .highlight-text::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, var(--primary), transparent);
}

/* Enhance metrics */
body.dark-mode .overview-metrics {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.03);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  gap: 30px;
}

body.dark-mode .metric-item {
  background: linear-gradient(145deg, #242f42, #1c2534);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

body.dark-mode .metric-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
}

body.dark-mode .metric-icon {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6px 15px rgba(67, 97, 238, 0.3);
  font-size: 1.4rem;
  color: white;
}

body.dark-mode .metric-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-white);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

body.dark-mode .metric-label {
  color: var(--text-light);
  font-size: 1rem;
  font-weight: 500;
  text-align: center;
}

/* Enhance CTA */
body.dark-mode .overview-cta {
  margin-top: 30px;
}

body.dark-mode .overview-btn {
  background: rgba(67, 97, 238, 0.1);
  border: 1px solid rgba(67, 97, 238, 0.3);
  color: #a0b3ff;
  border-radius: 30px;
  padding: 12px 28px;
  font-weight: 600;
  transition: all 0.3s ease;
}

body.dark-mode .btn-documentation-hero {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

body.dark-mode .overview-btn:hover {
  background: var(--primary);
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(67, 97, 238, 0.3);
}

/* Enhance feature cards */
body.dark-mode .features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

body.dark-mode .feature-card {
  background: linear-gradient(145deg, #242f42, #1c2534);
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.03);
}

body.dark-mode .feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3);
}

body.dark-mode .feature-icon {
  background: linear-gradient(135deg, var(--primary-light), var(--primary));
  width: 60px;
  height: 60px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 8px 15px rgba(67, 97, 238, 0.25);
}

body.dark-mode .feature-card h3 {
  color: var(--text-white);
  font-size: 1.25rem;
  margin: 15px 0;
  font-weight: 600;
}

body.dark-mode .feature-card p {
  color: var(--text-light);
  line-height: 1.7;
}

body.dark-mode .feature-list li {
  color: var(--text-light);
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

body.dark-mode .feature-list li i {
  color: #98ebc0;
  margin-right: 10px;
}

/* Enhance divider */
body.dark-mode .overview-divider {
  margin: 50px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

body.dark-mode .divider-text {
  background: linear-gradient(135deg, var(--primary-light), var(--primary));
  color: white;
  padding: 8px 20px;
  border-radius: 30px;
  font-weight: 600;
  font-size: 1.1rem;
  box-shadow: 0 6px 15px rgba(67, 97, 238, 0.3);
}

/* Timeline enhancements */
body.dark-mode .overview-timeline {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 16px;
  padding: 25px;
  margin-top: 50px;
  border: 1px solid rgba(255, 255, 255, 0.03);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

body.dark-mode .timeline-track {
  background: rgba(255, 255, 255, 0.06);
  height: 6px;
  border-radius: 3px;
  position: relative;
}

body.dark-mode .timeline-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 60%;
  background: linear-gradient(to right, var(--primary), var(--primary-light));
  border-radius: 3px;
}

body.dark-mode .timeline-milestones {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
}

body.dark-mode .milestone {
  color: var(--text-light);
  position: relative;
  padding-top: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

body.dark-mode .milestone::before {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 10px;
  height: 10px;
  background: #2a3548;
  border-radius: 50%;
  transition: all 0.3s ease;
}

body.dark-mode .milestone.active {
  color: #a0b3ff;
}

body.dark-mode .milestone.active::before {
  background: var(--primary);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.3);
}

body.dark-mode .milestone:hover {
  transform: translateY(-3px);
}

/* Overview conclusion */
body.dark-mode .overview-conclusion {
  color: var(--text-light);
  text-align: center;
  max-width: 800px;
  margin: 40px auto 20px;
  font-style: italic;
  line-height: 1.8;
  padding: 20px;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.03);
}

/* Responsive adjustments for Overview section */
@media (max-width: 768px) {
  body.dark-mode .project-overview {
    padding: 30px 20px;
  }

  body.dark-mode .overview-container {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  body.dark-mode .overview-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  body.dark-mode .features-grid {
    grid-template-columns: 1fr;
  }
}

/* Enhanced Roadmap Cards in Dark Mode */
body.dark-mode .roadmap-container.cards-view {
  padding: 20px 0;
}

body.dark-mode .roadmap-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 25px;
  margin: 30px 0;
}

body.dark-mode .roadmap-card {
  background: linear-gradient(145deg, #242f42, #1c2534);
  border-radius: 12px;
  padding: 25px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

body.dark-mode .roadmap-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
}

body.dark-mode .card-status-indicator {
  position: absolute;
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
}

body.dark-mode .roadmap-complete .card-status-indicator {
  background: var(--success-gradient);
  box-shadow: 0 0 15px rgba(76, 201, 240, 0.5);
}

body.dark-mode .roadmap-current .card-status-indicator {
  background: var(--accent-gradient);
  box-shadow: 0 0 15px rgba(67, 97, 238, 0.5);
}

body.dark-mode .roadmap-upcoming .card-status-indicator {
  background: var(--text-light);
  opacity: 0.7;
}

body.dark-mode .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

body.dark-mode .card-date {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
}

body.dark-mode .roadmap-status {
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  display: inline-block;
}

body.dark-mode .roadmap-complete .roadmap-status {
  background: rgba(76, 201, 240, 0.15);
  color: #8adeff;
}

body.dark-mode .roadmap-current .roadmap-status {
  background: rgba(67, 97, 238, 0.15);
  color: #a0b3ff;
}

body.dark-mode .roadmap-upcoming .roadmap-status {
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-light);
}

body.dark-mode .roadmap-card h3 {
  font-size: 1.3rem;
  margin: 0 0 15px 0;
  color: var(--text-white);
  padding-left: 10px;
}

body.dark-mode .roadmap-card p {
  color: var(--text-light);
  margin-bottom: 20px;
  line-height: 1.6;
  font-size: 0.95rem;
  padding-left: 10px;
}

body.dark-mode .roadmap-details {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  padding-left: 10px;
  margin-bottom: 20px;
}

body.dark-mode .detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.85rem;
  color: var(--text-light);
}

body.dark-mode .roadmap-complete .detail-item i {
  color: #8adeff;
}

body.dark-mode .roadmap-current .detail-item i {
  color: #a0b3ff;
}

body.dark-mode .roadmap-upcoming .detail-item i {
  color: var(--text-light);
  opacity: 0.7;
}

body.dark-mode .view-card-details {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), transparent);
  color: var(--text-white);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: block;
  width: 100%;
  text-align: center;
}

body.dark-mode .roadmap-complete .view-card-details:hover {
  background: rgba(76, 201, 240, 0.15);
  border-color: rgba(76, 201, 240, 0.3);
  transform: translateY(-3px);
}

body.dark-mode .roadmap-current .view-card-details:hover {
  background: rgba(67, 97, 238, 0.15);
  border-color: rgba(67, 97, 238, 0.3);
  transform: translateY(-3px);
}

body.dark-mode .roadmap-upcoming .view-card-details:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-3px);
}

/* Add subtle hover effect */
body.dark-mode .roadmap-card::after {
  content: "";
  position: absolute;
  top: -100%;
  left: -100%;
  width: 300%;
  height: 300%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none;
}

body.dark-mode .roadmap-card:hover::after {
  opacity: 0.4;
}

body.dark-mode .legend-item {
  background-color: #2a556b;
}

/* Dark mode styles for roadmap summary */
body.dark-mode .roadmap-section {
  background: var(--bg-dark, #0f172a);
  background-image: linear-gradient(
    135deg,
    rgba(30, 41, 59, 0.95) 0%,
    rgba(15, 23, 42, 0.9) 50%,
    rgba(30, 41, 59, 0.95) 100%
  );
  animation: darkGradientShift 15s ease infinite alternate;
  background-size: 200% 200%;
}

@keyframes darkGradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Dark mode particle background */
body.dark-mode .roadmap-particles {
  opacity: 0.2;
}

/* Dark mode floating icons */
body.dark-mode .roadmap-icon {
  filter: drop-shadow(0 3px 8px rgba(0, 0, 0, 0.5));
}

/* Add glow effect to icons in dark mode */
body.dark-mode .roadmap-icon:hover {
  filter: drop-shadow(0 0 10px var(--icon-color, rgba(99, 102, 241, 0.7)));
}

/* Dark mode decorative shapes */
body.dark-mode .roadmap-section .decorative-shape {
  background: linear-gradient(
    135deg,
    rgba(99, 102, 241, 0.3),
    rgba(79, 70, 229, 0.3)
  );
  opacity: 0.15;
}

body.dark-mode .roadmap-section::before {
  background: linear-gradient(
    to bottom,
    rgba(15, 23, 42, 0.9),
    rgba(15, 23, 42, 0)
  );
}

body.dark-mode .roadmap-section::after {
  background-image: url("data:image/svg+xml,%3Csvg width='30' height='30' viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15 0C6.716 0 0 6.716 0 15c0 8.284 6.716 15 15 15 8.284 0 15-6.716 15-15 0-8.284-6.716-15-15-15zm0 27.5c-6.904 0-12.5-5.596-12.5-12.5S8.096 2.5 15 2.5 27.5 8.096 27.5 15 21.904 27.5 15 27.5z' fill='%236366f1' fill-opacity='0.05' fill-rule='nonzero'/%3E%3C/svg%3E");
  opacity: 0.3;
}

body.dark-mode .roadmap-section .decorative-shape {
  background: linear-gradient(
    135deg,
    rgba(99, 102, 241, 0.3),
    rgba(79, 70, 229, 0.3)
  );
  opacity: 0.15;
}

body.dark-mode .roadmap-intro {
  color: var(--text-white);
}

body.dark-mode .roadmap-summary {
  background: rgba(15, 23, 42, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(67, 97, 238, 0.15);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

body.dark-mode .roadmap-summary:hover {
  box-shadow: 0 20px 45px rgba(0, 0, 0, 0.4), 0 0 20px rgba(67, 97, 238, 0.2);
  border: 1px solid rgba(67, 97, 238, 0.25);
}

body.dark-mode .progress-circular {
  background: linear-gradient(145deg, #242f42, #1c2534);
  border: 1px solid rgba(67, 97, 238, 0.15);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

body.dark-mode .progress-circular:hover {
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3), 0 0 15px rgba(67, 97, 238, 0.2);
  transform: translateY(-5px);
}

body.dark-mode .circular-chart {
  filter: drop-shadow(0 4px 8px rgba(67, 97, 238, 0.3));
}

body.dark-mode .circle-bg {
  stroke: rgba(255, 255, 255, 0.08);
}

body.dark-mode .percentage {
  fill: var(--text-white);
  font-weight: 600;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

body.dark-mode .progress-label {
  color: var(--text-white);
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

body.dark-mode .progress-label::after {
  background: linear-gradient(to right, #6366f1, #8b5cf6);
  box-shadow: 0 0 8px rgba(99, 102, 241, 0.5);
}

body.dark-mode .stat-item {
  background: linear-gradient(145deg, #242f42, #1c2534);
  border: 1px solid rgba(67, 97, 238, 0.1);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

body.dark-mode .stat-item:hover {
  background: linear-gradient(145deg, #293548, #1e293b);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25), 0 0 15px rgba(67, 97, 238, 0.15);
  transform: translateY(-7px);
  border: 1px solid rgba(67, 97, 238, 0.2);
}

body.dark-mode .stat-item:not(:last-child)::after {
  background: linear-gradient(
    to bottom,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
}

body.dark-mode .metric-icon {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  box-shadow: 0 8px 20px rgba(67, 97, 238, 0.3),
    0 0 0 1px rgba(99, 102, 241, 0.2);
}

body.dark-mode .stat-item:hover .metric-icon {
  box-shadow: 0 10px 25px rgba(67, 97, 238, 0.4),
    0 0 0 2px rgba(99, 102, 241, 0.25);
  transform: scale(1.1) rotate(10deg);
}

body.dark-mode .metric-icon::before {
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0.8) 0%,
    transparent 70%
  );
}

body.dark-mode .stat-value {
  color: var(--text-white);
  background: linear-gradient(to right, #a0b3ff, #6366f1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

body.dark-mode .stat-value::after {
  background: linear-gradient(to right, #6366f1, #8b5cf6);
  box-shadow: 0 0 8px rgba(99, 102, 241, 0.3);
}

body.dark-mode .stat-label {
  color: var(--text-light);
}

/* Animate glow effect for stat items in dark mode */
@keyframes dark-mode-stat-glow {
  0%,
  100% {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15),
      0 0 0 1px rgba(67, 97, 238, 0.1);
  }
  50% {
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2), 0 0 15px rgba(67, 97, 238, 0.2),
      0 0 0 1px rgba(67, 97, 238, 0.15);
  }
}

body.dark-mode .roadmap-current .stat-item {
  animation: dark-mode-stat-glow 3s infinite ease-in-out;
}

/* ======================================
   ENHANCED TEAM SECTION - DARK MODE
====================================== */

/* Team section background enhancements */
body.dark-mode .teams-container {
  background: linear-gradient(135deg, #151c2c 0%, #0f172a 100%);
  position: relative;
}

body.dark-mode .team-bg-circle {
  opacity: 0.15;
  filter: blur(80px);
  background: linear-gradient(
    45deg,
    rgba(99, 102, 241, 0.3),
    rgba(79, 70, 229, 0.3)
  );
}

/* Enhanced Team Intro */
body.dark-mode .team-intro h3 {
  color: var(--text-white);
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

body.dark-mode .team-intro h3::after {
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  box-shadow: 0 0 8px rgba(99, 102, 241, 0.5);
}

body.dark-mode .team-intro p {
  color: var(--text-light);
}

/* Enhanced Team Tabs */
body.dark-mode .team-tabs {
  background: rgba(15, 23, 42, 0.3);
  padding: 8px;
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
}

body.dark-mode .team-tab {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

body.dark-mode .team-tab:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

body.dark-mode .team-tab.active {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  border: none;
  box-shadow: 0 8px 20px rgba(99, 102, 241, 0.3),
    0 0 0 1px rgba(99, 102, 241, 0.5);
  transform: translateY(-5px);
}

body.dark-mode .team-tab i {
  color: var(--text-white);
  opacity: 0.7;
}

body.dark-mode .team-tab.active i {
  opacity: 1;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* Member Cards Enhanced */
body.dark-mode .member-card {
  background: linear-gradient(145deg, #242f42, #1c2534);
  border: 1px solid rgba(255, 255, 255, 0.03);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
}

body.dark-mode .member-card:hover {
  transform: translateY(-15px) rotateY(5deg);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.35), 0 0 0 1px rgba(99, 102, 241, 0.2);
}

body.dark-mode .member-card::after {
  box-shadow: 0 20px 40px rgba(99, 102, 241, 0.3);
}

body.dark-mode .member-img-container {
  background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
}

body.dark-mode .member-img {
  background: linear-gradient(45deg, #151c2c, #1e293b);
  color: #a0b3ff;
}

body.dark-mode .view-profile {
  background: linear-gradient(to top, rgba(99, 102, 241, 0.9), transparent);
  color: white;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

body.dark-mode .member-info {
  background: rgba(15, 23, 42, 0.5);
}

body.dark-mode .member-name {
  color: var(--text-white);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

body.dark-mode .member-role {
  background-color: rgba(99, 102, 241, 0.2);
  color: #a0b3ff;
  border: 1px solid rgba(99, 102, 241, 0.3);
}

body.dark-mode .member-card:hover .member-role {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  color: white;
  box-shadow: 0 5px 15px rgba(99, 102, 241, 0.3);
}

/* Enhanced Social Icons */
body.dark-mode .social-icon {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--text-light);
}

body.dark-mode .social-icon:hover {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  border-color: transparent;
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(99, 102, 241, 0.3);
  color: white;
}

/* Team Tech Section Enhancements */
body.dark-mode .team-tech {
  background: linear-gradient(145deg, #242f42, #1c2534);
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.03);
}

body.dark-mode .team-tech::before {
  background: linear-gradient(to bottom, #6366f1, #4f46e5);
  box-shadow: 0 0 15px rgba(99, 102, 241, 0.5);
}

body.dark-mode .tech-title {
  color: var(--text-white);
}

body.dark-mode .tech-title i {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  box-shadow: 0 8px 15px rgba(99, 102, 241, 0.3);
}

/* Tech Items */
body.dark-mode .tech-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.05);
  color: var(--text-light);
}

body.dark-mode .tech-item:hover {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  border-color: transparent;
  color: white;
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(99, 102, 241, 0.3);
}

/* Skills Enhancements */
body.dark-mode .skill-label {
  color: var(--text-white);
}

body.dark-mode .skill-label::before {
  color: #a0b3ff;
}

body.dark-mode .meter-bar {
  background-color: rgba(255, 255, 255, 0.05);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

body.dark-mode .meter-fill {
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  box-shadow: 0 0 10px rgba(99, 102, 241, 0.4);
}

body.dark-mode .meter-fill::after {
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.3),
    rgba(255, 255, 255, 0.1)
  );
}

body.dark-mode .meter-fill span {
  background: #1e293b;
  border: 1px solid rgba(99, 102, 241, 0.3);
  color: #a0b3ff;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

/* Team Member Filtering */
body.dark-mode .filter-label {
  color: var(--text-white);
}

body.dark-mode .filter-label::after {
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  box-shadow: 0 0 8px rgba(99, 102, 241, 0.3);
}

body.dark-mode .filter-btn {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.05);
  color: var(--text-light);
}

body.dark-mode .filter-btn::before {
  background: linear-gradient(
    135deg,
    rgba(99, 102, 241, 0.1) 0%,
    rgba(139, 92, 246, 0.1) 100%
  );
}

body.dark-mode .filter-btn:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  border-color: rgba(99, 102, 241, 0.2);
  color: #a0b3ff;
}

body.dark-mode .filter-btn.active {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  color: white;
  box-shadow: 0 10px 20px rgba(99, 102, 241, 0.3);
}

body.dark-mode .filter-counter {
  background: rgba(255, 255, 255, 0.1);
}

body.dark-mode .filter-btn.active .filter-counter {
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

/* Team Search Enhancement */
body.dark-mode .team-search input {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.05);
  color: var(--text-white);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

body.dark-mode .team-search input:focus {
  box-shadow: 0 8px 20px rgba(99, 102, 241, 0.2);
  border-color: rgba(99, 102, 241, 0.3);
  background: rgba(255, 255, 255, 0.07);
}

body.dark-mode .team-search i {
  color: rgba(255, 255, 255, 0.5);
}

/* ======================================
   MEMBER PROFILE PAGE - DARK MODE
====================================== */

/* Profile page background */
body.dark-mode .member-profile-section {
  background: linear-gradient(135deg, #151c2c 0%, #0f172a 100%);
  color: var(--text-white);
}

/* Profile back button */
body.dark-mode .profile-back-button a {
  background-color: rgba(30, 41, 59, 0.8);
  color: #a0b3ff;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

body.dark-mode .profile-back-button a:hover {
  background-color: rgba(67, 97, 238, 0.2);
  transform: translateX(-5px);
}

/* Profile header card */
body.dark-mode .profile-header-card {
  background: linear-gradient(145deg, #242f42, #1c2534);
  border: 1px solid rgba(255, 255, 255, 0.03);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

body.dark-mode .profile-bg-gradient {
  background: linear-gradient(
    135deg,
    rgba(99, 102, 241, 0.2),
    rgba(67, 97, 238, 0.1)
  );
  opacity: 1;
}

body.dark-mode .profile-avatar {
  background: linear-gradient(135deg, #4f46e5, #6366f1);
  border: 4px solid rgba(30, 41, 59, 0.7);
  box-shadow: 0 10px 25px rgba(67, 97, 238, 0.4);
}

body.dark-mode .profile-basic-info h1 {
  color: var(--text-white);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

body.dark-mode .profile-basic-info .member-role {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  box-shadow: 0 8px 15px rgba(67, 97, 238, 0.3);
}

body.dark-mode .member-team {
  color: var(--text-light);
}

body.dark-mode .member-team i {
  color: #a0b3ff;
}

/* Profile details card */
body.dark-mode .profile-details-card {
  background: linear-gradient(145deg, #242f42, #1c2534);
  border: 1px solid rgba(255, 255, 255, 0.03);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25);
}

/* Profile tabs */
body.dark-mode .profile-tabs {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  background: rgba(15, 23, 42, 0.3);
}

body.dark-mode .profile-tab {
  color: var(--text-light);
}

body.dark-mode .profile-tab:hover {
  color: #a0b3ff;
  background-color: rgba(67, 97, 238, 0.1);
}

body.dark-mode .profile-tab.active {
  color: #a0b3ff;
}

body.dark-mode .profile-tab.active::after {
  background: linear-gradient(to right, #6366f1, #4f46e5);
  box-shadow: 0 0 8px rgba(99, 102, 241, 0.3);
}

/* Profile content */
body.dark-mode .profile-tab-content {
  color: var(--text-light);
}

body.dark-mode .profile-section h2 {
  color: var(--text-white);
}

body.dark-mode .profile-section h2 i {
  color: #a0b3ff;
}

/* About section */
body.dark-mode #memberBio {
  color: var(--text-light);
  line-height: 1.7;
}

body.dark-mode .profile-list {
  color: var(--text-light);
}

body.dark-mode .profile-list li {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

body.dark-mode .profile-list li::before {
  color: #a0b3ff;
}

/* Skills section */
body.dark-mode .skill-label {
  color: var(--text-white);
}

body.dark-mode .meter-bar {
  background-color: rgba(255, 255, 255, 0.05);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

body.dark-mode .meter-fill {
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  box-shadow: 0 0 10px rgba(99, 102, 241, 0.4);
}

body.dark-mode .meter-fill::before {
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.3),
    rgba(255, 255, 255, 0.1)
  );
}

body.dark-mode .meter-fill span {
  background: #1e293b;
  border: 1px solid rgba(99, 102, 241, 0.3);
  color: #a0b3ff;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

/* Projects section */
body.dark-mode .project-contribution {
  background-color: rgba(67, 97, 238, 0.08);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-left: 4px solid rgba(99, 102, 241, 0.5);
}

body.dark-mode .project-contribution h3 {
  color: #a0b3ff;
}

body.dark-mode .project-contribution p {
  color: var(--text-light);
}

/* Tasks section */
body.dark-mode .task-item {
  background-color: rgba(67, 97, 238, 0.08);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-left: 4px solid rgba(99, 102, 241, 0.5);
}

body.dark-mode .task-item:hover {
  background-color: rgba(67, 97, 238, 0.12);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

body.dark-mode .task-title {
  color: var(--text-white);
}

body.dark-mode .task-title i {
  color: #a0b3ff;
}

body.dark-mode .task-status.completed {
  background-color: rgba(52, 199, 89, 0.2);
  color: #98ebc0;
}

body.dark-mode .task-status.in-progress {
  background-color: rgba(255, 149, 0, 0.2);
  color: #ffcf80;
}

body.dark-mode .task-status.pending {
  background-color: rgba(142, 142, 147, 0.2);
  color: #ccc;
}

body.dark-mode .task-details {
  color: var(--text-light);
}

body.dark-mode .task-meta {
  color: var(--text-light);
}

body.dark-mode .task-meta-item i {
  color: #a0b3ff;
}

/* Contact section */
body.dark-mode .contact-item {
  background: linear-gradient(145deg, #1f2937, #151c2c);
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25), 0 4px 8px rgba(0, 0, 0, 0.15);
}

body.dark-mode .contact-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4), 0 0 15px rgba(67, 97, 238, 0.2);
  background: linear-gradient(145deg, #242f42, #1c2534);
  border: 1px solid rgba(67, 97, 238, 0.15);
}

body.dark-mode .contact-icon {
  background: linear-gradient(
    135deg,
    rgba(99, 102, 241, 0.2),
    rgba(79, 70, 229, 0.2)
  );
  color: #a5b4fc;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(99, 102, 241, 0.1);
}

body.dark-mode .contact-item:hover .contact-icon {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  color: white;
  transform: scale(1.15) rotate(10deg);
  box-shadow: 0 10px 25px rgba(67, 97, 238, 0.4),
    0 0 0 2px rgba(99, 102, 241, 0.25);
  animation: pulseGlow 2s infinite;
}

@keyframes pulseGlow {
  0% {
    box-shadow: 0 8px 20px rgba(67, 97, 238, 0.3),
      0 0 0 1px rgba(99, 102, 241, 0.2);
  }
  50% {
    box-shadow: 0 8px 25px rgba(67, 97, 238, 0.5),
      0 0 0 1px rgba(99, 102, 241, 0.3), 0 0 20px rgba(103, 151, 255, 0.5);
  }
  100% {
    box-shadow: 0 8px 20px rgba(67, 97, 238, 0.3),
      0 0 0 1px rgba(99, 102, 241, 0.2);
  }
}

body.dark-mode .contact-info {
  color: #c3cbdc;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

body.dark-mode .contact-item:hover .contact-info {
  color: #f0f4f8;
}
body.dark-mode .contact-item {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

body.dark-mode .contact-icon {
  background: linear-gradient(
    135deg,
    rgba(99, 102, 241, 0.2),
    rgba(79, 70, 229, 0.2)
  );
  color: #a0b3ff;
}

body.dark-mode .contact-info {
  color: var(--text-light);
}

body.dark-mode .profile-social h3 {
  color: var(--text-white);
}

body.dark-mode .social-link {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.05);
  color: #a0b3ff;
}

body.dark-mode .social-link:hover {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  color: white;
  box-shadow: 0 10px 20px rgba(67, 97, 238, 0.3);
}

/* No tasks message */
body.dark-mode .no-tasks {
  color: var(--text-light);
  font-style: italic;
  text-align: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 10px;
  border: 1px dashed rgba(255, 255, 255, 0.1);
}
