function optimizePagePerformance() {
    // Implement lazy loading for images
    if ("loading" in HTMLImageElement.prototype) {
        const images = document.querySelectorAll("img:not([loading])");
        images.forEach((img) => {
            img.loading = "lazy";
        });
    } else {
        // Fallback for browsers that don't support native lazy loading
        const script = document.createElement("script");
        script.src =
            "https://cdnjs.cloudflare.com/ajax/libs/lazysizes/5.3.2/lazysizes.min.js";
        document.body.appendChild(script);

        // Add lazysizes class to images
        const images = document.querySelectorAll("img:not(.lazyload)");
        images.forEach((img) => {
            img.classList.add("lazyload");
            img.setAttribute("data-src", img.src);
            img.src =
                "data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==";
        });
    }

    // Implement resource hints for faster page loads
    const links = [
        {rel: "preconnect", href: "https://cdnjs.cloudflare.com"},
        {rel: "preconnect", href: "https://fonts.googleapis.com"},
        {rel: "preconnect", href: "https://fonts.gstatic.com", crossorigin: true},
    ];

    links.forEach((link) => {
        const linkEl = document.createElement("link");
        linkEl.rel = link.rel;
        linkEl.href = link.href;
        if (link.crossorigin) linkEl.crossorigin = link.crossorigin;
        document.head.appendChild(linkEl);
    });
}
