/* Sprint Progress Enhanced Styling */
.sprint-progress-section {
    --primary-gradient: linear-gradient(135deg, #4361ee, #4cc9f0);
    --card-shadow-hover: 0 15px 35px rgba(0, 0, 0, 0.1),
    0 3px 10px rgba(0, 0, 0, 0.05);
    --card-transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275),
    box-shadow 0.4s ease;
    position: relative;
    overflow: hidden;
    padding: 100px 0 120px;
    background-color: var(--bg-light);
}

/* Enhanced background with animated particles */
.sprint-particles-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 0;
}

.particle {
    position: absolute;
    border-radius: 50%;
    background-color: var(--primary);
    opacity: 0.07;
    animation: float 20s infinite ease-in-out;
}

.particle:nth-child(1) {
    width: 300px;
    height: 300px;
    top: -100px;
    left: -100px;
    animation-delay: 0s;
}

.particle:nth-child(2) {
    width: 200px;
    height: 200px;
    top: 50%;
    right: -50px;
    animation-delay: 3s;
}

.particle:nth-child(3) {
    width: 150px;
    height: 150px;
    bottom: -50px;
    left: 30%;
    animation-delay: 5s;
}

.particle:nth-child(4) {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 20%;
    animation-delay: 8s;
}

@keyframes float {
    0%,
    100% {
        transform: translateY(0) translateX(0);
    }
    25% {
        transform: translateY(-30px) translateX(20px);
    }
    50% {
        transform: translateY(-15px) translateX(-20px);
    }
    75% {
        transform: translateY(30px) translateX(10px);
    }
}

/* Enhanced section title */
.sprint-progress-section .section-title {
    position: relative;
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    z-index: 1;
    text-align: center;
}

.sprint-progress-section .section-title::after {
    content: "";
    display: block;
    width: 80px;
    height: 4px;
    background: var(--primary-gradient);
    margin: 15px auto 0;
    border-radius: 2px;
}

.sprint-intro {
    max-width: 700px;
    margin: 0 auto 50px;
    text-align: center;
    font-size: 1.1rem;
    color: var(--text-light);
    line-height: 1.7;
}

/* Enhanced sprint overview */
.sprint-overview {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-bottom: 60px;
    position: relative;
    z-index: 1;
}

/* Enhanced chart container */
.sprint-chart-container {
    flex: 1;
    min-width: 300px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    padding: 25px;
    transition: var(--card-transition);
    border: 1px solid rgba(0, 0, 0, 0.03);
    overflow: hidden;
    position: relative;
}

.sprint-chart-container:hover {
    box-shadow: var(--card-shadow-hover);
    transform: translateY(-7px);
}

.sprint-chart-container::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--primary-gradient);
    z-index: 1;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.chart-header h3 {
    position: relative;
    margin: 0;
    font-size: 1.3rem;
    color: var(--text-dark);
    font-weight: 600;
}

.chart-controls select {
    padding: 8px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    font-size: 0.9rem;
    transition: all 0.2s ease;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.chart-controls select:hover {
    border-color: var(--primary);
}

/* Enhanced metric cards */
.sprint-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 20px;
    flex: 1;
    min-width: 300px;
}

.metric-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.06);
    padding: 25px;
    text-align: center;
    transition: var(--card-transition);
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.03);
    overflow: hidden;
    transform-style: preserve-3d;
    transform: perspective(1000px);
}

.metric-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary);
    opacity: 0.7;
}

.metric-card::after {
    content: "";
    position: absolute;
    bottom: 0;
    right: 0;
    width: 80px;
    height: 80px;
    background: var(--primary);
    opacity: 0.03;
    border-radius: 50% 0 0 0;
    z-index: 0;
}

.metric-card:hover {
    transform: translateY(-8px) perspective(1000px);
    box-shadow: var(--card-shadow-hover);
}

.metric-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(67, 97, 238, 0.1);
    color: var(--primary);
    font-size: 1.4rem;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.metric-card:hover .metric-icon {
    transform: scale(1.1);
    background: var(--primary-gradient);
    color: white;
}

.pulse {
    animation: pulse-animation 2s infinite;
}

@keyframes pulse-animation {
    0% {
        box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.3);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(67, 97, 238, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(67, 97, 238, 0);
    }
}

.metric-card h3 {
    margin: 0 0 15px;
    font-size: 1.1rem;
    color: var(--text-light);
    font-weight: 500;
    position: relative;
    z-index: 1;
}

.metric-value {
    font-size: 1.8rem;
    font-weight: 700;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    margin-bottom: 15px;
    line-height: 1;
    position: relative;
    z-index: 1;
}

.metric-progress {
    margin-top: 15px;
    position: relative;
    z-index: 1;
}

.metric-progress-bar {
    height: 8px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.metric-progress-fill {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: 4px;
    width: 0;
    transition: width 1.8s cubic-bezier(0.1, 0.5, 0.2, 1);
}

.metric-progress-text {
    font-size: 0.9rem;
    color: var(--text-light);
}

/* Highlight card special styling */
.highlight-card {
    background: var(--primary-gradient);
    color: white;
}

.highlight-card::before {
    background: white;
}

.highlight-card h3,
.highlight-title {
    color: rgba(255, 255, 255, 0.9);
}

.highlight-card .metric-value {
    color: white;
    -webkit-text-fill-color: white;
}

.highlight-card .metric-icon {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.highlight-card:hover .metric-icon {
    background: white;
    color: var(--primary);
}

.velocity-trend {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.9);
}

.trend-icon {
    margin-right: 8px;
}

.trend-icon.positive {
    color: #ffffff;
}

.trend-value {
    font-weight: 600;
    margin-right: 5px;
}

/* Enhanced Sprint Tabs with Navigation */
.sprint-tabs-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    background: linear-gradient(
            to right,
            rgba(245, 247, 250, 0.8),
            rgba(240, 242, 247, 0.8)
    );
    box-shadow: 0 -5px 15px rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    padding: 5px 10px;
}

.sprint-nav-button {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-gradient);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    color: white;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    z-index: 10;
    box-shadow: 0 3px 10px rgba(67, 97, 238, 0.3);
    margin: 0 5px;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.sprint-nav-button:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 5px 15px rgba(67, 97, 238, 0.4);
}

.sprint-nav-button:hover::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
}

.sprint-nav-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.sprint-nav-button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #e0e0e0;
    color: #999;
    box-shadow: none;
}

.sprint-nav-button.disabled:hover {
    transform: none;
    box-shadow: none;
}

.sprint-tabs {
    display: flex;
    overflow-x: hidden;
    background: transparent;
    scrollbar-width: thin;
    scrollbar-color: var(--primary) transparent;
    position: relative;
    padding: 0 5px;
    z-index: 2;
    flex: 1;
    scroll-behavior: smooth;
    width: calc(130px * 8); /* Show exactly 8 tabs (approximate width) */
    max-width: 100%;
}

.sprint-tabs::before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 30px;
    background: linear-gradient(
            to right,
            transparent,
            rgba(240, 242, 247, 0.9) 70%
    );
    z-index: 3;
    pointer-events: none;
    opacity: 0.8;
}

.sprint-tabs::-webkit-scrollbar {
    height: 4px;
}

.sprint-tabs::-webkit-scrollbar-thumb {
    background-color: var(--primary);
    border-radius: 2px;
}

.sprint-tab {
    padding: 16px 20px;
    background: transparent;
    border: none;
    cursor: pointer;
    font-weight: 500;
    color: var(--text-light);
    transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
    white-space: nowrap;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    overflow: hidden;
    margin: 0 2px;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    font-size: 0.95rem;
    letter-spacing: 0.2px;
    min-width: 120px;
}

.sprint-tab::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 0;
    background: var(--primary-gradient);
    opacity: 0;
    transition: all 0.3s ease;
}

.sprint-tab:not(.disabled):hover::before {
    opacity: 0.05;
    height: 100%;
}

.tab-status {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    transition: all 0.3s ease;
    position: relative;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.7);
    flex-shrink: 0; /* Prevent the status indicator from shrinking */
}

.tab-status.completed {
    background: var(--success);
}

.tab-status.completed::after {
    content: "✓";
    position: absolute;
    font-size: 8px;
    color: white;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-weight: bold;
}

.tab-status.in-progress {
    background: var(--warning);
    animation: pulse-status 2s infinite;
}

@keyframes pulse-status {
    0% {
        box-shadow: 0 0 0 0 rgba(249, 199, 79, 0.7),
        0 0 0 2px rgba(255, 255, 255, 0.7);
    }
    70% {
        box-shadow: 0 0 0 8px rgba(249, 199, 79, 0),
        0 0 0 2px rgba(255, 255, 255, 0.7);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(249, 199, 79, 0),
        0 0 0 2px rgba(255, 255, 255, 0.7);
    }
}

.tab-status.upcoming {
    background: var(--text-light);
    opacity: 0.5;
}

.sprint-tab.active {
    background: white;
    color: var(--primary);
    font-weight: 600;
    box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
}

.sprint-tab.active .tab-status {
    transform: scale(1.2);
}

.sprint-tab.active::after {
    content: "";
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--primary-gradient);
    z-index: 1;
    animation: slideIn 0.4s cubic-bezier(0.19, 1, 0.22, 1) forwards;
}

@keyframes slideIn {
    from {
        transform: scaleX(0);
        opacity: 0;
    }
    to {
        transform: scaleX(1);
        opacity: 1;
    }
}

.sprint-tab:not(.active):not(.disabled):hover {
    background: rgba(255, 255, 255, 0.7);
    color: var(--primary);
    transform: translateY(-1px);
}

.sprint-tab.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    color: var(--text-light);
    filter: grayscale(0.4);
}

/* Ripple effect */
.tab-ripple {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.7);
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
    z-index: 0;
}

@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Tab content styling */
.tab-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    line-height: 1.2;
}

.tab-title {
    font-weight: 600;
}

.tab-meta {
    font-size: 0.75rem;
    opacity: 0.8;
    margin-top: 3px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.tab-completion {
    display: inline-block;
    padding: 1px 5px;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 600;
    background: rgba(114, 196, 151, 0.15);
    color: var(--success);
}

.sprint-tab:nth-child(4) .tab-completion {
    background: rgba(249, 199, 79, 0.15);
    color: var(--warning);
}

/* Tooltip on hover */
.sprint-tab {
    position: relative;
}

.sprint-tab:not(.disabled)::after {
    content: "";
    position: absolute;
    bottom: -1px;
    left: 0;
    height: 3px;
    width: 0;
    background: var(--primary-gradient);
    transition: width 0.3s ease;
    z-index: 1;
    opacity: 0.7;
}

.sprint-tab:not(.disabled):hover::after {
    width: 100%;
}

.sprint-tab.active::after {
    width: 100%;
    opacity: 1;
}

/* Sprint tabs enhancement */
.sprint-details-container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    position: relative;
    z-index: 1;
    max-width: 1100px;
    margin: 0 auto;
    border: 1px solid rgba(0, 0, 0, 0.03);
}

.sprint-detail {
    display: none;
    padding: 35px;
    opacity: 0;
    transition: opacity 0.4s ease, transform 0.4s ease;
}

.sprint-detail.active {
    display: block;
    opacity: 1;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced sprint header */
.sprint-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding-bottom: 25px;
    margin-bottom: 30px;
}

.sprint-title-section {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-bottom: 15px;
    gap: 20px;
}

.sprint-title-section h3 {
    margin: 0;
    flex: 1;
    color: var(--text-dark);
    font-size: 1.6rem;
    position: relative;
    font-weight: 600;
}

.sprint-badges {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.sprint-status {
    padding: 8px 16px;
    border-radius: 30px;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
}

.sprint-status.completed {
    background: rgba(114, 196, 151, 0.15);
    color: var(--success);
}

.sprint-status.in-progress {
    background: rgba(249, 199, 79, 0.15);
    color: var(--warning);
}

.sprint-timeframe {
    color: var(--text-light);
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.sprint-summary {
    color: var(--text-light);
    font-size: 1.05rem;
    line-height: 1.6;
    margin-top: 8px;
}

/* Enhanced sprint stats */
.sprint-stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.sprint-stat {
    background: linear-gradient(145deg, #ffffff, #f5f7ff);
    border-radius: 16px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);
    padding: 25px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    transform-style: preserve-3d;
}

.sprint-stat::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--primary-gradient);
    opacity: 0.8;
}

.sprint-stat:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.highlight-stat {
    background: linear-gradient(
            135deg,
            rgba(76, 201, 240, 0.1) 0%,
            rgba(67, 97, 238, 0.1) 100%
    );
    border: 1px solid rgba(67, 97, 238, 0.1);
}

.sprint-stat .stat-number {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 12px;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
}

.sprint-stat .stat-label {
    font-size: 0.95rem;
    color: var(--text-light);
    font-weight: 500;
}

/* Content grid enhancement */
.sprint-content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 40px;
}

.task-distribution,
.key-achievements {
    background: #ffffff;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.03);
    position: relative;
    overflow: hidden;
}

.task-distribution::before,
.key-achievements::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--primary-gradient);
    opacity: 0.8;
}

.task-distribution:hover,
.key-achievements:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.task-distribution h4,
.key-achievements h4 {
    margin-top: 0;
    margin-bottom: 25px;
    color: var(--text-dark);
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
}

.distribution-chart-container {
    height: 260px;
    margin-top: 20px;
}

/* Achievement list enhancement */
.achievement-list {
    padding-left: 0;
    list-style: none;
}

.achievement-item {
    padding: 12px 0;
    display: flex;
    align-items: flex-start;
    gap: 12px;
    color: var(--text-light);
    border-bottom: 1px dashed rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.achievement-item:last-child {
    border-bottom: none;
}

.achievement-item:hover {
    transform: translateX(5px);
    color: var(--text-dark);
}

.achievement-check {
    color: var(--success);
    font-size: 1.2rem;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Document links enhancement */
.sprint-documents {
    margin-top: 30px;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.document-link {
    background: linear-gradient(145deg, #ffffff, #f5f7ff);
    padding: 10px 18px;
    border-radius: 30px;
    font-size: 0.95rem;
    color: var(--primary);
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    text-decoration: none;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.document-link:hover {
    background: var(--primary-gradient);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(67, 97, 238, 0.2);
}

/* Health dashboard enhancement */
.sprint-health {
    margin-top: 40px;
    background: #ffffff;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.03);
    position: relative;
}

.sprint-health::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--primary-gradient);
    opacity: 0.8;
}

.sprint-health:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.sprint-health h4 {
    margin-top: 0;
    margin-bottom: 25px;
    color: var(--text-dark);
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
}

.health-metrics-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    gap: 30px;
    margin-top: 20px;
    margin-bottom: 40px;
}

.health-metric {
    min-width: 130px;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: transform 0.3s ease;
}

.health-metric:hover {
    transform: translateY(-8px);
}

.metric-gauge {
    width: 130px;
    max-width: 100%;
    position: relative;
    margin-bottom: 15px;
}

.gauge {
    overflow: visible;
    filter: drop-shadow(0 5px 15px rgba(0, 0, 0, 0.1));
}

.gauge-bg {
    fill: none;
    stroke: rgba(0, 0, 0, 0.05);
    stroke-width: 10;
}

.gauge-fill {
    fill: none;
    stroke: var(--primary);
    stroke-width: 10;
    stroke-linecap: round;
    transform-origin: center;
    transform: rotate(-90deg);
    stroke-dasharray: 314;
    transition: stroke-dashoffset 1.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.gauge-fill.quality {
    stroke: #4cc9f0;
}

.gauge-fill.velocity {
    stroke: #f25c54;
}

.gauge-fill.satisfaction {
    stroke: #72c497;
}

.gauge-value {
    font-size: 22px;
    font-weight: 700;
    fill: var(--text-dark);
}

.gauge-label {
    text-align: center;
    font-size: 0.95rem;
    color: var(--text-light);
    font-weight: 500;
}

/* Sprint insights enhancement */
.sprint-insights {
    background: white;
    border-radius: 16px;
    padding: 25px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.03);
}

.sprint-insights:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.insight-title {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 20px;
    font-size: 1.1rem;
}

.insights-list {
    padding-left: 0;
    list-style: none;
    margin: 0;
}

.insights-list li {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
    color: var(--text-light);
    border-bottom: 1px dashed rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.insights-list li:hover {
    transform: translateX(5px);
    color: var(--text-dark);
}

.insights-list li:last-child {
    border-bottom: none;
}

.insight-badge {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    flex-shrink: 0;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.insight-badge.positive {
    background-color: rgba(114, 196, 151, 0.15);
    color: #72c497;
}

.insight-badge.neutral {
    background-color: rgba(76, 201, 240, 0.15);
    color: #4cc9f0;
}

.insight-badge.negative {
    background-color: rgba(242, 92, 84, 0.15);
    color: #f25c54;
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .sprint-content-grid {
        grid-template-columns: 1fr;
    }

    .sprint-progress-section .section-title {
        font-size: 2.2rem;
    }
}

@media (max-width: 768px) {
    .sprint-progress-section {
        padding: 70px 0 90px;
    }

    .sprint-progress-section .section-title {
        font-size: 2rem;
    }

    .sprint-intro {
        font-size: 1rem;
        margin-bottom: 40px;
    }

    .sprint-stats-row {
        gap: 15px;
    }

    .sprint-stat {
        padding: 20px 15px;
    }

    .stat-number {
        font-size: 1.7rem;
    }

    .sprint-title-section {
        flex-direction: column;
        align-items: flex-start;
    }

    .sprint-badges {
        width: 100%;
    }

    .sprint-detail {
        padding: 25px 20px;
    }

    .health-metrics-container {
        gap: 20px;
    }

    .task-distribution,
    .key-achievements,
    .sprint-health {
        padding: 25px 20px;
    }
}

@media (max-width: 576px) {
    .sprint-progress-section {
        padding: 60px 0 80px;
    }

    .sprint-progress-section .section-title {
        font-size: 1.8rem;
    }

    .sprint-intro {
        margin-bottom: 30px;
    }

    .metric-card {
        padding: 20px 15px;
    }

    .sprint-tabs-wrapper {
        padding: 0;
    }

    .sprint-nav-button {
        width: 32px;
        height: 32px;
        margin: 0 5px;
        font-size: 0.8rem;
    }

    .sprint-tabs {
        padding: 0 2px;
    }

    .sprint-tab {
        padding: 12px 15px;
        font-size: 0.85rem;
    }

    .tab-status {
        width: 8px;
        height: 8px;
    }

    .health-metrics-container {
        flex-direction: column;
        align-items: center;
    }

    .health-metric {
        width: 100%;
        max-width: 180px;
    }

    .sprint-header {
        padding-bottom: 20px;
        margin-bottom: 25px;
    }

    .sprint-title-section h3 {
        font-size: 1.4rem;
    }
}
