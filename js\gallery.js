/**
 * Gallery functionality
 */

document.addEventListener("DOMContentLoaded", function () {
    // Preload gallery images for smoother experience
    const preloadGalleryImages = () => {
        const galleryImages = document.querySelectorAll(
            ".gallery-img-container img"
        );
        galleryImages.forEach((img) => {
            // Set image loading attribute for better performance
            img.loading = "eager";

            // Add loading state
            const container = img.closest(".gallery-img-container");
            container.classList.add("loading");

            img.onload = () => {
                container.classList.remove("loading");
                container.classList.add("loaded");
            };

            img.onerror = () => {
                container.classList.remove("loading");
                container.classList.add("error");
            };
        });
    };

    preloadGalleryImages();

    // Initialize gallery tabs with ARIA attributes
    const galleryTabs = document.querySelectorAll(".gallery-tab");
    const galleryItems = document.querySelectorAll(".gallery-item");

    // Set ARIA roles for accessibility
    galleryTabs.forEach((tab) => {
        tab.setAttribute("role", "tab");
        tab.setAttribute("tabindex", "0");

        // Allow keyboard activation
        tab.addEventListener("keypress", (e) => {
            if (e.key === "Enter" || e.key === " ") {
                e.preventDefault();
                tab.click();
            }
        });
    });

    // Create better animation effect with GPU acceleration
    const animateGalleryItem = (item, show) => {
        if (show) {
            item.style.display = "block";
            item.style.opacity = "0";
            item.style.transform = "translateY(20px)";

            // Use requestAnimationFrame for smoother animations
            requestAnimationFrame(() => {
                requestAnimationFrame(() => {
                    item.style.opacity = "1";
                    item.style.transform = "translateY(0)";
                    item.style.transition =
                        "opacity 0.4s ease, transform 0.5s cubic-bezier(0.1, 0.9, 0.2, 1)";
                });
            });
        } else {
            item.style.opacity = "0";
            item.style.transform = "translateY(20px)";

            // Hide after transition with a clean up
            const transitionEnded = () => {
                item.style.display = "none";
                item.removeEventListener("transitionend", transitionEnded);
            };

            item.addEventListener("transitionend", transitionEnded);
        }
    };

    // Filter gallery items when tab is clicked - optimized version
    galleryTabs.forEach((tab) => {
        tab.addEventListener("click", () => {
            // Update active tab and ARIA
            galleryTabs.forEach((t) => {
                t.classList.remove("active");
                t.setAttribute("aria-selected", "false");
            });

            tab.classList.add("active");
            tab.setAttribute("aria-selected", "true");

            const filterValue = tab.getAttribute("data-gallery");

            // Batch DOM operations for better performance
            requestAnimationFrame(() => {
                // Filter gallery items with optimized animation
                galleryItems.forEach((item) => {
                    if (filterValue === "all" || item.classList.contains(filterValue)) {
                        animateGalleryItem(item, true);
                    } else {
                        animateGalleryItem(item, false);
                    }
                });
            });
        });
    });

    // Initialize image zoom functionality
    const galleryZoomButtons = document.querySelectorAll(".gallery-zoom");

    galleryZoomButtons.forEach((button) => {
        button.addEventListener("click", (e) => {
            e.stopPropagation();

            // Get the parent gallery item
            const galleryItem = button.closest(".gallery-item");
            const imgSrc = galleryItem.querySelector("img").src;
            const imgTitle = galleryItem.querySelector(".gallery-title").textContent;

            // Create and display lightbox
            createLightbox(imgSrc, imgTitle);
        });
    });

    // Enhance the createLightbox function
    function createLightbox(imgSrc, imgTitle) {
        // Create basic elements
        const lightbox = document.createElement("div");
        lightbox.className = "lightbox";

        // Add zoom controls
        const zoomControls = document.createElement("div");
        zoomControls.className = "zoom-controls";
        zoomControls.innerHTML = `
      <button class="zoom-btn zoom-in" aria-label="Zoom in"><i class="fas fa-search-plus"></i></button>
      <button class="zoom-btn zoom-out" aria-label="Zoom out"><i class="fas fa-search-minus"></i></button>
      <button class="zoom-btn zoom-reset" aria-label="Reset zoom"><i class="fas fa-undo"></i></button>
    `;

        // Create lightbox elements
        const lightboxContent = document.createElement("div");
        lightboxContent.className = "lightbox-content";

        const closeBtn = document.createElement("div");
        closeBtn.className = "lightbox-close";
        closeBtn.innerHTML = '<i class="fas fa-times"></i>';

        const img = document.createElement("img");
        img.src = imgSrc;
        img.alt = imgTitle;

        const caption = document.createElement("div");
        caption.className = "lightbox-caption";
        caption.textContent = imgTitle;

        // Assemble lightbox
        lightboxContent.appendChild(closeBtn);
        lightboxContent.appendChild(img);
        lightboxContent.appendChild(caption);
        lightboxContent.appendChild(zoomControls);
        lightbox.appendChild(lightboxContent);

        // Add to document
        document.body.appendChild(lightbox);

        // Add lightbox styles
        lightbox.style.position = "fixed";
        lightbox.style.top = "0";
        lightbox.style.left = "0";
        lightbox.style.width = "100%";
        lightbox.style.height = "100%";
        lightbox.style.background = "rgba(0,0,0,0.9)";
        lightbox.style.display = "flex";
        lightbox.style.alignItems = "center";
        lightbox.style.justifyContent = "center";
        lightbox.style.zIndex = "1100";
        lightbox.style.opacity = "0";
        lightbox.style.transition = "opacity 0.3s ease";

        lightboxContent.style.position = "relative";
        lightboxContent.style.maxWidth = "90%";
        lightboxContent.style.maxHeight = "90%";

        img.style.maxWidth = "100%";
        img.style.maxHeight = "80vh";
        img.style.display = "block";
        img.style.boxShadow = "0 0 30px rgba(0,0,0,0.5)";

        caption.style.color = "white";
        caption.style.textAlign = "center";
        caption.style.padding = "10px 0";
        caption.style.fontWeight = "500";

        closeBtn.style.position = "absolute";
        closeBtn.style.top = "-15px";
        closeBtn.style.right = "-15px";
        closeBtn.style.width = "35px";
        closeBtn.style.height = "35px";
        closeBtn.style.backgroundColor = "white";
        closeBtn.style.borderRadius = "50%";
        closeBtn.style.display = "flex";
        closeBtn.style.alignItems = "center";
        closeBtn.style.justifyContent = "center";
        closeBtn.style.cursor = "pointer";
        closeBtn.style.boxShadow = "0 2px 10px rgba(0,0,0,0.2)";
        closeBtn.style.zIndex = "1200";

        // Force reflow
        lightbox.offsetHeight;

        // Show with animation
        requestAnimationFrame(() => {
            lightbox.style.opacity = "1";
        });

        // Add ARIA attributes for accessibility
        lightbox.setAttribute("role", "dialog");
        lightbox.setAttribute("aria-modal", "true");
        lightbox.setAttribute("aria-labelledby", "lightbox-title");

        caption.id = "lightbox-title";

        closeBtn.setAttribute("aria-label", "Close lightbox");
        closeBtn.setAttribute("role", "button");
        closeBtn.setAttribute("tabindex", "0");

        // Add event listeners
        closeBtn.addEventListener("click", closeLightbox);
        lightbox.addEventListener("click", (e) => {
            if (e.target === lightbox) {
                closeLightbox();
            }
        });
        document.addEventListener("keydown", (e) => {
            if (e.key === "Escape") {
                closeLightbox();
            }
        });

        // Support keyboard navigation and focus management
        closeBtn.focus(); // Set initial focus to close button

        function closeLightbox() {
            lightbox.style.opacity = "0";
            setTimeout(() => {
                document.body.removeChild(lightbox);
            }, 300);
        }

        // Add zoom functionality
        let scale = 1;
        const zoomIn = lightbox.querySelector(".zoom-in");
        const zoomOut = lightbox.querySelector(".zoom-out");
        const zoomReset = lightbox.querySelector(".zoom-reset");

        zoomIn.addEventListener("click", () => {
            scale += 0.25;
            img.style.transform = `scale(${scale})`;
        });

        zoomOut.addEventListener("click", () => {
            if (scale > 0.5) scale -= 0.25;
            img.style.transform = `scale(${scale})`;
        });

        zoomReset.addEventListener("click", () => {
            scale = 1;
            img.style.transform = `scale(${scale})`;
        });
    }

    // Enhance gallery functionality
    enhanceGallery();
});

function enhanceGallery() {
    const galleryGrid = document.querySelector(".gallery-grid");
    const filterBtns = document.querySelectorAll(".gallery-tab");

    // Add isotope.js-like filtering
    const galleryItems = document.querySelectorAll(".gallery-item");

    // Create category badges on items
    galleryItems.forEach((item) => {
        const category = item.getAttribute("data-category");
        const badge = document.createElement("span");
        badge.className = "category-badge";
        badge.textContent = category;
        item.querySelector(".gallery-img-container").appendChild(badge);
    });

    // Add styles for badges
    const style = document.createElement("style");
    style.textContent = `
    .category-badge {
      position: absolute;
      top: 10px;
      left: 10px;
      background: var(--primary);
      color: white;
      padding: 3px 10px;
      font-size: 0.7rem;
      border-radius: 20px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
      opacity: 0.8;
      z-index: 2;
    }
    .gallery-grid {
      transition: height 0.5s ease-out;
    }
  `;
    document.head.appendChild(style);
}
