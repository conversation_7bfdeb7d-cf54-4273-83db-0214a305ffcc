/* Enhanced Stats Component
   Provides additional refinements to the stat cards for a polished look
*/

/* Improved card glow effect on hover */
.stat-card::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle at 50% 0%,
    rgba(255, 255, 255, 0.2),
    transparent 60%
  );
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none;
  z-index: 0;
}

.stat-card:hover::after {
  opacity: 1;
}

/* Subtle shine effect on the stat number */
.stat-number::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 80%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transform: skewX(-25deg);
  transition: all 0.75s ease;
  opacity: 0;
}

.stat-card:hover .stat-number::after {
  left: 120%;
  opacity: 1;
}

/* Stylish particle animation for counter completion */
.counter-particle {
  border-radius: 50%;
  filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.4));
}

/* Enhanced counter animation */
@keyframes number-celebrate {
  0% {
    transform: scale(1);
    text-shadow: 0 0 0 rgba(255, 255, 255, 0);
  }
  50% {
    transform: scale(1.12);
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
  }
  100% {
    transform: scale(1);
    text-shadow: 0 0 0 rgba(255, 255, 255, 0);
  }
}

/* Icon animation */
@keyframes subtle-pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
}

.stat-icon i {
  transition: all 0.4s ease;
}

.stat-card:hover .stat-icon i {
  animation: subtle-pulse 2s infinite;
}

/* Add a responsive adjustment for better display on mobile */
@media (max-width: 768px) {
  .hero-stats-container {
    gap: 20px;
  }

  .stat-card {
    padding: 22px 18px;
    min-width: 160px;
  }

  .stat-icon {
    width: 56px;
    height: 56px;
    font-size: 1.4rem;
    margin-bottom: 15px;
  }

  .stat-number {
    font-size: 2.4rem;
  }

  .stat-label {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .hero-stats-container {
    gap: 15px;
  }

  .stat-card {
    padding: 18px 15px;
    min-width: 140px;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 1.3rem;
    margin-bottom: 12px;
  }

  .stat-number {
    font-size: 2.2rem;
  }
}
