{"id": "menna-allah-abd-<PERSON><PERSON><PERSON>-moh<PERSON><PERSON>", "name": "<PERSON><PERSON>", "role": "Mobile Developer", "team": "mobile", "contact": {"phone": "+201018896806", "email": "<EMAIL>"}, "bio": "<PERSON><PERSON> is a mobile developer with expertise in creating intuitive and responsive mobile applications. She specializes in UI/UX design and cross-platform development.", "education": ["B.S. Computer Science, Cairo University, 2022", "Mobile Application Development Certification, 2021"], "skills": [{"name": "Flutter", "level": 90}, {"name": "Dart", "level": 85}, {"name": "Mobile UI/UX", "level": 90}, {"name": "Firebase", "level": 80}, {"name": "State Management", "level": 85}], "projects": [{"name": "E-commerce Mobile App", "description": "Designed and developed a feature-rich e-commerce mobile application with seamless user experience and secure payment integration."}, {"name": "Offline-First Architecture", "description": "Implemented an offline-first architecture for mobile applications, ensuring functionality even with intermittent connectivity."}], "social": {"github": "https://github.com/00menna00", "linkedin": "https://www.linkedin.com/in/menna2024", "twitter": "", "instagram": "", "facebook": ""}, "tasks": [{"title": "Design Mobile UI Component Library", "description": "Create a comprehensive library of reusable UI components following Material Design guidelines with custom theming support.", "status": "Completed", "startDate": "02/05/2025", "endDate": "02/20/2025", "hours": 40, "category": "UI/UX", "icon": "palette", "complexity": "Medium"}, {"title": "Implement State Management Solution", "description": "Research and implement an efficient state management solution for complex application workflows with optimized performance.", "status": "Completed", "startDate": "02/22/2025", "endDate": "03/05/2025", "hours": 30, "category": "Architecture", "icon": "sitemap", "complexity": "High"}, {"title": "Create Offline Data Sync Module", "description": "Develop a robust offline data synchronization module that handles conflict resolution and ensures data integrity across devices.", "status": "In Progress", "startDate": "03/08/2025", "endDate": "03/20/2025", "hours": 35, "category": "Data Management", "icon": "sync-alt", "complexity": "High"}, {"title": "Implement Advanced Animations", "description": "Design and implement advanced UI animations and transitions to enhance user experience and provide visual feedback for interactions.", "status": "Pending", "startDate": "03/25/2025", "endDate": "04/05/2025", "hours": 25, "category": "UI/UX", "icon": "film", "complexity": "Medium"}]}