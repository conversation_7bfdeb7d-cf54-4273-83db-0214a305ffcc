document.addEventListener('DOMContentLoaded', function() {
  initializeMetrics();
  animateDemoBox();
});

function initializeMetrics() {
  // Get all circular progress elements
  const circleProgress = document.querySelectorAll('.circle-progress');
  
  // Set up Intersection Observer to animate them when in view
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const circle = entry.target.querySelector('.progress-ring-circle');
        const value = entry.target.getAttribute('data-value');
        
        if (circle && value) {
          const radius = circle.getAttribute('r');
          const circumference = 2 * Math.PI * radius;
          
          // Calculate the offset based on the percentage
          const offset = circumference - (value / 100) * circumference;
          
          // Apply the offset with an animation delay
          setTimeout(() => {
            circle.style.strokeDashoffset = offset;
          }, 300);
        }
        
        // Once animated, stop observing
        observer.unobserve(entry.target);
      }
    });
  }, {
    threshold: 0.2
  });
  
  // Observe each progress circle
  circleProgress.forEach(progress => {
    // Initialize stroke-dasharray
    const circle = progress.querySelector('.progress-ring-circle');
    if (circle) {
      const radius = circle.getAttribute('r');
      const circumference = 2 * Math.PI * radius;
      circle.style.strokeDasharray = `${circumference} ${circumference}`;
      circle.style.strokeDashoffset = circumference; // Start at 0%
    }
    
    // Start observing
    observer.observe(progress);
  });
}

function animateDemoBox() {
  const demoContainer = document.querySelector('.demo-container');
  
  if (demoContainer) {
    demoContainer.addEventListener('mousemove', (e) => {
      const rect = demoContainer.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      // Calculate rotation based on mouse position
      const rotateY = ((x / rect.width) - 0.5) * 10;
      const rotateX = ((y / rect.height) - 0.5) * -10;
      
      // Apply the rotation transform
      const demoWrapper = demoContainer.querySelector('.demo-wrapper');
      if (demoWrapper) {
        demoWrapper.style.transform = `rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
      }
    });
    
    // Reset rotation when mouse leaves
    demoContainer.addEventListener('mouseleave', () => {
      const demoWrapper = demoContainer.querySelector('.demo-wrapper');
      if (demoWrapper) {
        demoWrapper.style.transform = 'rotateX(5deg) rotateY(0deg)';
      }
    });
    
    // Handle click on play button
    const playButton = demoContainer.querySelector('.play-button');
    if (playButton) {
      playButton.addEventListener('click', () => {
        alert('Video demo would play here in a production environment!');
      });
    }
  }
}
