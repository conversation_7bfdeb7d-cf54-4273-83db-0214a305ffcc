/* Table of Contents Styles */

.toc-container {
  width: 250px;
  max-height: 70vh;
  position: fixed;
  right: 20px;
  top: 100px;
  background: var(--bg-white);
  border-radius: 12px;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
  z-index: 100;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transform-origin: center right;
  opacity: 0; /* Start hidden and fade in after initialization */
  transition: all 0.4s ease, transform 0.4s ease, opacity 0.5s ease;
}

/* Mini mode with better visibility and click interaction */
.toc-container.mini-mode {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--primary);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 6px 16px rgba(67, 97, 238, 0.25);
  overflow: visible;
  z-index: 101; /* Ensure it's above other elements */
  opacity: 1;
}

/* Hover effect for mini mode */
.toc-container.mini-mode:hover {
  transform: scale(1.1);
  box-shadow: 0 8px 24px rgba(67, 97, 238, 0.4);
}

/* Pulse animation for mini mode */
.toc-container.mini-mode::after {
  content: "";
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 50%;
  background: rgba(67, 97, 238, 0.3);
  opacity: 0;
  z-index: -1;
  animation: toc-pulse 2s infinite;
}

@keyframes toc-pulse {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.2);
    opacity: 0;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

/* TOC mini mode icon */
.toc-container.mini-mode::before {
  content: "\f0ca";
  font-family: "Font Awesome 5 Free", serif;
  font-weight: 900;
  color: white;
  font-size: 1.2rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* Hide elements in mini mode */
.toc-container.mini-mode .toc-header,
.toc-container.mini-mode .toc-list {
  opacity: 0;
  visibility: hidden;
  position: absolute;
}

/* Header styling */
.toc-header {
  padding: 14px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: var(--bg-white);
  position: sticky;
  top: 0;
  z-index: 2;
}

.toc-header h3 {
  font-size: 1.1rem;
  margin: 0;
  color: var(--primary);
  display: flex;
  align-items: center;
  gap: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
}

.toc-header h3::before {
  content: "\f02e";
  font-family: "Font Awesome 5 Free", serif;
  font-weight: 900;
  font-size: 0.9rem;
}

/* Toggle buttons */
.toc-toggle,
.mini-mode-toggle {
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  color: var(--primary);
}

.toc-toggle:hover,
.mini-mode-toggle:hover {
  background: rgba(67, 97, 238, 0.1);
  transform: scale(1.1);
}

.toc-toggle.collapsed {
  transform: rotate(-180deg);
}

/* List styling */
.toc-list {
  list-style: none;
  padding: 5px 0;
  margin: 0;
  overflow-y: auto;
  max-height: calc(70vh - 60px);
  scrollbar-width: thin;
  scrollbar-color: var(--primary) rgba(0, 0, 0, 0.05);
  transition: opacity 0.3s ease, max-height 0.3s ease;
}

.toc-list.collapsed {
  max-height: 0;
  overflow: hidden;
  opacity: 0;
}

.toc-list::-webkit-scrollbar {
  width: 4px;
}

.toc-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.02);
}

.toc-list::-webkit-scrollbar-thumb {
  background: var(--primary-light);
  border-radius: 10px;
}

/* Item styling */
.toc-item {
  padding: 10px 10px 10px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
  margin: 2px 0;
  display: flex;
  align-items: center;
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.toc-item:hover {
  background: rgba(67, 97, 238, 0.08);
  border-left-color: var(--primary-light);
}

.toc-item.active {
  color: var(--primary);
  font-weight: 600;
  background: rgba(67, 97, 238, 0.12);
  border-left: 3px solid var(--primary);
}

.toc-item i {
  margin-right: 10px;
  font-size: 0.9rem;
  color: var(--primary);
  opacity: 0.8;
  width: 18px;
  text-align: center;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .toc-container {
    position: fixed;
    bottom: 25px;
    top: auto;
    right: 25px;
    max-height: 500px;
    transform-origin: bottom right;
  }

  .toc-container.mini-mode {
    width: 55px;
    height: 55px;
    box-shadow: 0 8px 20px rgba(67, 97, 238, 0.4);
  }

  .toc-container.mini-mode::before {
    font-size: 1.3rem;
  }

  .toc-container:not(.mini-mode) {
    width: 85vw;
    max-width: 350px;
    max-height: 60vh;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
  }
}

@media (max-width: 576px) {
  .toc-container {
    position: fixed;
    bottom: 20px;
    top: auto;
    right: 20px;
    max-height: 50vh;
    transform: scale(0.9);
    transform-origin: bottom right;
    opacity: 0.9;
  }

  .toc-container:hover {
    opacity: 1;
    transform: scale(1);
  }

  .toc-container.mini-mode {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    overflow: hidden;
    bottom: 20px;
    right: 20px;
  }
}
