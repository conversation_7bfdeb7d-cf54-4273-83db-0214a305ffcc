/**
 * Responsive Utilities
 */

/* Responsive utility classes */
.hide-sm {
  display: initial;
}

.show-sm {
  display: none;
}

/* Common responsive media queries */
@media (max-width: 1200px) {
  .container {
    max-width: 992px;
  }
}

@media (max-width: 992px) {
  .container {
    max-width: 768px;
  }

  .hide-md {
    display: none;
  }

  .show-md {
    display: initial;
  }

  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }

  .hero h1 {
    font-size: 3rem;
  }

  .hero p {
    font-size: 1.2rem;
  }
}

@media (max-width: 768px) {
  .container {
    max-width: 576px;
  }

  .hide-sm {
    display: none;
  }

  .show-sm {
    display: initial;
  }

  .hero {
    padding: 100px 0 60px;
  }

  .hero h1 {
    font-size: 2.5rem;
  }

  .hero p {
    font-size: 1.1rem;
  }

  .hero-stats-container {
    gap: 15px;
  }

  .stat-card {
    min-width: 150px;
    padding: 15px;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 1.4rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .hero-stats {
    gap: 15px;
  }

  .stat-card {
    min-width: 140px;
    padding: 15px;
  }

  /* Adjust header navigation */
  .header-content {
    flex-direction: column;
    align-items: flex-start;
  }

  nav ul {
    margin-top: 15px;
    flex-wrap: wrap;
  }

  nav ul li {
    margin-left: 0;
    margin-right: 20px;
    margin-bottom: 10px;
  }

  /* Common section spacing */
  section {
    padding: 40px 0;
  }
}

@media (max-width: 576px) {
  .container {
    max-width: 100%;
    padding: 0 15px;
  }

  .hero h1 {
    font-size: 2rem;
  }

  .hero p {
    font-size: 1rem;
  }

  .cta-buttons {
    flex-direction: column;
    gap: 15px;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }

  .hero-stats-container {
    flex-direction: column;
    align-items: center;
  }

  .stat-card {
    width: 80%;
    max-width: 220px;
  }

  /* Simplify grid layouts */
  .grid {
    gap: 15px;
  }

  .features-grid,
  .team-members,
  .gallery-grid,
  .doc-cards {
    gap: 15px;
  }

  /* Ensure section titles are readable */
  .section-title {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .hero-stats {
    flex-direction: column;
    align-items: center;
    max-width: 280px;
    margin-left: auto;
    margin-right: auto;
  }

  .stat-card {
    width: 100%;
    flex-direction: row;
    text-align: left;
    align-items: center;
    padding: 12px 20px;
  }

  .stat-icon {
    margin-bottom: 0;
    margin-right: 15px;
  }

  .stat-content {
    text-align: left;
  }

  body {
    font-size: 14px;
  }
}
