/**
 * Sprint Progress Enhanced Interactivity
 * Adds more advanced animations and interactive elements to the sprint progress section
 */
function enhanceSprintProgressUI() {
  // Initialize components
  initParticleBackground();
  initProgressAnimations();
  initTiltEffects();
  initTabTransitions();
  initSprintTabsNavigation();
  enhanceChartDesign();
  animateProgressBars();
}

/**
 * Create particle background
 */
function initParticleBackground() {
  const section = document.querySelector(".sprint-progress-section");
  if (!section) return;

  // Create background container if it doesn't exist
  if (!document.querySelector(".sprint-particles-bg")) {
    const particlesBg = document.createElement("div");
    particlesBg.className = "sprint-particles-bg";

    // Add particles
    for (let i = 0; i < 4; i++) {
      const particle = document.createElement("div");
      particle.className = "particle";
      particlesBg.appendChild(particle);
    }

    // Insert after background wave
    const wave = section.querySelector(".section-background-wave");
    if (wave) {
      section.insertBefore(particlesBg, wave.nextSibling);
    } else {
      section.insertBefore(particlesBg, section.firstChild);
    }
  }
}

/**
 * Initialize progressive animations
 */
function initProgressAnimations() {
  // Get all animated elements
  const animatedElements = document.querySelectorAll(
    ".sprint-progress-section .animate-in"
  );

  // Set up enhanced intersection observer
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("visible");
          entry.target.style.animationPlayState = "running";
          observer.unobserve(entry.target);

          // If this is a container with metric cards, start their animations
          if (
            entry.target.classList.contains("sprint-metrics") ||
            entry.target.classList.contains("sprint-stats-row")
          ) {
            cascadeChildAnimations(entry.target);
          }
        }
      });
    },
    {
      root: null,
      threshold: 0.15,
      rootMargin: "0px 0px -100px 0px",
    }
  );

  // Observe all animated elements
  animatedElements.forEach((element) => {
    observer.observe(element);
  });

  // Add scroll animation to all gauge fills
  animateGaugeFillings();
}

/**
 * Create cascade animation effect for child elements
 * @param {HTMLElement} container - The parent container
 */
function cascadeChildAnimations(container) {
  const children = container.children;

  Array.from(children).forEach((child, index) => {
    child.style.opacity = "0";
    child.style.transform = "translateY(20px)";
    child.style.transition = "opacity 0.5s ease, transform 0.5s ease";
    child.style.transitionDelay = `${0.1 + index * 0.1}s`;

    // Trigger reflow
    child.getBoundingClientRect();

    // Apply animation
    child.style.opacity = "1";
    child.style.transform = "translateY(0)";
  });
}

/**
 * Initialize 3D tilt effects
 */
function initTiltEffects() {
  // Check if VanillaTilt is available
  if (typeof VanillaTilt === "undefined") {
    // Load VanillaTilt if not available
    const script = document.createElement("script");
    script.src =
      "https://cdnjs.cloudflare.com/ajax/libs/vanilla-tilt/1.8.0/vanilla-tilt.min.js";
    script.onload = () => {
      applyTiltEffect();
    };
    document.head.appendChild(script);
  } else {
    applyTiltEffect();
  }
}

/**
 * Apply tilt effect to cards
 */
function applyTiltEffect() {
  if (typeof VanillaTilt !== "undefined") {
    // Apply enhanced tilt options to different elements

    // Metric cards
    VanillaTilt.init(document.querySelectorAll(".metric-card"), {
      max: 8,
      speed: 400,
      glare: true,
      "max-glare": 0.1,
      scale: 1.05,
    });

    // Sprint stats
    VanillaTilt.init(document.querySelectorAll(".sprint-stat"), {
      max: 5,
      speed: 300,
      glare: false,
      scale: 1.02,
    });

    // Content cards
    VanillaTilt.init(
      document.querySelectorAll(
        ".task-distribution, .key-achievements, .sprint-health, .sprint-insights"
      ),
      {
        max: 3,
        speed: 200,
        glare: false,
      }
    );
  }
}

/**
 * Enhanced tab transitions
 */
function initTabTransitions() {
  const sprintTabs = document.querySelectorAll(".sprint-tab:not(.disabled)");
  const sprintDetails = document.querySelectorAll(".sprint-detail");

  sprintTabs.forEach((tab) => {
    tab.addEventListener("click", () => {
      // Skip if already active
      if (tab.classList.contains("active")) return;

      // Find currently active tab and detail
      const activeTab = document.querySelector(".sprint-tab.active");
      const activeDetail = document.querySelector(".sprint-detail.active");
      const sprintId = tab.getAttribute("data-sprint");
      const targetDetail = document.getElementById(`sprint-${sprintId}`);

      if (!targetDetail) return;

      // Smooth transition out of current detail
      if (activeDetail) {
        activeDetail.style.opacity = "0";
        activeDetail.style.transform = "translateY(10px)";
      }

      // Update tab states
      activeTab?.classList.remove("active");
      tab.classList.add("active");

      // Wait for fade out then switch content
      setTimeout(() => {
        // Hide previous content
        activeDetail?.classList.remove("active");

        // Show new content
        targetDetail.classList.add("active");
        targetDetail.style.opacity = "0";
        targetDetail.style.transform = "translateY(10px)";

        // Trigger animation
        setTimeout(() => {
          targetDetail.style.opacity = "1";
          targetDetail.style.transform = "translateY(0)";

          // Animate child elements
          animateTabContent(targetDetail);
        }, 50);
      }, 200);
    });
  });
}

/**
 * Animate tab content when switching tabs
 * @param {HTMLElement} tabContent - The tab content container
 */
function animateTabContent(tabContent) {
  // Animate sprint stats
  const statsRow = tabContent.querySelector(".sprint-stats-row");
  if (statsRow) {
    cascadeChildAnimations(statsRow);
  }

  // Animate gauge fills
  const gauges = tabContent.querySelectorAll(".gauge-fill");
  gauges.forEach((gauge) => {
    // Reset the gauge
    const originalOffset = gauge.style.strokeDashoffset;
    gauge.style.strokeDashoffset = "314";

    // Trigger animation after a short delay
    setTimeout(() => {
      gauge.style.strokeDashoffset = originalOffset;
    }, 300);
  });

  // Animate progress bars
  const progressBars = tabContent.querySelectorAll(".metric-progress-fill");
  if (progressBars.length > 0) {
    progressBars.forEach((bar) => {
      const width = bar.style.width;
      bar.style.width = "0";

      setTimeout(() => {
        bar.style.width = width;
      }, 200);
    });
  }
}

/**
 * Animate gauge fillings
 */
function animateGaugeFillings() {
  // Find all gauge elements
  const gauges = document.querySelectorAll(".gauge-fill");

  // Set up observer to animate when visible
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          // Get current offset and animate from no progress to current value
          const gauge = entry.target;
          const currentOffset = gauge.style.strokeDashoffset;

          // Start with no progress
          gauge.style.strokeDashoffset = "314";

          // Trigger animation after a small delay
          setTimeout(() => {
            gauge.style.strokeDashoffset = currentOffset;
          }, 300);

          observer.unobserve(gauge);
        }
      });
    },
    { threshold: 0.2 }
  );

  // Observe all gauges
  gauges.forEach((gauge) => observer.observe(gauge));
}

/**
 * Enhance chart design
 */
function enhanceChartDesign() {
  // Check if Chart.js exists
  if (typeof Chart === "undefined") return;

  // Update Chart.js defaults for better visual design
  Chart.defaults.font.family =
    "'Inter', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif";
  Chart.defaults.font.size = 12;
  Chart.defaults.color = "#6c757d";
  Chart.defaults.plugins.tooltip.backgroundColor = "rgba(255, 255, 255, 0.9)";
  Chart.defaults.plugins.tooltip.titleColor = "#333";
  Chart.defaults.plugins.tooltip.bodyColor = "#666";
  Chart.defaults.plugins.tooltip.borderColor = "rgba(0, 0, 0, 0.1)";
  Chart.defaults.plugins.tooltip.borderWidth = 1;
  Chart.defaults.plugins.tooltip.padding = 12;
  Chart.defaults.plugins.tooltip.cornerRadius = 8;
  Chart.defaults.plugins.tooltip.boxPadding = 6;
  Chart.defaults.plugins.legend.labels.usePointStyle = true;
  Chart.defaults.plugins.legend.labels.padding = 15;

  // Enhance existing charts if any exist
  const burndownChart = document.getElementById("sprintBurndownChart");
  if (burndownChart && burndownChart.chart) {
    updateChartStyles(burndownChart.chart);
  }

  // Enhance task distribution charts
  for (let i = 1; i <= 4; i++) {
    const distChart = document.getElementById(`taskDistChart${i}`);
    if (distChart && distChart.chart) {
      updateChartStyles(distChart.chart);
    }
  }
}

/**
 * Update styles for an existing chart
 * @param {Chart} chart - The Chart.js instance
 */
function updateChartStyles(chart) {
  // Apply enhanced styling to the chart
  if (chart.config.type === "line") {
    // Line chart enhancements
    const gradient = chart.ctx.createLinearGradient(0, 0, 0, chart.height);
    gradient.addColorStop(0, "rgba(67, 97, 238, 0.4)");
    gradient.addColorStop(1, "rgba(67, 97, 238, 0.0)");

    // Update dataset styles
    chart.data.datasets.forEach((dataset, index) => {
      if (index === 0) {
        // Ideal burndown line
        dataset.borderDash = [5, 5];
        dataset.borderWidth = 2;
      } else if (index === 1) {
        // Actual burndown line
        dataset.backgroundColor = gradient;
        dataset.borderWidth = 3;
        dataset.pointBorderWidth = 2;
        dataset.pointRadius = 5;
        dataset.pointHoverRadius = 8;
      }
    });

    // Update chart options
    chart.options.plugins.tooltip.usePointStyle = true;
    chart.options.scales.y.grid.color = "rgba(0, 0, 0, 0.03)";
    chart.options.scales.x.grid.color = "rgba(0, 0, 0, 0.03)";

    chart.update();
  } else if (chart.config.type === "doughnut" || chart.config.type === "pie") {
    // Doughnut/pie chart enhancements
    chart.options.plugins.tooltip.usePointStyle = true;
    chart.options.cutout = "65%";
    chart.options.radius = "90%";
    chart.options.plugins.legend.position = "right";

    chart.update();
  }
}

/**
 * Initialize sprint tabs navigation with pagination
 */
function initSprintTabsNavigation() {
  const sprintTabsWrapper = document.querySelector(".sprint-tabs-wrapper");
  if (!sprintTabsWrapper) return;

  const sprintTabs = sprintTabsWrapper.querySelector(".sprint-tabs");
  const prevButton = sprintTabsWrapper.querySelector(".prev-sprints");
  const nextButton = sprintTabsWrapper.querySelector(".next-sprints");
  const allTabs = sprintTabs.querySelectorAll(".sprint-tab");

  // Number of tabs to show at once
  const visibleTabsCount = 8; // Show only 8 sprints at a time

  // Initialize state
  let currentPage = 0;
  const maxPage = Math.ceil(allTabs.length / visibleTabsCount) - 1;

  // Initial setup - hide tabs beyond the visible count
  updateVisibleTabs();
  updateNavigationButtons();

  // Add event listeners to navigation buttons
  prevButton.addEventListener("click", () => {
    if (currentPage > 0) {
      currentPage--;
      updateVisibleTabs();
      updateNavigationButtons();
    }
  });

  nextButton.addEventListener("click", () => {
    if (currentPage < maxPage) {
      currentPage++;
      updateVisibleTabs();
      updateNavigationButtons();
    }
  });

  // Function to update which tabs are visible
  function updateVisibleTabs() {
    // Hide all tabs first
    allTabs.forEach((tab, index) => {
      const pageOfTab = Math.floor(index / visibleTabsCount);

      if (pageOfTab === currentPage) {
        // Show tabs on current page
        tab.style.display = "flex";
      } else {
        // Hide tabs on other pages
        tab.style.display = "none";
      }
    });
  }

  // Function to update the state of navigation buttons
  function updateNavigationButtons() {
    // Update prev button state
    if (currentPage === 0) {
      prevButton.classList.add("disabled");
      prevButton.setAttribute("aria-disabled", "true");
    } else {
      prevButton.classList.remove("disabled");
      prevButton.setAttribute("aria-disabled", "false");
    }

    // Update next button state
    if (currentPage >= maxPage) {
      nextButton.classList.add("disabled");
      nextButton.setAttribute("aria-disabled", "true");
    } else {
      nextButton.classList.remove("disabled");
      nextButton.setAttribute("aria-disabled", "false");
    }
  }
}

/**
 * Animate progress bars on scroll
 */
function animateProgressBars() {
  const progressBars = document.querySelectorAll(".metric-progress-fill");

  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const bar = entry.target;
          const width = bar.style.width;

          // Reset width to 0
          bar.style.width = "0";

          // Animate to target width
          setTimeout(() => {
            bar.style.width = width;
          }, 200);

          observer.unobserve(bar);
        }
      });
    },
    { threshold: 0.2 }
  );

  progressBars.forEach((bar) => observer.observe(bar));
}

// Run when DOM is fully loaded
document.addEventListener("DOMContentLoaded", function () {
  enhanceSprintProgressUI();
});

// Expose function for manual initialization
window.enhanceSprintProgressUI = enhanceSprintProgressUI;
