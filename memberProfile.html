<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Team Member Profile - GradProject</title>
    <!-- Import the main CSS file which imports all others -->
    <link rel="stylesheet" href="css/main.css" />
    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css"
    />
    <link rel="stylesheet" href="css/components/team.css" />
    <link rel="stylesheet" href="css/components/member-profile.css" />
    <!-- VanillaTilt.js for 3D card effects -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/vanilla-tilt/1.8.0/vanilla-tilt.min.js"></script>
  </head>
  <body>
    <header>
      <div class="container header-content">
        <div class="header-left">
          <div class="logo">GradProject</div>
        </div>

        <div class="header-center">
          <nav>
            <ul>
              <li>
                <a href="index.html#overview"
                  ><i class="fas fa-info-circle"></i> Overview</a
                >
              </li>
              <li>
                <a href="index.html#team"><i class="fas fa-users"></i> Team</a>
              </li>
              <li>
                <a href="index.html#progress"
                  ><i class="fas fa-tasks"></i> Progress</a
                >
              </li>
              <li>
                <a href="index.html#documentation"
                  ><i class="fas fa-file-alt"></i> Documentation</a
                >
              </li>
            </ul>
          </nav>
        </div>

        <div class="header-right">
          <div class="theme-toggle" id="themeToggle">
            <i class="fas fa-moon"></i>
            <i class="fas fa-sun"></i>
            <div class="toggle-ball"></div>
          </div>
        </div>
      </div>
    </header>

    <main>
      <section class="member-profile-section">
        <div class="container">
          <div class="profile-back-button">
            <a href="index.html#team"
              ><i class="fas fa-arrow-left"></i> Back to Team</a
            >
          </div>

          <div class="profile-content">
            <!-- Profile content will be loaded here via JavaScript -->
            <div class="profile-header-card">
              <div class="profile-hero">
                <div class="profile-bg-gradient"></div>
                <div class="profile-particles" id="profileParticles"></div>
                <div class="profile-img-container">
                  <div class="profile-avatar"><i class="fas fa-user"></i></div>
                </div>
                <div class="profile-basic-info">
                  <h1 id="memberName">Loading...</h1>
                  <div id="memberRole" class="member-role">Loading...</div>
                  <div id="memberTeam" class="member-team">
                    <i class="fas fa-users"></i> <span>Loading...</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="profile-details-card">
              <div class="profile-tabs">
                <div class="profile-tab active" data-tab="about">About</div>
                <div class="profile-tab" data-tab="skills">Skills</div>
                <div class="profile-tab" data-tab="tasks">Tasks</div>
                <div class="profile-tab" data-tab="contact">Contact</div>
                <div class="tab-indicator" id="tabIndicator"></div>
              </div>

              <div class="profile-tab-content active" id="about-content">
                <div class="profile-section">
                  <h2><i class="fas fa-user-circle"></i> About</h2>
                  <p id="memberBio">Loading member information...</p>
                </div>

                <div class="profile-section">
                  <h2><i class="fas fa-graduation-cap"></i> Education</h2>
                  <ul id="memberEducation" class="profile-list">
                    <li>Loading education information...</li>
                  </ul>
                </div>

                <div class="profile-section">
                  <h2>
                    <i class="fas fa-project-diagram"></i> Project Contributions
                  </h2>
                  <div id="projectContributions">
                    <!-- Project contributions will be added here dynamically -->
                  </div>
                </div>
              </div>

              <div class="profile-tab-content" id="skills-content">
                <div class="profile-section">
                  <h2><i class="fas fa-code"></i> Technical Skills</h2>
                  <div class="skill-meters" id="skillMeters">
                    <!-- Skills will be added here dynamically -->
                  </div>
                </div>
              </div>

              <div class="profile-tab-content" id="tasks-content">
                <div class="profile-section">
                  <h2><i class="fas fa-tasks"></i> Tasks & Responsibilities</h2>

                  <div class="tasks-container">
                    <div class="tasks-controls">
                      <div class="tasks-filter">
                        <button class="filter-btn active" data-filter="all">
                          <i class="fas fa-list"></i> All Tasks
                        </button>
                        <button class="filter-btn" data-filter="completed">
                          <i class="fas fa-check-circle"></i> Completed
                        </button>
                        <button class="filter-btn" data-filter="in-progress">
                          <i class="fas fa-spinner"></i> In Progress
                        </button>
                        <button class="filter-btn" data-filter="pending">
                          <i class="fas fa-clock"></i> Pending
                        </button>
                      </div>
                      <div class="tasks-view-toggle">
                        <div
                          class="view-btn active"
                          data-view="card"
                          title="Card View"
                        >
                          <i class="fas fa-th-large"></i>
                        </div>
                        <div
                          class="view-btn"
                          data-view="timeline"
                          title="Timeline View"
                        >
                          <i class="fas fa-stream"></i>
                        </div>
                      </div>
                    </div>

                    <div id="memberTasks" class="tasks-card-view">
                      <!-- Tasks will be added here dynamically -->
                    </div>

                    <div
                      id="memberTasksTimeline"
                      class="tasks-timeline"
                      style="display: none"
                    >
                      <!-- Timeline view will be populated dynamically -->
                    </div>
                  </div>
                </div>
              </div>

              <div class="profile-tab-content" id="contact-content">
                <div class="profile-section">
                  <h2><i class="fas fa-envelope"></i> Contact Information</h2>
                  <div class="contact-details" id="contactDetails">
                    <!-- Contact details will be inserted here -->
                  </div>

                  <div class="profile-social">
                    <h3>Social Links</h3>
                    <div class="social-links" id="socialLinks">
                      <!-- Social links will be added here dynamically -->
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <footer>
      <div class="container">
        <div class="footer-content">
          <div class="footer-column">
            <h3>About Project</h3>
            <p>
              Our graduation project represents months of collaborative work and
              dedication to create an innovative solution for smart city
              management.
            </p>
          </div>
          <div class="footer-column">
            <h3>Quick Links</h3>
            <div class="footer-links">
              <a href="#overview"
                ><i class="fas fa-angle-right"></i> Project Overview</a
              >
              <a href="#team"
                ><i class="fas fa-angle-right"></i> Team Members</a
              >
              <a href="#sprints"
                ><i class="fas fa-angle-right"></i> Sprint Progress</a
              >
              <a href="#documentation"
                ><i class="fas fa-angle-right"></i> Documentation</a
              >
            </div>
          </div>
          <div class="footer-column">
            <h3>Contact</h3>
            <div class="footer-links">
              <a href="mailto:<EMAIL>"
                ><i class="fas fa-envelope"></i> <EMAIL></a
              >
              <a href="#"><i class="fas fa-phone"></i> +****************</a>
              <a href="#"
                ><i class="fas fa-map-marker-alt"></i> University Campus,
                Building 4</a
              >
            </div>
          </div>
        </div>
        <div class="copyright">
          &copy; 2025 Graduation Project Team. All Rights Reserved.
        </div>
      </div>
    </footer>

    <!-- JavaScript Files -->
    <script src="js/theme.js"></script>
    <script src="js/memberProfile.js"></script>
  </body>
</html>
