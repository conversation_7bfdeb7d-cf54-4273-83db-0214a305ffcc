/**
 * Main JavaScript functionality
 * Enhanced with error handling, performance optimization, and dependency checking
 */

document.addEventListener("DOMContentLoaded", function () {
    // Track initialization performance
    const initStart = performance.now();
    console.log("Initializing application...");

    addProjectProgressVisualization();
    enhanceSprintProgress();

    // Check if a function exists before trying to call it
    function functionExists(fn) {
        return typeof fn === "function";
    }

    // Setup global error handling for initialization functions
    function safeInit(fnName, fn) {
        if (!functionExists(fn)) {
            console.warn(
                `Function ${fnName} is not defined, skipping initialization`
            );
            return true; // Don't fail critical features for undefined functions
        }

        try {
            console.log(`Initializing: ${fnName}`);
            fn();
            return true;
        } catch (error) {
            console.error(`Error initializing ${fnName}:`, error);
            return false;
        }
    }

    // Critical features - initialize immediately
    const criticalFeatures = [
        // Table of Contents is now critical for navigation
        {name: "Table of Contents", fn: initTableOfContents},
        {name: "Smooth Scrolling", fn: initSmoothScrolling},
        // Only add features that are truly critical to basic site functionality
    ];

    // Features that should run early but aren't critical
    const earlyFeatures = [
        {name: "Accessibility", fn: enhanceAccessibility},
        // TOC moved to critical features
    ];

    // Less critical features - can be deferred
    const deferredFeatures = [
        {name: "Animations", fn: initAnimations},
        {name: "Timeline Interactions", fn: initTimelineInteraction},
        {name: "Performance Optimizations", fn: optimizePagePerformance},
        {name: "Project Progress", fn: addProjectProgressVisualization},
        {name: "Tech Stack", fn: addTechStackSection},
        {name: "Contact Form", fn: enhanceContactForm},
        {name: "Testimonial Slider", fn: initTestimonialSlider},
        {name: "Component Initialization", fn: initializeAllComponents},
        {name: "Stat Counters", fn: initAnimatedCounters},
        {name: "Progress Path", fn: addProgressPath},
        {name: "Sprint Progress", fn: enhanceSprintProgress},
    ];

    // Initialize critical features immediately
    let criticalSuccess = true;
    criticalFeatures.forEach((feature) => {
        if (!safeInit(feature.name, feature.fn)) {
            criticalSuccess = false;
        }
    });

    // Run early features immediately but don't consider them critical
    earlyFeatures.forEach((feature) => {
        safeInit(feature.name, feature.fn);
    });

    // If critical features fail, show error message
    if (!criticalSuccess) {
        const errorMsg = document.createElement("div");
        errorMsg.className = "initialization-error";
        errorMsg.innerHTML = `
            <div class="error-content">
                <i class="fas fa-exclamation-triangle"></i>
                <p>Some critical features could not be initialized. Try refreshing the page.</p>
                <button class="dismiss-error" aria-label="Dismiss error message">×</button>
            </div>
        `;
        document.body.prepend(errorMsg);

        // Add error message styling
        const style = document.createElement("style");
        style.textContent = `
            .initialization-error {
                background: #ffdddd;
                color: #990000;
                padding: 10px;
                text-align: center;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                z-index: 10000;
                box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            }
            .error-content {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 10px;
            }
            .dismiss-error {
                background: none;
                border: none;
                color: #990000;
                font-size: 1.4rem;
                cursor: pointer;
                padding: 0 5px;
                margin-left: 10px;
            }
            .dismiss-error:hover {
                color: #cc0000;
            }
        `;
        document.head.appendChild(style);

        // Add dismiss functionality
        const dismissBtn = errorMsg.querySelector(".dismiss-error");
        if (dismissBtn) {
            dismissBtn.addEventListener("click", () => {
                errorMsg.style.display = "none";
            });
        }
    }

    // Initialize deferred features with requestAnimationFrame for better performance
    requestAnimationFrame(() => {
        let featuresInitialized = 0;
        const totalFeatures = deferredFeatures.length;

        // Initialize features one by one with small delays between them
        function initNextFeature(index) {
            if (index >= totalFeatures) {
                const initEnd = performance.now();
                console.log(
                    `All features initialized in ${(initEnd - initStart).toFixed(2)}ms`
                );
                return;
            }

            const feature = deferredFeatures[index];
            safeInit(feature.name, feature.fn);
            featuresInitialized++;

            // Small delay before initializing next feature
            setTimeout(() => initNextFeature(index + 1), 50);
        }

        initNextFeature(0);
    });

    // Initialize active section tracking with better dependency checking
    if (functionExists(highlightActiveSectionInToc) && functionExists(debounce)) {
        window.addEventListener(
            "scroll",
            debounce(highlightActiveSectionInToc, 100)
        );
    } else {
        console.warn("Couldn't initialize section tracking: missing dependencies");
    }

    // Check for theme preference
    if (localStorage.getItem("theme") === "dark") {
        document.documentElement.setAttribute("data-theme", "dark");
    }

    // Add page load timing to analytics
    window.addEventListener("load", () => {
        const loadTime = performance.now();
        console.log(`Page fully loaded in ${loadTime.toFixed(2)}ms`);
    });

    // Initialize search functionality - ensure it's called only once
    safeInit("Search Toggle", initSearchToggle);
});

// Initialize smooth scrolling for all anchor links
function initSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
        anchor.addEventListener("click", function (e) {
            e.preventDefault();

            const targetId = this.getAttribute("href");
            if (targetId !== "#") {
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: "smooth",
                    });
                }
            }
        });
    });
}

// Helper function to load Chart.js
function loadChartJS() {
    return new Promise((resolve, reject) => {
        if (window.Chart) {
            resolve();
            return;
        }

        const script = document.createElement("script");
        script.src = "https://cdn.jsdelivr.net/npm/chart.js";
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
    });
}

// Create the burndown chart
function createBurndownChart() {
    if (!window.Chart) return;

    const ctx = document.getElementById("sprintBurndownChart").getContext("2d");
    new Chart(ctx, {
        type: "line",
        data: {
            labels: [
                "Sprint 1",
                "Sprint 2",
                "Sprint 3",
                "Sprint 4",
                "Sprint 5",
                "Sprint 6",
            ],
            datasets: [
                {
                    label: "Planned Work",
                    data: [100, 85, 70, 50, 30, 0],
                    borderColor: "rgba(67, 97, 238, 0.7)",
                    borderWidth: 2,
                    borderDash: [5, 5],
                    fill: false,
                    pointBackgroundColor: "white",
                    tension: 0.4,
                },
                {
                    label: "Actual Work",
                    data: [100, 82, 65, 35, null, null],
                    borderColor: "rgba(67, 97, 238, 1)",
                    backgroundColor: "rgba(67, 97, 238, 0.1)",
                    borderWidth: 2,
                    fill: true,
                    pointBackgroundColor: "white",
                    tension: 0.4,
                },
            ],
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: "Project Burndown Chart",
                    font: {
                        size: 16,
                    },
                },
                legend: {
                    position: "bottom",
                },
                tooltip: {
                    mode: "index",
                    intersect: false,
                },
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: "Remaining Work (%)",
                    },
                    suggestedMax: 100,
                },
                x: {
                    title: {
                        display: true,
                        text: "Sprint",
                    },
                },
            },
        },
    });
}

// Create the task distribution chart for Sprint 1
function createTaskDistributionChart() {
    if (!window.Chart) return;

    const ctx = document.getElementById("taskDistChart1").getContext("2d");
    new Chart(ctx, {
        type: "doughnut",
        data: {
            labels: ["Frontend", "Backend", "Documentation", "Testing"],
            datasets: [
                {
                    data: [4, 3, 3, 2],
                    backgroundColor: [
                        "rgba(67, 97, 238, 0.7)",
                        "rgba(76, 201, 240, 0.7)",
                        "rgba(249, 199, 79, 0.7)",
                        "rgba(151, 150, 240, 0.7)",
                    ],
                    borderColor: "white",
                    borderWidth: 2,
                },
            ],
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: "right",
                },
            },
        },
    });
}

function addProgressPath() {
    const timelineSection = document.querySelector(".timeline-section");
    const timeline = document.querySelector(".timeline");

    // Create SVG path that follows the timeline
    const pathContainer = document.createElement("div");
    pathContainer.className = "progress-path-container";

    pathContainer.innerHTML = `
    <svg class="progress-path" viewBox="0 0 10 100" preserveAspectRatio="none">
      <path class="path-track" d="M5,0 L5,100" />
      <path class="path-progress" d="M5,0 L5,100" stroke-dasharray="100" stroke-dashoffset="100" />
    </svg>
  `;

    timeline.parentNode.insertBefore(pathContainer, timeline);

    // Add styles
    const style = document.createElement("style");
    style.textContent = `
    .progress-path-container {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      height: 100%;
      width: 10px;
      z-index: 0;
    }
    .progress-path {
      width: 100%;
      height: 100%;
    }
    .path-track {
      stroke: rgba(67, 97, 238, 0.2);
      stroke-width: 4;
    }
    .path-progress {
      stroke: var(--primary);
      stroke-width: 4;
      stroke-linecap: round;
      animation: drawPath 2s ease-out forwards 0.5s;
    }
    @keyframes drawPath {
      to { stroke-dashoffset: 20; } /* Adjust based on project completion */
    }
  `;
    document.head.appendChild(style);
}

function addTechStackSection() {
    // Create the tech stack section
    const techSection = document.createElement("section");
    techSection.id = "tech-stack";
    techSection.className = "tech-stack-section";

    techSection.innerHTML = `
    <div class="container">
      <h2 class="section-title">Our Tech Stack</h2>
      <p class="tech-intro">These are the key technologies powering our smart city solution</p>
      
      <div class="tech-categories">
        <div class="tech-category">
          <h3><i class="fas fa-code"></i> Frontend</h3>
          <div class="tech-logos">
            <div class="tech-logo">
              <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/javascript/javascript-original.svg" alt="JavaScript">
              <span>JavaScript</span>
            </div>
            <div class="tech-logo">
              <img src="https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg" alt="React">
              <span>React</span>
            </div>
            <!-- Add more frontend technologies -->
          </div>
        </div>
        
        <!-- Add more categories: Backend, Database, DevOps, etc. -->
      </div>
    </div>
  `;

    // Add styles
    const style = document.createElement("style");
    style.textContent = `
    .tech-stack-section {
      padding: 60px 0;
      background: var(--bg-light);
    }
    
    .tech-intro {
      text-align: center;
      max-width: 700px;
      margin: 0 auto 40px;
      color: var(--text-light);
    }
    
    .tech-categories {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 30px;
    }
    
    .tech-category h3 {
      font-size: 1.3rem;
      margin-bottom: 20px;
      color: var(--primary-dark);
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .tech-logos {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
    }
    
    .tech-logo {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 80px;
      text-align: center;
    }
    
    .tech-logo img {
      width: 50px;
      height: 50px;
      margin-bottom: 10px;
      transition: transform 0.3s ease;
    }
    
    .tech-logo:hover img {
      transform: translateY(-5px);
    }
    
    .tech-logo span {
      font-size: 0.8rem;
      color: var(--text-light);
    }
  `;
    document.head.appendChild(style);

    // Insert before footer
    const footer = document.querySelector("footer");
    if (footer) {
        footer.parentNode.insertBefore(techSection, footer);
    }
}

function initializeMainPage() {
    document.querySelectorAll("section").forEach((section, index) => {
        section.style.opacity = "0";
        section.style.transform = "translateY(50px)";
        section.style.transition = "opacity 0.8s ease, transform 0.8s ease";
        section.style.transitionDelay = `${0.2 + index * 0.1}s`;
    });

    // Show sections
    setTimeout(() => {
        document.querySelectorAll("section").forEach((section) => {
            section.style.opacity = "1";
            section.style.transform = "translateY(0)";
        });
    }, 100);
}

// Main initialization function
function initializeAllComponents() {
    // Initialize testimonial slider
    initTestimonialSlider();

    // Apply entrance animations to testimonials
    const testimonialElements = document.querySelectorAll(
        ".testimonials-section .section-title, .testimonials-intro, .testimonial-item"
    );
    testimonialElements.forEach((el, index) => {
        el.style.opacity = "0";
        el.style.transform = "translateY(20px)";
        el.style.transition = "opacity 0.6s ease, transform 0.6s ease";
        el.style.transitionDelay = `${0.2 + index * 0.1}s`;
    });

    // Create intersection observer for testimonial section
    const testimonialObserver = new IntersectionObserver(
        (entries) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    entry.target
                        .querySelectorAll(
                            ".section-title, .testimonials-intro, .testimonial-item"
                        )
                        .forEach((el) => {
                            el.style.opacity = "1";
                            el.style.transform = "translateY(0)";
                        });
                    testimonialObserver.unobserve(entry.target);
                }
            });
        },
        {threshold: 0.2}
    );

    // Observe testimonial section
    const testimonialSection = document.querySelector(".testimonials-section");
    if (testimonialSection) {
        testimonialObserver.observe(testimonialSection);
    }
}

// Initialize search functionality - improved with better event handling
function initSearchToggle() {
    // Search functionality has been disabled

}
