/**
 * Member Profile JavaScript
 * Handles loading and displaying individual team member information
 */

/**
 * Calculate hours between two dates
 * @param {string} startDate - Start date in format MM/DD/YYYY
 * @param {string} endDate - End date in format MM/DD/YYYY
 * @returns {number} - Number of hours between dates
 */
function calculateHours(startDate, endDate) {
  if (!startDate || !endDate) return null;

  // Parse dates
  const start = new Date(startDate);
  const end = new Date(endDate);

  // Check if dates are valid
  if (isNaN(start.getTime()) || isNaN(end.getTime())) return null;

  // Calculate difference in milliseconds
  const diffMs = end - start;

  // Convert to hours (ms / 1000 / 60 / 60)
  const hours = Math.round(diffMs / (1000 * 60 * 60));

  return hours > 0 ? hours : null;
}

// Initialize the profile page
function initProfilePage() {
  console.log("Initializing profile page");

  // Parse URL parameters to get member information
  const params = new URLSearchParams(window.location.search);
  const memberId = params.get("id");
  const memberName = params.get("name");
  const memberRole = params.get("role");
  const memberTeam = params.get("team");

  try {
    // Load member data (now async)
    loadMemberData(memberId, memberName, memberRole, memberTeam).then(() => {
      // Setup tabs after data is loaded
      setupTabs();

      // Create animated particles for the profile header
      createParticles();

      // Initialize animations
      initializeAnimations();

      // Setup task filter and view toggle handlers
      setupTaskControls();

      // Ensure tab indicator is properly positioned after everything is loaded
      setTimeout(updateTabIndicator, 200);

      // Add another delayed update for reliability
      setTimeout(updateTabIndicator, 500);
    });
  } catch (error) {
    console.error("Error initializing profile page:", error);
  }
}

// Run initialization when DOM is loaded
document.addEventListener("DOMContentLoaded", initProfilePage);

// Also run initialization when window is fully loaded (backup)
window.addEventListener("load", function () {
  // Check if tabs are working, if not reinitialize
  const activeTab = document.querySelector(".profile-tab.active");
  const tabIndicator = document.getElementById("tabIndicator");

  if (!activeTab || !tabIndicator || tabIndicator.style.width === "") {
    console.log("Tabs not properly initialized, reinitializing...");
    setupTabs();
    setTimeout(updateTabIndicator, 100);
  }
});

/**
 * Create animated particles for the profile header background
 */
function createParticles() {
  const container = document.getElementById("profileParticles");
  if (!container) return;

  // Clear any existing particles
  container.innerHTML = "";

  // Create random particles
  for (let i = 0; i < 15; i++) {
    const particle = document.createElement("div");
    particle.classList.add("particle");

    // Random size between 5px and 20px
    const size = Math.floor(Math.random() * 16) + 5;
    particle.style.width = `${size}px`;
    particle.style.height = `${size}px`;

    // Random position
    const posX = Math.floor(Math.random() * 100);
    const posY = Math.floor(Math.random() * 100);
    particle.style.left = `${posX}%`;
    particle.style.top = `${posY}%`;

    // Random animation duration and delay
    const duration = (Math.random() * 10 + 5).toFixed(2);
    const delay = (Math.random() * 5).toFixed(2);
    particle.style.animationDuration = `${duration}s`;
    particle.style.animationDelay = `${delay}s`;

    // Random opacity
    particle.style.opacity = (Math.random() * 0.5 + 0.1).toFixed(2);

    container.appendChild(particle);
  }
}

/**
 * Initialize various animations on the profile page
 */
function initializeAnimations() {
  // Animate skill progress bars when they come into view
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const progressBars = entry.target.querySelectorAll(
            ".skill-progress-fill"
          );
          progressBars.forEach((bar, index) => {
            const percentage = bar.getAttribute("data-percentage");
            setTimeout(() => {
              bar.style.width = `${percentage}%`;
            }, 100 * index);
          });

          // Add tilt effect to skill cards if VanillaTilt is available
          if (typeof VanillaTilt !== "undefined") {
            VanillaTilt.init(entry.target.querySelectorAll(".skill-meter"), {
              max: 8,
              speed: 400,
              glare: true,
              "max-glare": 0.1,
              scale: 1.02,
            });
          } else {
            // If VanillaTilt is not available, try to load it
            loadVanillaTilt().then(() => {
              if (typeof VanillaTilt !== "undefined") {
                VanillaTilt.init(
                  entry.target.querySelectorAll(".skill-meter"),
                  {
                    max: 8,
                    speed: 400,
                    glare: true,
                    "max-glare": 0.1,
                    scale: 1.02,
                  }
                );
              }
            });
          }

          observer.unobserve(entry.target);
        }
      });
    },
    { threshold: 0.2 }
  );

  const skillsSection = document.getElementById("skillMeters");
  if (skillsSection) {
    observer.observe(skillsSection);
  }

  // Animate task progress bars when they come into view
  const taskObserver = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const progressBars =
            entry.target.querySelectorAll(".progress-bar-fill");
          progressBars.forEach((bar, index) => {
            const percentage = bar.getAttribute("data-percentage");
            setTimeout(() => {
              bar.style.width = `${percentage}%`;
            }, 100 * index);
          });

          // Apply tilt effect to task cards
          if (typeof VanillaTilt !== "undefined") {
            VanillaTilt.init(entry.target.querySelectorAll(".task-item"), {
              max: 5,
              speed: 400,
              glare: false,
              scale: 1.02,
              gyroscope: false,
            });
          }

          taskObserver.unobserve(entry.target);
        }
      });
    },
    { threshold: 0.1 }
  );

  const tasksSection = document.getElementById("memberTasks");
  if (tasksSection) {
    taskObserver.observe(tasksSection);
  }

  // Update tab indicator position
  updateTabIndicator();
}

/**
 * Load VanillaTilt.js if not already loaded
 */
function loadVanillaTilt() {
  return new Promise((resolve, reject) => {
    if (typeof VanillaTilt !== "undefined") {
      resolve();
      return;
    }

    const script = document.createElement("script");
    script.src =
      "https://cdnjs.cloudflare.com/ajax/libs/vanilla-tilt/1.8.0/vanilla-tilt.min.js";
    script.onload = resolve;
    script.onerror = reject;
    document.head.appendChild(script);
  });
}

/**
 * Update tab indicator position based on the active tab
 */
function updateTabIndicator() {
  const activeTab = document.querySelector(".profile-tab.active");
  const tabIndicator = document.getElementById("tabIndicator");
  const tabsContainer = document.querySelector(".profile-tabs");

  console.log("Updating tab indicator", {
    activeTabExists: !!activeTab,
    tabIndicatorExists: !!tabIndicator,
    tabsContainerExists: !!tabsContainer,
    activeTabId: activeTab ? activeTab.getAttribute("data-tab") : null,
  });

  if (activeTab && tabIndicator && tabsContainer) {
    // Force a reflow to ensure DOM is fully updated
    void tabsContainer.offsetWidth;

    // Set a small timeout to ensure DOM is fully updated
    setTimeout(() => {
      try {
        // Get the dimensions and position of the active tab
        const tabRect = activeTab.getBoundingClientRect();

        // Calculate the left position relative to the container
        const leftPosition = activeTab.offsetLeft;

        // Set the indicator position and width
        tabIndicator.style.width = `${tabRect.width}px`;
        tabIndicator.style.left = `${leftPosition}px`;

        console.log("Tab indicator updated:", {
          tab: activeTab.getAttribute("data-tab"),
          width: tabRect.width,
          left: leftPosition,
        });
      } catch (error) {
        console.error("Error updating tab indicator:", error);
      }
    }, 50); // Increased timeout for more reliability
  } else {
    console.warn("Could not update tab indicator: missing elements", {
      activeTab: !!activeTab,
      tabIndicator: !!tabIndicator,
      tabsContainer: !!tabsContainer,
    });
  }
}

/**
 * Load member data and populate the profile page
 */
async function loadMemberData(memberId, memberName, memberRole, memberTeam) {
  // Set basic member info in the header
  document.getElementById("memberName").textContent =
    memberName || "Team Member";
  document.getElementById("memberRole").textContent = memberRole || "Team Role";
  document.getElementById("memberTeam").querySelector("span").textContent =
    formatTeamName(memberTeam);

  // Set document title
  document.title = `${memberName || "Team Member"} - Project Profile`;

  // Set appropriate icon for the team
  const teamIcon = document.getElementById("memberTeam").querySelector("i");
  setTeamIcon(teamIcon, memberTeam);

  // Load member details from JSON file
  let memberData;
  try {
    // Convert team name format (e.g., "web-team" to "web")
    let teamId = memberTeam ? memberTeam.replace("-team", "") : "";

    // Handle special cases for team IDs
    if (teamId === "frontend" || teamId === "backend") {
      teamId = "web";
    } else if (teamId === "embedded-system") {
      teamId = "embedded";
    }

    // Fetch member data from JSON file
    const response = await fetch(`/data/team/${teamId}/${memberId}.json`);

    if (response.ok) {
      memberData = await response.json();
    } else {
      console.warn(`Failed to load member data for ${memberId} from JSON file`);
      // Fall back to the mock data
      memberData = getMemberData(memberId, memberTeam, memberRole);
    }
  } catch (error) {
    console.error("Error loading member data:", error);
    // Fall back to the mock data
    memberData = getMemberData(memberId, memberTeam, memberRole);
  }

  // Populate the about section
  document.getElementById("memberBio").textContent = memberData.bio;

  // Populate education
  const educationList = document.getElementById("memberEducation");
  educationList.innerHTML = "";
  memberData.education.forEach((item) => {
    const li = document.createElement("li");
    li.textContent = item;
    educationList.appendChild(li);
  });

  // Populate skills with enhanced visualization
  const skillMeters = document.getElementById("skillMeters");
  skillMeters.innerHTML = "";
  memberData.skills.forEach((skill) => {
    const skillIcon = getSkillIcon(skill.name);
    const skillTip = getSkillTooltip(skill.name);
    const skillCategory = getSkillCategory(skill.name);
    const skillLevel = getSkillLevelText(skill.level);

    const skillMeter = document.createElement("div");
    skillMeter.className = "skill-meter";
    skillMeter.setAttribute(
      "data-skill",
      skill.name.toLowerCase().replace(/\s+/g, "-")
    );
    skillMeter.setAttribute("data-category", skillCategory);

    // Create skill particles for animation
    const particles = `
      <div class="skill-particles">
        <div class="skill-particle"></div>
        <div class="skill-particle"></div>
        <div class="skill-particle"></div>
      </div>
    `;

    skillMeter.innerHTML = `
      <div class="skill-tooltip">${skillTip}</div>
      <div class="skill-badge">${skillCategory}</div>
      ${particles}
      <div class="skill-header">
        <div class="skill-icon-wrapper">
          <i class="${skillIcon} skill-icon"></i>
        </div>
        <div class="skill-info">
          <span class="skill-label">${skill.name}</span>
          <div class="skill-level">
            <i class="fas fa-star" style="color: var(--primary); font-size: 0.8rem; margin-right: 5px;"></i>
            <span class="skill-level-text">${skillLevel}</span>
          </div>
        </div>
      </div>
      <div class="skill-progress-container">
        <div class="skill-progress-bg">
          <div class="skill-progress-fill" data-percentage="${skill.level}"></div>
          <div class="skill-progress-glow"></div>
        </div>
        <div class="skill-percentage">
          <span>Proficiency</span>
          <span class="skill-percentage-value">${skill.level}%</span>
        </div>
      </div>
    `;
    skillMeters.appendChild(skillMeter);
  });

  // Check if project contributions section exists before trying to populate it
  const projectContributions = document.getElementById("projectContributions");
  if (projectContributions) {
    projectContributions.innerHTML = "";
    memberData.projects.forEach((project) => {
      const projectDiv = document.createElement("div");
      projectDiv.className = "project-contribution";
      projectDiv.innerHTML = `
        <h3>${project.name}</h3>
        <p>${project.description}</p>
      `;
      projectContributions.appendChild(projectDiv);
    });
  }

  // Populate tasks with enhanced UI
  const memberTasks = document.getElementById("memberTasks");
  const memberTasksTimeline = document.getElementById("memberTasksTimeline");
  memberTasks.innerHTML = "";
  memberTasksTimeline.innerHTML = "";

  if (memberData.tasks && memberData.tasks.length > 0) {
    // Sort tasks by status and start date
    const sortedTasks = [...memberData.tasks].sort((a, b) => {
      // First sort by status priority (In Progress, Pending, Completed)
      const statusOrder = { "in progress": 0, pending: 1, completed: 2 };
      const statusA = a.status.toLowerCase();
      const statusB = b.status.toLowerCase();

      if (statusOrder[statusA] !== statusOrder[statusB]) {
        return statusOrder[statusA] - statusOrder[statusB];
      }

      // Then sort by start date (most recent first)
      const dateA = a.startDate ? new Date(a.startDate) : new Date(0);
      const dateB = b.startDate ? new Date(b.startDate) : new Date(0);
      return dateB - dateA;
    });

    // Populate card view
    sortedTasks.forEach((task, index) => {
      const taskDiv = document.createElement("div");
      taskDiv.className = "task-item";
      taskDiv.setAttribute(
        "data-status",
        task.status.toLowerCase().replace(/\s+/g, "-")
      );
      taskDiv.setAttribute(
        "data-category",
        task.category
          ? task.category.toLowerCase().replace(/\s+/g, "-")
          : "general"
      );

      // Add status class for styling
      let statusClass = "";
      let statusIcon = "";
      switch (task.status.toLowerCase()) {
        case "completed":
          statusClass = "completed";
          statusIcon = "check-circle";
          taskDiv.classList.add("completed");
          break;
        case "in progress":
          statusClass = "in-progress";
          statusIcon = "spinner";
          taskDiv.classList.add("in-progress");
          break;
        default:
          statusClass = "pending";
          statusIcon = "clock";
          taskDiv.classList.add("pending");
      }

      // Calculate progress percentage based on status
      let progressPercentage = 0;
      switch (task.status.toLowerCase()) {
        case "completed":
          progressPercentage = 100;
          break;
        case "in progress":
          progressPercentage = Math.floor(Math.random() * 41) + 30; // Random between 30-70%
          break;
        case "pending":
          progressPercentage = Math.floor(Math.random() * 20); // Random between 0-20%
          break;
      }

      // Add animation delay based on index
      taskDiv.style.animationDelay = `${0.1 * index}s`;

      // Create task HTML with enhanced UI elements
      taskDiv.innerHTML = `
        <div class="task-decoration"></div>
        <div class="task-actions">
          <div class="task-action-btn" data-action="details">
            <i class="fas fa-info-circle"></i>
            <span class="task-action-tooltip">Details</span>
          </div>
          <div class="task-action-btn" data-action="share">
            <i class="fas fa-share-alt"></i>
            <span class="task-action-tooltip">Share</span>
          </div>
        </div>
        <div class="task-header">
          <div class="task-title-wrapper">
            <h3 class="task-title"><i class="fas fa-${
              task.icon || "check-circle"
            }"></i> ${task.title}</h3>
            <div class="task-subtitle"><i class="fas fa-layer-group"></i> ${
              task.category || "General"
            }</div>
          </div>
          <span class="task-status ${statusClass}"><i class="fas fa-${statusIcon}"></i> ${
        task.status
      }</span>
        </div>

        <div class="task-content">
          <div class="task-details">${task.description}</div>
          <div class="task-details-toggle">Read more <i class="fas fa-chevron-down"></i></div>

          <div class="task-progress">
            <div class="progress-bar-container">
              <div class="progress-bar-fill" data-percentage="${progressPercentage}"></div>
            </div>
            <div class="progress-percentage">${progressPercentage}% Complete</div>
          </div>

          <div class="task-meta">
            <span class="task-meta-item"><i class="fas fa-calendar-day"></i> ${
              task.startDate || "N/A"
            }</span>
            <span class="task-meta-item"><i class="fas fa-calendar-check"></i> ${
              task.endDate || "N/A"
            }</span>
            <span class="task-meta-item"><i class="fas fa-clock"></i> ${
              calculateHours(task.startDate, task.endDate) || task.hours || "0"
            } hours</span>
            <span class="task-meta-item"><i class="fas fa-code-branch"></i> ${
              task.complexity || "Medium"
            }</span>
          </div>
        </div>
      `;

      memberTasks.appendChild(taskDiv);

      // Also create timeline view entry
      const timelineItem = document.createElement("div");
      timelineItem.className = "task-item";
      timelineItem.setAttribute(
        "data-status",
        task.status.toLowerCase().replace(/\s+/g, "-")
      );

      // Add timeline marker
      const timelineMarker = document.createElement("div");
      timelineMarker.className = "timeline-marker";
      memberTasksTimeline.appendChild(timelineMarker);

      // Simplified version for timeline view
      timelineItem.innerHTML = `
        <div class="task-header">
          <div class="task-title-wrapper">
            <h3 class="task-title"><i class="fas fa-${
              task.icon || "check-circle"
            }"></i> ${task.title}</h3>
            <div class="task-subtitle"><i class="fas fa-calendar-day"></i> ${
              task.startDate || "N/A"
            } - <i class="fas fa-calendar-check"></i> ${
        task.endDate || "N/A"
      }</div>
          </div>
          <span class="task-status ${statusClass}"><i class="fas fa-${statusIcon}"></i> ${
        task.status
      }</span>
        </div>
        <p class="task-details">${task.description}</p>
        <div class="task-meta">
          <span class="task-meta-item"><i class="fas fa-layer-group"></i> ${
            task.category || "General"
          }</span>
          <span class="task-meta-item"><i class="fas fa-clock"></i> ${
            calculateHours(task.startDate, task.endDate) || task.hours || "0"
          } hours</span>
        </div>
      `;

      memberTasksTimeline.appendChild(timelineItem);
    });

    // Initialize task interactions after populating
    setTimeout(initializeTaskInteractions, 100);
  } else {
    // Empty state with icon and helpful message
    const emptyState = `
      <div class="no-tasks">
        <i class="fas fa-clipboard-list"></i>
        <h3>No Tasks Found</h3>
        <p>This team member doesn't have any assigned tasks yet. Check back later for updates on their project responsibilities.</p>
      </div>
    `;
    memberTasks.innerHTML = emptyState;
    memberTasksTimeline.innerHTML = emptyState;
  }

  // Populate contact details
  const contactDetails = document.getElementById("contactDetails");
  contactDetails.innerHTML = "";
  if (memberData.contact.email) {
    contactDetails.innerHTML += `
      <div class="contact-item">
        <div class="contact-icon"><i class="fas fa-envelope"></i></div>
        <div class="contact-info">${memberData.contact.email}</div>
      </div>
    `;
  }

  if (memberData.contact.phone) {
    contactDetails.innerHTML += `
      <div class="contact-item">
        <div class="contact-icon"><i class="fas fa-phone"></i></div>
        <div class="contact-info">${memberData.contact.phone}</div>
      </div>
    `;
  }

  if (memberData.contact.office) {
    contactDetails.innerHTML += `
      <div class="contact-item">
        <div class="contact-icon"><i class="fas fa-map-marker-alt"></i></div>
        <div class="contact-info">${memberData.contact.office}</div>
      </div>
    `;
  }

  // Populate social links
  const socialLinks = document.getElementById("socialLinks");
  socialLinks.innerHTML = "";

  if (memberData.social.github) {
    socialLinks.innerHTML += `
      <a href="${memberData.social.github}" class="social-link" target="_blank" rel="noopener noreferrer">
        <i class="fab fa-github"></i>
      </a>
    `;
  }

  if (memberData.social.linkedin) {
    socialLinks.innerHTML += `
      <a href="${memberData.social.linkedin}" class="social-link" target="_blank" rel="noopener noreferrer">
        <i class="fab fa-linkedin-in"></i>
      </a>
    `;
  }

  if (memberData.social.twitter) {
    socialLinks.innerHTML += `
      <a href="${memberData.social.twitter}" class="social-link" target="_blank" rel="noopener noreferrer">
        <i class="fab fa-twitter"></i>
      </a>
    `;
  }

  if (memberData.social.instagram) {
    socialLinks.innerHTML += `
      <a href="${memberData.social.instagram}" class="social-link" target="_blank" rel="noopener noreferrer">
        <i class="fab fa-instagram"></i>
      </a>
    `;
  }

  if (memberData.social.facebook) {
    socialLinks.innerHTML += `
      <a href="${memberData.social.facebook}" class="social-link" target="_blank" rel="noopener noreferrer">
        <i class="fab fa-facebook-f"></i>
      </a>
    `;
  }
}

/**
 * Get appropriate icon for a skill
 */
function getSkillIcon(skillName) {
  const skillIcons = {
    // Programming languages
    JavaScript: "fab fa-js",
    TypeScript: "fab fa-js",
    Java: "fab fa-java",
    Python: "fab fa-python",
    PHP: "fab fa-php",
    Ruby: "fab fa-ruby",
    Swift: "fab fa-swift",
    Kotlin: "fas fa-mobile-alt",
    "C#": "fab fa-microsoft",
    "C++": "fas fa-code",
    C: "fas fa-code",
    Go: "fas fa-code",
    Rust: "fas fa-cog",

    // Web technologies
    HTML: "fab fa-html5",
    HTML5: "fab fa-html5",
    CSS: "fab fa-css3-alt",
    CSS3: "fab fa-css3-alt",
    SASS: "fab fa-sass",
    SCSS: "fab fa-sass",
    Bootstrap: "fab fa-bootstrap",
    Tailwind: "fab fa-css3",
    "Tailwind CSS": "fab fa-css3",

    // Frameworks
    React: "fab fa-react",
    "React.js": "fab fa-react",
    Angular: "fab fa-angular",
    Vue: "fab fa-vuejs",
    "Vue.js": "fab fa-vuejs",
    "Node.js": "fab fa-node-js",
    "Express.js": "fab fa-node-js",
    Django: "fab fa-python",
    Flask: "fab fa-python",
    Laravel: "fab fa-laravel",
    Spring: "fab fa-java",

    // Design and UI
    "UI Design": "fas fa-palette",
    "UX Design": "fas fa-user",
    "UI/UX": "fas fa-user-circle",
    "UI/UX Design": "fas fa-user-circle",
    Figma: "fab fa-figma",
    "Adobe XD": "fab fa-adobe",
    Sketch: "fas fa-pencil-ruler",

    // Databases
    SQL: "fas fa-database",
    MySQL: "fas fa-database",
    PostgreSQL: "fas fa-database",
    MongoDB: "fas fa-database",
    NoSQL: "fas fa-database",
    Firebase: "fas fa-fire",

    // DevOps & Tools
    Git: "fab fa-git-alt",
    GitHub: "fab fa-github",
    Docker: "fab fa-docker",
    AWS: "fab fa-aws",
    Azure: "fab fa-microsoft",
    GCP: "fab fa-google",
    "CI/CD": "fas fa-sync",
    Jenkins: "fab fa-jenkins",

    // AI & Machine Learning
    "Machine Learning": "fas fa-brain",
    "Deep Learning": "fas fa-brain",
    TensorFlow: "fas fa-network-wired",
    PyTorch: "fas fa-fire",
    NLP: "fas fa-comment-alt",
    "Computer Vision": "fas fa-eye",
    "Data Science": "fas fa-chart-bar",
    "Data Analysis": "fas fa-chart-line",

    // Mobile
    iOS: "fab fa-apple",
    Android: "fab fa-android",
    "React Native": "fab fa-react",
    Flutter: "fab fa-google",
    Swift: "fab fa-swift",

    // Others
    Communication: "fas fa-comments",
    Leadership: "fas fa-users",
    "Project Management": "fas fa-tasks",
    Agile: "fas fa-sync",
    Scrum: "fas fa-users-cog",
    Testing: "fas fa-vial",
    QA: "fas fa-check-circle",
    SEO: "fas fa-search",
    Analytics: "fas fa-chart-pie",
  };

  return skillIcons[skillName] || "fas fa-star";
}

/**
 * Get tooltip text for a skill
 */
function getSkillTooltip(skillName) {
  // Personalized tooltip messages based on skill level
  const tooltips = {
    JavaScript: "Frontend & backend development",
    React: "UI component development",
    "Node.js": "Server-side JavaScript",
    Python: "Data analysis & automation",
    "Machine Learning": "AI model development",
    "Data Analysis": "Extracting insights from data",
    "UI/UX Design": "Creating intuitive interfaces",
    Leadership: "Team guidance & motivation",
    Communication: "Clear & effective messaging",
    "Project Management": "Organizing & delivering projects",
    DevOps: "CI/CD pipeline optimization",
    AWS: "Cloud infrastructure management",
    Docker: "Container deployment",
    MongoDB: "NoSQL database management",
    SQL: "Relational database queries",
    Git: "Version control & collaboration",
    Testing: "Quality assurance processes",
    Agile: "Iterative development methodology",
  };

  return tooltips[skillName] || `Expertise in ${skillName}`;
}

/**
 * Get skill category for classification
 */
function getSkillCategory(skillName) {
  // Map skills to categories
  const categories = {
    // Programming languages
    JavaScript: "Frontend",
    TypeScript: "Frontend",
    Java: "Backend",
    Python: "Backend",
    PHP: "Backend",
    Ruby: "Backend",
    Swift: "Mobile",
    Kotlin: "Mobile",
    "C#": "Backend",
    "C++": "Backend",
    C: "Backend",
    Go: "Backend",
    Rust: "Backend",

    // Web technologies
    HTML: "Frontend",
    HTML5: "Frontend",
    CSS: "Frontend",
    CSS3: "Frontend",
    SASS: "Frontend",
    SCSS: "Frontend",
    Bootstrap: "Frontend",
    Tailwind: "Frontend",
    "Tailwind CSS": "Frontend",

    // Frameworks
    React: "Frontend",
    "React.js": "Frontend",
    Angular: "Frontend",
    Vue: "Frontend",
    "Vue.js": "Frontend",
    "Node.js": "Backend",
    "Express.js": "Backend",
    Django: "Backend",
    Flask: "Backend",
    Laravel: "Backend",
    Spring: "Backend",

    // Design and UI
    "UI Design": "Design",
    "UX Design": "Design",
    "UI/UX": "Design",
    "UI/UX Design": "Design",
    Figma: "Design",
    "Adobe XD": "Design",
    Sketch: "Design",

    // Databases
    SQL: "Database",
    MySQL: "Database",
    PostgreSQL: "Database",
    MongoDB: "Database",
    NoSQL: "Database",
    Firebase: "Database",

    // DevOps & Tools
    Git: "DevOps",
    GitHub: "DevOps",
    Docker: "DevOps",
    AWS: "DevOps",
    Azure: "DevOps",
    GCP: "DevOps",
    "CI/CD": "DevOps",
    Jenkins: "DevOps",

    // AI & Machine Learning
    "Machine Learning": "AI/ML",
    "Deep Learning": "AI/ML",
    TensorFlow: "AI/ML",
    PyTorch: "AI/ML",
    NLP: "AI/ML",
    "Computer Vision": "AI/ML",
    "Data Science": "AI/ML",
    "Data Analysis": "AI/ML",

    // Mobile
    iOS: "Mobile",
    Android: "Mobile",
    "React Native": "Mobile",
    Flutter: "Mobile",

    // Others
    Communication: "Soft Skill",
    Leadership: "Soft Skill",
    "Project Management": "Soft Skill",
    Agile: "Process",
    Scrum: "Process",
    Testing: "QA",
    QA: "QA",
    SEO: "Marketing",
    Analytics: "Analytics",
  };

  return categories[skillName] || "Other";
}

/**
 * Get descriptive text for skill level
 */
function getSkillLevelText(level) {
  if (level >= 90) return "Expert";
  if (level >= 75) return "Advanced";
  if (level >= 60) return "Proficient";
  if (level >= 40) return "Intermediate";
  return "Beginner";
}

/**
 * Set up tab functionality for the profile page
 */
function setupTabs() {
  console.log("Setting up profile tabs with direct event delegation");

  // Use event delegation on the document level to handle tab clicks
  document.addEventListener("click", function (e) {
    // Find if a profile tab was clicked
    const clickedTab = e.target.closest(".profile-tab");
    if (!clickedTab) return; // Not a tab click

    e.preventDefault();
    e.stopPropagation();

    const tabName = clickedTab.getAttribute("data-tab");
    console.log("Tab clicked via delegation:", tabName);

    if (!tabName) {
      console.error("Tab clicked but no data-tab attribute found");
      return;
    }

    // Remove active class from all tabs
    document.querySelectorAll(".profile-tab").forEach((tab) => {
      tab.classList.remove("active");
    });

    // Add active class to clicked tab
    clickedTab.classList.add("active");

    // Hide all tab contents
    document.querySelectorAll(".profile-tab-content").forEach((content) => {
      content.classList.remove("active");
    });

    // Show selected tab content
    const tabContentId = `${tabName}-content`;
    const targetContent = document.getElementById(tabContentId);

    if (targetContent) {
      targetContent.classList.add("active");
      console.log("Activated tab content:", tabContentId);

      // Re-trigger animations for skills when tab becomes visible
      if (tabContentId === "skills-content") {
        const progressBars = document.querySelectorAll(
          "#skillMeters .skill-progress-fill"
        );

        progressBars.forEach((bar, index) => {
          // Reset the width first
          bar.style.width = "0%";

          // Then animate it after a short delay
          const percentage = bar.getAttribute("data-percentage");
          setTimeout(() => {
            bar.style.width = `${percentage}%`;
          }, 100 * index);
        });

        // Re-apply tilt effect if available
        if (typeof VanillaTilt !== "undefined") {
          VanillaTilt.init(document.querySelectorAll(".skill-meter"), {
            max: 8,
            speed: 400,
            glare: true,
            "max-glare": 0.1,
            scale: 1.02,
          });
        }
      }
    } else {
      console.error(`Tab content with id "${tabContentId}" not found`);
    }

    // Update tab indicator position after changing tabs
    updateTabIndicator();
  });

  // Make sure at least one tab is active
  const tabs = document.querySelectorAll(".profile-tab");
  if (!document.querySelector(".profile-tab.active") && tabs.length > 0) {
    tabs[0].classList.add("active");
    const firstTabId = `${tabs[0].getAttribute("data-tab")}-content`;
    const firstContent = document.getElementById(firstTabId);
    if (firstContent) {
      firstContent.classList.add("active");
    }
  }

  // Set initial tab indicator position
  updateTabIndicator();

  console.log("Profile tabs setup complete with event delegation");
}

/**
 * Format team name for display
 */
function formatTeamName(teamCode) {
  if (!teamCode) return "Unknown Team";

  switch (teamCode) {
    case "web-team":
      return "Web Development Team";
    case "mobile-team":
      return "Mobile Development Team";
    case "ai-team":
      return "AI Team";
    case "embedded-team":
      return "Embedded Systems Team";
    case "supervisors-team":
      return "Supervisors";
    default:
      return teamCode
        .replace("-team", " Team")
        .replace(/\b\w/g, (c) => c.toUpperCase());
  }
}

/**
 * Set appropriate icon based on team
 */
function setTeamIcon(iconElement, teamCode) {
  switch (teamCode) {
    case "web-team":
      iconElement.className = "fas fa-globe";
      break;
    case "mobile-team":
      iconElement.className = "fas fa-mobile-alt";
      break;
    case "ai-team":
      iconElement.className = "fas fa-brain";
      break;
    case "embedded-team":
      iconElement.className = "fas fa-microchip";
      break;
    case "supervisors-team":
      iconElement.className = "fas fa-user-tie";
      break;
    default:
      iconElement.className = "fas fa-users";
  }
}

/**
 * Initialize task interactions like expand/collapse, filtering, and view switching
 */
function initializeTaskInteractions() {
  // Setup task details toggle (expand/collapse)
  const detailsToggles = document.querySelectorAll(".task-details-toggle");
  detailsToggles.forEach((toggle) => {
    toggle.addEventListener("click", function () {
      const details = this.previousElementSibling;

      if (details.classList.contains("expanded")) {
        details.classList.remove("expanded");
        this.innerHTML = 'Read more <i class="fas fa-chevron-down"></i>';
      } else {
        details.classList.add("expanded");
        this.innerHTML = 'Read less <i class="fas fa-chevron-up"></i>';
      }
    });
  });

  // Initialize progress bars animation
  const progressBars = document.querySelectorAll(".progress-bar-fill");
  progressBars.forEach((bar, index) => {
    const percentage = bar.getAttribute("data-percentage");
    setTimeout(() => {
      bar.style.width = `${percentage}%`;
    }, 100 * index);
  });

  // Setup action buttons functionality
  const actionButtons = document.querySelectorAll(".task-action-btn");
  actionButtons.forEach((btn) => {
    btn.addEventListener("click", function (e) {
      e.stopPropagation(); // Prevent event bubbling

      const action = this.getAttribute("data-action");
      const taskItem = this.closest(".task-item");
      const taskTitle = taskItem
        .querySelector(".task-title")
        .textContent.trim();

      // Handle different actions
      switch (action) {
        case "details":
          // Show task details in a modal or expand the card
          alert(`Viewing details for task: ${taskTitle}`);
          break;

        case "share":
          // Share task information
          const shareText = `Check out this task: ${taskTitle}`;

          // Try to use the Web Share API if available
          if (navigator.share) {
            navigator
              .share({
                title: "Task Information",
                text: shareText,
                url: window.location.href,
              })
              .catch((err) => {
                console.log("Error sharing:", err);
                // Fallback
                alert(`Share: ${shareText}\n\nURL: ${window.location.href}`);
              });
          } else {
            // Fallback for browsers that don't support Web Share API
            alert(`Share: ${shareText}\n\nURL: ${window.location.href}`);
          }
          break;

        default:
          console.log(`Action '${action}' not implemented yet`);
      }

      // Add a visual feedback effect
      this.classList.add("action-clicked");
      setTimeout(() => {
        this.classList.remove("action-clicked");
      }, 300);
    });
  });

  // Apply tilt effect to task cards if VanillaTilt is available
  if (typeof VanillaTilt !== "undefined") {
    VanillaTilt.init(document.querySelectorAll(".task-item"), {
      max: 5,
      speed: 400,
      glare: false,
      scale: 1.02,
      gyroscope: false,
    });
  }
}

/**
 * Setup task filtering and view switching functionality
 */
function setupTaskControls() {
  // Task filtering
  const filterButtons = document.querySelectorAll(".filter-btn");
  filterButtons.forEach((button) => {
    button.addEventListener("click", function () {
      // Update active state
      filterButtons.forEach((btn) => btn.classList.remove("active"));
      this.classList.add("active");

      const filter = this.getAttribute("data-filter");
      const taskItems = document.querySelectorAll(".task-item");

      taskItems.forEach((item) => {
        if (filter === "all") {
          item.style.display = "";
        } else {
          const status = item.getAttribute("data-status");
          if (status === filter) {
            item.style.display = "";
          } else {
            item.style.display = "none";
          }
        }
      });
    });
  });

  // View switching (card vs timeline)
  const viewButtons = document.querySelectorAll(".view-btn");
  const cardView = document.getElementById("memberTasks");
  const timelineView = document.getElementById("memberTasksTimeline");

  viewButtons.forEach((button) => {
    button.addEventListener("click", function () {
      // Update active state
      viewButtons.forEach((btn) => btn.classList.remove("active"));
      this.classList.add("active");

      const view = this.getAttribute("data-view");

      if (view === "card") {
        cardView.style.display = "";
        timelineView.style.display = "none";
      } else if (view === "timeline") {
        cardView.style.display = "none";
        timelineView.style.display = "";
      }
    });
  });
}

/**
 * Get member data from the mock database
 * In a real application, this would be fetched from an API
 */
function getMemberData(memberId, team, role) {
  // Mock member data
  const memberDatabase = {
    // Web Team
    "john-doe": {
      bio: "John is an experienced frontend developer specializing in modern JavaScript frameworks and responsive web design. He leads the frontend team in architecting scalable, maintainable UI components.",
      education: [
        "M.S. Computer Science, Tech University, 2023",
        "B.S. Software Engineering, State University, 2021",
      ],
      skills: [
        { name: "JavaScript", level: 95 },
        { name: "React", level: 90 },
        { name: "CSS/SASS", level: 85 },
        { name: "UI/UX Design", level: 75 },
        { name: "Node.js", level: 70 },
      ],
      projects: [
        {
          name: "User Interface Architecture",
          description:
            "Designed and implemented the core UI architecture and component system for the project.",
        },
        {
          name: "Performance Optimization",
          description:
            "Improved application load time by 40% through code splitting and lazy loading techniques.",
        },
      ],
      contact: {
        email: "<EMAIL>",
        phone: "+****************",
        office: "Engineering Building, Room 305",
      },
      social: {
        github: "https://github.com/",
        linkedin: "https://linkedin.com/",
        twitter: "https://twitter.com/",
      },
      tasks: [
        {
          title: "Design UI Component System",
          description:
            "Created a reusable component library that standardizes UI elements across the entire application.",
          status: "Completed",
          startDate: "02/10/2025",
          endDate: "02/14/2025",
          hours: 24,
          category: "Frontend",
          icon: "object-group",
        },
        {
          title: "Implement User Authentication Flow",
          description:
            "Developed secure authentication and authorization system with multiple sign-in options.",
          status: "Completed",
          startDate: "02/17/2025",
          endDate: "02/20/2025",
          hours: 18,
          category: "Frontend",
          icon: "user-lock",
        },
        {
          title: "Optimize Dashboard Performance",
          description:
            "Improved dashboard loading time by implementing lazy loading and optimizing component rendering.",
          status: "In Progress",
          startDate: "02/24/2025",
          endDate: "02/28/2025",
          hours: 12,
          category: "Performance",
          icon: "tachometer-alt",
        },
      ],
    },
    // AI Team template
    "dr-james-lin": {
      bio: "Dr. James Lin is a machine learning researcher with expertise in neural networks and computer vision. He leads the AI team in developing intelligent algorithms for image recognition and data analysis.",
      education: [
        "Ph.D. Artificial Intelligence, MIT, 2018",
        "M.S. Computer Science, Stanford University, 2015",
        "B.S. Mathematics, UC Berkeley, 2013",
      ],
      skills: [
        { name: "Machine Learning", level: 95 },
        { name: "TensorFlow", level: 90 },
        { name: "Computer Vision", level: 85 },
        { name: "Python", level: 95 },
        { name: "Data Analysis", level: 90 },
      ],
      projects: [
        {
          name: "Object Recognition System",
          description:
            "Developed an advanced object recognition system with 98% accuracy for identifying urban infrastructure components.",
        },
        {
          name: "Predictive Analytics Engine",
          description:
            "Created algorithms for predicting system failures based on sensor data patterns.",
        },
      ],
      contact: {
        email: "<EMAIL>",
        phone: "+****************",
        office: "AI Lab, Building B, Room 201",
      },
      social: {
        github: "https://github.com/",
        linkedin: "https://linkedin.com/",
        twitter: "https://twitter.com/",
      },
      tasks: [
        {
          title: "Develop Image Recognition Algorithm",
          description:
            "Created and trained a neural network model for detecting crop diseases from images with 95% accuracy.",
          status: "Completed",
          startDate: "02/03/2025",
          endDate: "02/14/2025",
          hours: 40,
          category: "Machine Learning",
          icon: "image",
        },
        {
          title: "Optimize Model for Edge Devices",
          description:
            "Reduced model size by 60% while maintaining accuracy to enable deployment on resource-constrained devices.",
          status: "Completed",
          startDate: "02/17/2025",
          endDate: "02/21/2025",
          hours: 30,
          category: "Optimization",
          icon: "microchip",
        },
        {
          title: "Implement Real-time Analysis Pipeline",
          description:
            "Built data processing pipeline for real-time analysis of sensor data with alerts for anomalous patterns.",
          status: "In Progress",
          startDate: "02/24/2025",
          endDate: "03/01/2025",
          hours: 22,
          category: "Data Science",
          icon: "stream",
        },
      ],
    },
    // Default template
    default: {
      bio: "This team member is a valuable contributor to our project, bringing specialized expertise and dedication to their role.",
      education: [
        "Relevant Degree, University Name, Year",
        "Professional Certification, Organization, Year",
      ],
      skills: [
        { name: "Skill 1", level: 85 },
        { name: "Skill 2", level: 75 },
        { name: "Skill 3", level: 90 },
      ],
      projects: [
        {
          name: "Project Component",
          description:
            "Contributed to development of key project components and features.",
        },
        {
          name: "Technical Implementation",
          description:
            "Implemented technical solutions to address project requirements.",
        },
      ],
      contact: {
        email: "<EMAIL>",
        phone: "+****************",
        office: "Project Building, Room 101",
      },
      social: {
        github: "https://github.com/",
        linkedin: "https://linkedin.com/",
        twitter: "https://twitter.com/",
      },
      tasks: [
        {
          title: "Feature Development",
          description:
            "Working on implementation of assigned project features and components.",
          status: "In Progress",
          startDate: "02/24/2025",
          endDate: "03/01/2025",
          hours: 15,
          category: "Development",
          icon: "code",
        },
      ],
    },
  };

  // Generate a member ID if not provided
  const generatedId =
    memberId ||
    (role ? role.toLowerCase().replace(/\s+/g, "-") : "team-member");

  // Return member data if exists, otherwise customize the default template
  if (memberDatabase[generatedId]) {
    return memberDatabase[generatedId];
  } else {
    // Customize default template based on role and team
    const defaultData = JSON.parse(JSON.stringify(memberDatabase.default)); // Deep copy

    // Customize bio based on role and team
    if (role && team) {
      const teamName = formatTeamName(team);
      defaultData.bio = `As a ${role} on the ${teamName}, this member contributes specialized skills and expertise to advance our project goals.`;
    }

    return defaultData;
  }
}
