// Enhanced service worker with better caching strategies

const CACHE_NAME = "graduation-project-v1";
const ASSETS = [
    "/",
    "/index.html",
    "/css/components.css",
    "/css/dark-theme.css",
    "/js/main.js",
    "/js/gallery.js",
    "/js/teams.js",
    "/js/theme.js",
];

// Install event - cache assets
self.addEventListener("install", (event) => {
    event.waitUntil(
        caches
            .open(CACHE_NAME)
            .then((cache) => cache.addAll(ASSETS))
            .then(() => self.skipWaiting())
    );
});

// Activate event - clean old caches
self.addEventListener("activate", (event) => {
    event.waitUntil(
        caches
            .keys()
            .then((keys) =>
                Promise.all(
                    keys
                        .filter((key) => key !== CACHE_NAME)
                        .map((key) => caches.delete(key))
                )
            )
            .then(() => self.clients.claim())
    );
});

// Fetch event - serve from cache, fall back to network
self.addEventListener("fetch", (event) => {
    event.respondWith(
        caches.match(event.request).then((response) => {
            if (response) {
                return response;
            }

            // Clone the request - request can only be used once
            const fetchRequest = event.request.clone();

            return fetch(fetchRequest).then((response) => {
                // Check if valid response
                if (!response || response.status !== 200 || response.type !== "basic") {
                    return response;
                }

                // Clone the response - response can only be used once
                const responseToCache = response.clone();

                caches.open(CACHE_NAME).then((cache) => {
                    // Only cache same-origin requests
                    if (event.request.url.startsWith(self.location.origin)) {
                        cache.put(event.request, responseToCache);
                    }
                });

                return response;
            });
        })
    );
});
