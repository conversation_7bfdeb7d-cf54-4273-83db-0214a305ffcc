<div class="task-item in-progress" data-status="in-progress" data-category="performance" style="animation-delay: 0s; will-change: transform; transform: perspective(1000px) rotateX(0deg) rotateY(0deg) scale3d(1, 1, 1);">
  <div class="task-decoration"></div>
  <div class="task-actions">
    <div class="task-action-btn" data-action="details">
      <i class="fas fa-info-circle"></i>
      <span class="task-action-tooltip">Details</span>
    </div>
    <div class="task-action-btn" data-action="share">
      <i class="fas fa-share-alt"></i>
      <span class="task-action-tooltip">Share</span>
    </div>
  </div>
  <div class="task-header">
    <div class="task-title-wrapper">
      <h3 class="task-title"><i class="fas fa-tachometer-alt"></i> Optimize Dashboard Performance</h3>
      <div class="task-subtitle"><i class="fas fa-layer-group"></i> Performance</div>
    </div>
    <span class="task-status in-progress"><i class="fas fa-spinner"></i> In Progress</span>
  </div>

  <div class="task-content">
    <div class="task-details">Improved dashboard loading time by implementing lazy loading and optimizing component rendering.</div>
    <div class="task-details-toggle">Read more <i class="fas fa-chevron-down"></i></div>

    <div class="task-progress">
      <div class="progress-bar-container">
        <div class="progress-bar-fill" data-percentage="52" style="width: 52%;"></div>
      </div>
      <div class="progress-percentage">52% Complete</div>
    </div>

    <div class="task-meta">
      <span class="task-meta-item"><i class="fas fa-calendar-day"></i> 02/24/2025</span>
      <span class="task-meta-item"><i class="fas fa-calendar-check"></i> 02/28/2025</span>
      <span class="task-meta-item"><i class="fas fa-clock"></i> 12 hours</span>
      <span class="task-meta-item"><i class="fas fa-code-branch"></i> Medium</span>
    </div>
  </div>
</div>
