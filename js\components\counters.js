// Animate stat counters when they come into view
function initAnimatedCounters() {
    const statNumbers = document.querySelectorAll(".stat-number[data-counter]");

    // Initialize counters immediately with 0
    statNumbers.forEach((stat) => {
        stat.textContent = "0";
    });

    const observer = new IntersectionObserver(
        (entries) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    const target = entry.target;
                    const countTo = parseInt(target.getAttribute("data-counter"));

                    // Make sure countTo is a valid number
                    if (isNaN(countTo)) {
                        console.error("Invalid counter value:", target);
                        return;
                    }

                    // Use a smaller increment for smoother animation
                    const duration = 2000; // ms
                    const interval = Math.max(30, Math.floor(duration / countTo));

                    let count = 0;
                    const counter = setInterval(() => {
                        count += 1;
                        target.textContent = count;

                        if (count >= countTo) {
                            // Ensure final value is displayed correctly
                            target.textContent = countTo;
                            clearInterval(counter);

                            // Add a final animation once completed
                            target.style.animation = "none";
                            setTimeout(() => {
                                target.style.animation = "finalPulse 0.5s ease-out";
                            }, 10);
                        }
                    }, interval);

                    observer.unobserve(target);
                }
            });
        },
        {
            threshold: 0.2,
        }
    );

    // Observe all stat counters
    statNumbers.forEach((stat) => {
        observer.observe(stat);
    });

    // Add the final pulse animation if it doesn't exist
    if (!document.querySelector('style[data-id="pulse-animation"]')) {
        const style = document.createElement("style");
        style.setAttribute("data-id", "pulse-animation");
        style.textContent = `
      @keyframes finalPulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
      }
    `;
        document.head.appendChild(style);
    }
}