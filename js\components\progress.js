function addProjectProgressVisualization() {
    // Create the progress section
    const progressSection = document.getElementById('progress');

    if (!progressSection) {
        console.error('Progress section not found');
        return;
    }

    // Insert the section before the documentation section
    const documentationSection = document.getElementById("documentation");
    if (documentationSection && progressSection.parentNode !== documentationSection.parentNode) {
        documentationSection.parentNode.insertBefore(
            progressSection,
            documentationSection
        );
    }

    // Add animation trigger
    const chartFills = document.querySelectorAll(".chart-fill, .mini-bar");
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
            if (entry.isIntersecting) {
                requestAnimationFrame(() => {
                    entry.target.style.transform = "scaleX(1)";
                });
                observer.unobserve(entry.target);
            }
        });
    });

    chartFills.forEach((fill) => observer.observe(fill));
}

// Run the function when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', function () {
    addProjectProgressVisualization();
});