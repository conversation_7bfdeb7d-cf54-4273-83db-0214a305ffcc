/**
 * Theme Management System
 * This file centralizes theme configuration and switching logic
 */

/* Import theme files */
@import "light.css";
@import "dark.css";

/* 
 * Combined Theme File
 * This file combines light and dark themes for standalone use
 */

/* Import the theme styles */
@import "light.css";
@import "dark.css";

/* Common theme transition properties */
body {
  transition: background-color 0.3s ease, color 0.3s ease,
    border-color 0.3s ease;
}

/* Theme toggle styles */
.theme-toggle {
  position: relative;
  width: 65px;
  height: 32px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 6px;
  cursor: pointer;
  overflow: hidden;
  box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.15),
    0 5px 10px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-toggle:hover {
  transform: translateY(-2px);
  box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.15), 0 8px 15px rgba(0, 0, 0, 0.1);
}

.theme-toggle:active {
  transform: translateY(0);
  box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.15), 0 3px 8px rgba(0, 0, 0, 0.08);
}

.theme-toggle i {
  font-size: 15px;
  color: white;
  z-index: 2;
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  opacity: 0.9;
}

.theme-toggle i.fa-sun {
  color: #ffd43b;
  transform: rotate(0deg) scale(1);
}

.theme-toggle i.fa-moon {
  color: #a5b4fc;
  transform: rotate(0deg) scale(1);
}

body.dark-mode .theme-toggle i.fa-sun {
  transform: rotate(120deg) scale(0.7);
  opacity: 0.5;
}

body.dark-mode .theme-toggle i.fa-moon {
  transform: rotate(0deg) scale(1.2);
  opacity: 1;
}

body:not(.dark-mode) .theme-toggle i.fa-sun {
  transform: rotate(0deg) scale(1.2);
  opacity: 1;
}

body:not(.dark-mode) .theme-toggle i.fa-moon {
  transform: rotate(-120deg) scale(0.7);
  opacity: 0.5;
}

.toggle-ball {
  position: absolute;
  width: 26px;
  height: 26px;
  background: white;
  border-radius: 50%;
  left: 3px;
  transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275),
    background-color 0.5s ease, box-shadow 0.3s ease;
  z-index: 1;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

body.dark-mode .toggle-ball {
  transform: translateX(33px);
  background-color: #334155;
  box-shadow: 0 0 10px rgba(165, 180, 252, 0.5),
    0 0 0 2px rgba(165, 180, 252, 0.2);
}

/* Theme change animation */
.theme-transition * {
  transition: background-color 0.4s ease, color 0.4s ease,
    border-color 0.4s ease, box-shadow 0.4s ease;
}

/* Add toggle glow effect */
@keyframes toggleGlow {
  0% {
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.5),
      inset 0 2px 5px rgba(0, 0, 0, 0.15);
  }
  50% {
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.8),
      inset 0 2px 5px rgba(0, 0, 0, 0.15);
  }
  100% {
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.5),
      inset 0 2px 5px rgba(0, 0, 0, 0.15);
  }
}

.theme-toggle.active {
  animation: toggleGlow 0.6s ease;
}
