/**
 * <PERSON><PERSON><PERSON> to update team member <PERSON><PERSON><PERSON> files to add social links
 * This script moves existing social links from contact to a new social object
 * and adds missing social links with empty values
 */

const fs = require('fs');
const path = require('path');

// Define the social networks we want to include
const socialNetworks = ['github', 'linkedin', 'twitter', 'instagram', 'facebook'];

// Get all team member JSON files
const teamFolders = ['web', 'mobile', 'ai', 'embedded', 'supervisors'];
let jsonFiles = [];

teamFolders.forEach(folder => {
  const folderPath = path.join('data', 'team', folder);
  
  // Skip if folder doesn't exist
  if (!fs.existsSync(folderPath)) {
    console.log(`Folder ${folderPath} does not exist, skipping...`);
    return;
  }
  
  // Get all JSON files in the folder
  const files = fs.readdirSync(folderPath)
    .filter(file => file.endsWith('.json'))
    .map(file => path.join(folderPath, file));
  
  jsonFiles = [...jsonFiles, ...files];
});

console.log(`Found ${jsonFiles.length} team member <PERSON><PERSON><PERSON> files to update.`);

// Process each JSON file
jsonFiles.forEach(filePath => {
  try {
    // Read the file
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const memberData = JSON.parse(fileContent);
    
    // Check if the file already has a social object
    if (!memberData.social) {
      memberData.social = {};
      
      // Move existing social links from contact to social
      socialNetworks.forEach(network => {
        if (memberData.contact && memberData.contact[network]) {
          memberData.social[network] = memberData.contact[network];
          delete memberData.contact[network];
        } else {
          // Add empty value for missing social links
          memberData.social[network] = "";
        }
      });
      
      // Write the updated content back to the file
      fs.writeFileSync(filePath, JSON.stringify(memberData, null, 2));
      console.log(`Updated ${filePath}`);
    } else {
      console.log(`${filePath} already has a social object, skipping...`);
    }
  } catch (error) {
    console.error(`Error updating ${filePath}:`, error);
  }
});

console.log('Social links update completed.');
