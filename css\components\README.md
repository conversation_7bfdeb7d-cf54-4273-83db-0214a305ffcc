# Component CSS Organization

This directory contains organized CSS files broken down by component type. This structure makes the codebase more
maintainable and improves development workflow.

## File Structure

- **base-components.css**: Core components like project overview, features grid, project highlights
- **team.css**: Team members, cards, filters, and skill meters
- **modals.css**: Modal dialogs, profiles, and lightbox styles
- **timeline.css**: Timeline components for sprints and project history
- **gallery.css**: Gallery grid, items, overlays, and loading states
- **contact.css**: Contact forms and information cards
- **testimonials.css**: Testimonial sliders, cards, and navigation
- **toc.css**: Table of contents component with mini-mode
- **documentation.css**: Documentation cards and layouts
- **utils.css**: Utility styles, animations, and media queries
