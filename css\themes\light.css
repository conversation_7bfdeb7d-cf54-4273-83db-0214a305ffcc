/**
 * Light Theme (Default)
 */

body {
  /* Light theme variables - defined in base/variables.css */
  background-color: var(--bg-light);
  color: var(--text-dark);
}

/* Light theme component styles */
.project-overview {
  background-color: var(--bg-white);
}

.feature-card,
.highlight-item,
.metric-card,
.team-tab,
.member-card,
.doc-card,
.contact-card {
  background-color: var(--bg-white);
}

.progress-card,
.sprint-chart-container,
.toc-container,
.roadmap-content {
  background: var(--bg-white);
}

/* Light theme specific overrides */
.theme-toggle {
  background: rgba(0, 0, 0, 0.1);
}

.theme-toggle i {
  color: var(--text-dark);
}

.toggle-ball {
  background: white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}
