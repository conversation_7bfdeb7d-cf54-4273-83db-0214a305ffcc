/* Member Profile Page Styles - Redesigned */

/* Main section styling with gradient background */
.member-profile-section {
  padding: 60px 0;
  min-height: 90vh;
  background: linear-gradient(135deg, #f9fafc 0%, #eef1f8 100%);
  position: relative;
  overflow: hidden;
}

/* Add decorative background elements */
.member-profile-section::before,
.member-profile-section::after {
  content: "";
  position: absolute;
  border-radius: 50%;
  z-index: 0;
  opacity: 0.05;
  animation: float 15s infinite ease-in-out;
}

.member-profile-section::before {
  width: 400px;
  height: 400px;
  background: linear-gradient(
    135deg,
    var(--primary) 0%,
    var(--primary-light) 100%
  );
  top: -100px;
  right: -100px;
}

.member-profile-section::after {
  width: 300px;
  height: 300px;
  background: linear-gradient(
    135deg,
    var(--primary-light) 0%,
    var(--accent) 100%
  );
  bottom: -50px;
  left: -50px;
  animation-delay: 2s;
  animation-duration: 18s;
}

@keyframes float {
  0%,
  100% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(-15px, 15px);
  }
  50% {
    transform: translate(10px, -10px);
  }
  75% {
    transform: translate(-5px, -15px);
  }
}

/* Back button with enhanced hover effect */
.profile-back-button {
  margin-bottom: 30px;
  position: relative;
  z-index: 5;
}

.profile-back-button a {
  display: inline-flex;
  align-items: center;
  color: var(--primary);
  font-weight: 500;
  text-decoration: none;
  padding: 10px 20px;
  border-radius: 30px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.15);
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  border: 1px solid rgba(255, 255, 255, 0.6);
}

.profile-back-button a:hover {
  transform: translateX(-8px);
  background: white;
  box-shadow: 0 8px 25px rgba(67, 97, 238, 0.25);
}

.profile-back-button i {
  margin-right: 10px;
  transition: transform 0.3s ease;
}

.profile-back-button a:hover i {
  transform: translateX(-5px);
}

/* Profile content layout */
.profile-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 30px;
  position: relative;
  z-index: 2;
}

/* Profile Header Card - Enhanced */
.profile-header-card {
  background: white;
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
  border: 1px solid rgba(255, 255, 255, 0.8);
  transition: transform 0.5s ease, box-shadow 0.5s ease;
}

.profile-header-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(67, 97, 238, 0.15);
}

/* Hero section with animated background */
.profile-hero {
  position: relative;
  padding: 50px 40px;
  min-height: 220px;
  display: flex;
  align-items: center;
  gap: 50px;
  overflow: hidden;
}

/* Animated background gradient */
.profile-bg-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(67, 97, 238, 0.8) 0%,
    rgba(76, 201, 240, 0.8) 100%
  );
  z-index: 0;
  transform: translateZ(0);
}

/* Animated particle effect */
.profile-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 1;
}

.particle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  animation: float-particle 8s infinite ease-in-out;
  opacity: 0.5;
}

@keyframes float-particle {
  0%,
  100% {
    transform: translate(0, 0);
  }
  50% {
    transform: translate(20px, -20px);
  }
}

/* Profile image container with glow effect */
.profile-img-container {
  position: relative;
  z-index: 2;
}

.profile-avatar {
  width: 170px;
  height: 170px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4895ef, #4361ee);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 70px;
  box-shadow: 0 10px 30px rgba(67, 97, 238, 0.4);
  border: 5px solid rgba(255, 255, 255, 0.8);
  position: relative;
  transition: transform 0.5s ease, box-shadow 0.5s ease;
  overflow: hidden;
}

/* Avatar hover effect */
.profile-avatar:hover {
  transform: scale(1.05) rotate(5deg);
  box-shadow: 0 15px 35px rgba(67, 97, 238, 0.5);
}

/* Avatar glow effect */
.profile-avatar::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    135deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transform: rotate(45deg);
  animation: shine 3s infinite;
}

@keyframes shine {
  0% {
    transform: translateX(-100%) rotate(45deg);
  }
  20%,
  100% {
    transform: translateX(100%) rotate(45deg);
  }
}

/* Basic info styling */
.profile-basic-info {
  position: relative;
  z-index: 2;
  flex: 1;
  color: white;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.profile-basic-info h1 {
  margin-bottom: 15px;
  font-size: 2.8rem;
  font-weight: 700;
  letter-spacing: 0.5px;
  background: linear-gradient(to right, #ffffff, #e0e7ff);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  animation: fadeIn 0.8s ease-out forwards;
}

.profile-basic-info .member-role {
  display: inline-block;
  color: white;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  padding: 10px 25px;
  border-radius: 30px;
  font-size: 1.1rem;
  font-weight: 500;
  margin-bottom: 20px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transform: translateY(20px);
  opacity: 0;
  animation: slideUp 0.6s 0.3s ease-out forwards;
}

.member-team {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 10px;
  transform: translateY(20px);
  opacity: 0;
  animation: slideUp 0.6s 0.5s ease-out forwards;
}

.member-team i {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.2rem;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Profile Details Card with elevated design */
.profile-details-card {
  background: white;
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.8);
  transition: transform 0.5s ease, box-shadow 0.5s ease;
  position: relative;
  z-index: 1;
}

.profile-details-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(67, 97, 238, 0.15);
}

/* Modernized tabs */
.profile-tabs {
  display: flex;
  background: rgba(249, 250, 252, 0.5);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 0 20px;
  position: relative;
}

.profile-tab {
  padding: 22px 30px;
  cursor: pointer;
  font-weight: 600;
  color: #7f8897;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.3px;
  user-select: none; /* Prevent text selection */
  z-index: 5; /* Ensure it's clickable */
}

.profile-tab:hover {
  color: var(--primary);
}

.profile-tab.active {
  color: var(--primary);
}

.profile-tab::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(to right, var(--primary), var(--primary-light));
  transform: scaleX(0);
  transform-origin: center;
  transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  border-radius: 3px 3px 0 0;
}

.profile-tab.active::before {
  transform: scaleX(1);
}

/* Tab indicator animation */
.tab-indicator {
  position: absolute;
  bottom: 0;
  height: 3px;
  background: linear-gradient(to right, var(--primary), var(--primary-light));
  border-radius: 3px 3px 0 0;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  z-index: 10; /* Ensure it's above other elements */
  pointer-events: none; /* Allow clicks to pass through */
}

/* Tab content with improved animations */
.profile-tab-content {
  display: none;
  padding: 40px;
  animation: fadeInUp 0.6s ease forwards;
}

.profile-tab-content.active {
  display: block;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Section styling */
.profile-section {
  margin-bottom: 45px;
}

.profile-section:last-child {
  margin-bottom: 0;
}

.profile-section h2 {
  font-size: 1.6rem;
  color: var(--text-dark);
  margin-bottom: 25px;
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
}

.profile-section h2 i {
  color: var(--primary);
  font-size: 1.4rem;
}

.profile-section h2::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -10px;
  width: 50px;
  height: 3px;
  background: linear-gradient(to right, var(--primary), var(--primary-light));
  border-radius: 3px;
}

/* Bio text with better typography */
#memberBio {
  color: var(--text-light);
  line-height: 1.8;
  font-size: 1.05rem;
  letter-spacing: 0.2px;
}

/* Enhanced list styles */
.profile-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.profile-list li {
  padding: 15px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  padding-left: 30px;
  transition: all 0.3s ease;
  color: var(--text-light);
  line-height: 1.6;
}

.profile-list li::before {
  content: "\f0a4";
  font-family: "Font Awesome 5 Free";
  font-weight: 400;
  position: absolute;
  left: 0;
  color: var(--primary);
  transition: transform 0.3s ease;
}

.profile-list li:hover {
  padding-left: 35px;
  color: var(--text-dark);
}

.profile-list li:hover::before {
  transform: translateX(5px);
}

.profile-list li:last-child {
  border-bottom: none;
}

/* Modern Enhanced Skills Section */
.skill-meters {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 30px;
  margin-top: 30px;
}

.skill-meter {
  position: relative;
  background: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  padding: 25px 20px;
  transform: translateY(30px);
  opacity: 0;
  animation: skillFadeIn 0.6s forwards;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  overflow: hidden;
  border: 1px solid rgba(67, 97, 238, 0.05);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.skill-meter::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, var(--primary), var(--primary-light));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.skill-meter:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(67, 97, 238, 0.15);
}

.skill-meter:hover::before {
  transform: scaleX(1);
}

.skill-meter:nth-child(2) {
  animation-delay: 0.15s;
}
.skill-meter:nth-child(3) {
  animation-delay: 0.3s;
}
.skill-meter:nth-child(4) {
  animation-delay: 0.45s;
}
.skill-meter:nth-child(5) {
  animation-delay: 0.6s;
}
.skill-meter:nth-child(6) {
  animation-delay: 0.75s;
}

@keyframes skillFadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Skill Header with Icon */
.skill-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.skill-icon-wrapper {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  background: linear-gradient(
    135deg,
    rgba(67, 97, 238, 0.1),
    rgba(76, 201, 240, 0.1)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
  transition: all 0.4s ease;
}

.skill-icon-wrapper::after {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  top: 100%;
  left: 0;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  z-index: 0;
}

.skill-meter:hover .skill-icon-wrapper::after {
  top: 0;
}

.skill-icon {
  font-size: 2rem;
  color: var(--primary);
  position: relative;
  z-index: 1;
  transition: all 0.4s ease;
}

.skill-meter:hover .skill-icon {
  color: white;
  transform: scale(1.1) rotate(5deg);
}

.skill-info {
  flex: 1;
}

.skill-label {
  font-weight: 600;
  color: var(--text-dark);
  font-size: 1.1rem;
  margin-bottom: 5px;
  transition: color 0.3s ease;
  display: block;
}

.skill-meter:hover .skill-label {
  color: var(--primary);
}

.skill-level {
  font-size: 0.85rem;
  color: var(--text-light);
  display: flex;
  align-items: center;
}

.skill-level-text {
  margin-left: 5px;
  opacity: 0.8;
}

/* Skill Progress Bar */
.skill-progress-container {
  margin-top: auto;
  padding-top: 20px;
}

.skill-progress-bg {
  height: 8px;
  background-color: rgba(67, 97, 238, 0.1);
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.skill-progress-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 0%; /* Will be set by JS */
  background: linear-gradient(90deg, var(--primary), var(--primary-light));
  border-radius: 10px;
  transition: width 1.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 0 10px rgba(67, 97, 238, 0.3);
}

.skill-progress-glow {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 15px;
  background: white;
  filter: blur(5px);
  opacity: 0;
  border-radius: 10px;
  transform: translateX(5px);
  transition: opacity 0.3s ease;
}

.skill-meter:hover .skill-progress-glow {
  opacity: 0.7;
  animation: progressGlow 1.5s infinite;
}

@keyframes progressGlow {
  0% {
    transform: translateX(5px);
  }
  100% {
    transform: translateX(-100px);
  }
}

.skill-percentage {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
  font-size: 0.9rem;
  color: var(--text-light);
}

.skill-percentage-value {
  font-weight: 700;
  color: var(--primary);
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.skill-meter:hover .skill-percentage-value {
  transform: scale(1.1);
}

/* Skill Tooltip */
.skill-tooltip {
  position: absolute;
  top: -45px;
  left: 50%;
  transform: translateX(-50%) scale(0.7);
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.1);
  padding: 7px 15px;
  border-radius: 20px;
  font-size: 0.85rem;
  color: var(--text-dark);
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  white-space: nowrap;
  z-index: 10;
  border: 1px solid rgba(67, 97, 238, 0.1);
}

.skill-tooltip::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
  width: 10px;
  height: 10px;
  background: rgba(255, 255, 255, 0.95);
  border-right: 1px solid rgba(67, 97, 238, 0.1);
  border-bottom: 1px solid rgba(67, 97, 238, 0.1);
}

.skill-meter:hover .skill-tooltip {
  opacity: 1;
  top: -55px;
  transform: translateX(-50%) scale(1);
}

/* Skill Badge */
.skill-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(67, 97, 238, 0.1);
  color: var(--primary);
  font-size: 0.7rem;
  font-weight: 600;
  padding: 3px 8px;
  border-radius: 20px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.skill-meter:hover .skill-badge {
  background: var(--primary);
  color: white;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
}

/* Skill Particles */
.skill-particles {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100px;
  height: 100px;
  overflow: hidden;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.skill-meter:hover .skill-particles {
  opacity: 1;
}

.skill-particle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: var(--primary);
  border-radius: 50%;
  bottom: -20px;
  right: 20px;
  opacity: 0;
  animation: particleRise 1.5s ease-out infinite;
}

.skill-particle:nth-child(2) {
  width: 6px;
  height: 6px;
  right: 40px;
  animation-delay: 0.3s;
}

.skill-particle:nth-child(3) {
  width: 4px;
  height: 4px;
  right: 60px;
  animation-delay: 0.6s;
}

@keyframes particleRise {
  0% {
    transform: translateY(0) scale(1);
    opacity: 0;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    transform: translateY(-80px) scale(0);
    opacity: 0;
  }
}

/* Dark mode adjustments */
body.dark-mode .skill-meter {
  background: rgba(30, 30, 40, 0.8);
  border-color: rgba(255, 255, 255, 0.05);
}

body.dark-mode .skill-icon-wrapper {
  background: rgba(255, 255, 255, 0.1);
}

body.dark-mode .skill-progress-bg {
  background-color: rgba(255, 255, 255, 0.1);
}

body.dark-mode .skill-badge {
  background: rgba(255, 255, 255, 0.1);
}

body.dark-mode .skill-tooltip {
  background: rgba(30, 30, 40, 0.95);
  color: var(--text-white);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

body.dark-mode .skill-tooltip::after {
  background: rgba(30, 30, 40, 0.95);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive styling */
@media (max-width: 768px) {
  .skill-meters {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 20px;
  }

  .skill-icon-wrapper {
    width: 50px;
    height: 50px;
  }

  .skill-icon {
    font-size: 1.8rem;
  }
}

@media (max-width: 576px) {
  .skill-meters {
    grid-template-columns: repeat(1, 1fr);
    gap: 15px;
  }

  .skill-icon-wrapper {
    width: 45px;
    height: 45px;
  }

  .skill-icon {
    font-size: 1.6rem;
  }

  .skill-label {
    font-size: 0.9rem;
  }

  .skill-percentage-value {
    font-size: 1rem;
  }
}

/* Project contributions with card design */
.project-contribution {
  background-color: rgba(67, 97, 238, 0.03);
  padding: 25px;
  border-radius: 18px;
  margin-bottom: 25px;
  transition: all 0.3s ease;
  border-left: 4px solid var(--primary);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.03);
}

.project-contribution:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(67, 97, 238, 0.08);
  background-color: rgba(67, 97, 238, 0.05);
}

.project-contribution h3 {
  color: var(--primary);
  margin-bottom: 12px;
  font-size: 1.25rem;
  position: relative;
  padding-bottom: 10px;
}

.project-contribution h3::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 30px;
  height: 2px;
  background: linear-gradient(to right, var(--primary), var(--primary-light));
}

.project-contribution p {
  color: var(--text-light);
  line-height: 1.7;
}

/* Enhanced Tasks & Responsibilities Section */
.tasks-container {
  position: relative;
}

/* Task filters and controls */
.tasks-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  flex-wrap: wrap;
  gap: 15px;
}

.tasks-filter {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 8px 16px;
  border-radius: 30px;
  font-size: 0.85rem;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.8);
  color: var(--text-light);
  border: 1px solid rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
  display: flex;
  align-items: center;
  gap: 6px;
}

.filter-btn i {
  font-size: 0.8rem;
  opacity: 0.7;
}

.filter-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.1);
  color: var(--primary);
}

.filter-btn.active {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.2);
}

.filter-btn.active i {
  opacity: 1;
}

.tasks-view-toggle {
  display: flex;
  gap: 5px;
  background: rgba(255, 255, 255, 0.8);
  padding: 5px;
  border-radius: 30px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.view-btn {
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text-light);
}

.view-btn:hover {
  color: var(--primary);
}

.view-btn.active {
  background: var(--primary);
  color: white;
}

/* Task timeline view */
.tasks-timeline {
  position: relative;
  margin-left: 30px;
  padding-left: 30px;
  border-left: 2px dashed rgba(67, 97, 238, 0.2);
  margin-bottom: 40px;
}

.timeline-marker {
  position: absolute;
  left: -10px;
  top: 0;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: white;
  border: 2px solid var(--primary);
  z-index: 2;
  transition: all 0.3s ease;
}

.timeline-marker::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--primary);
  transition: all 0.3s ease;
}

/* Task cards with enhanced modern design */
.task-item {
  background-color: white;
  padding: 25px;
  border-radius: 18px;
  margin-bottom: 30px;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  transform: translateY(30px);
  opacity: 0;
  animation: taskFadeIn 0.6s forwards;
  border: 1px solid rgba(255, 255, 255, 0.8);
}

@keyframes taskFadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.task-item:nth-child(2) {
  animation-delay: 0.15s;
}

.task-item:nth-child(3) {
  animation-delay: 0.3s;
}

.task-item:nth-child(4) {
  animation-delay: 0.45s;
}

.task-item:nth-child(5) {
  animation-delay: 0.6s;
}

.task-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(to right, var(--primary), var(--primary-light));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s ease;
}

.task-item:hover {
  box-shadow: 0 15px 35px rgba(67, 97, 238, 0.15);
  transform: translateY(-8px) scale(1.02);
}

.task-item:hover::before {
  transform: scaleX(1);
}

.task-item.completed {
  border-left: 4px solid #34c759;
}

.task-item.in-progress {
  border-left: 4px solid #ff9500;
}

.task-item.pending {
  border-left: 4px solid #8e8e93;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  position: relative;
}

.task-title-wrapper {
  flex: 1;
}

.task-title {
  font-weight: 600;
  color: var(--text-dark);
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
  position: relative;
  transition: all 0.3s ease;
}

.task-title i {
  color: var(--primary);
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.task-item:hover .task-title {
  color: var(--primary);
}

.task-item:hover .task-title i {
  transform: scale(1.2) rotate(5deg);
}

.task-status {
  padding: 6px 14px;
  border-radius: 30px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
}

.task-status i {
  font-size: 0.8rem;
}

.task-status.completed {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34c759;
  border: 1px solid rgba(52, 199, 89, 0.2);
}

.task-status.in-progress {
  background-color: rgba(255, 149, 0, 0.1);
  color: #ff9500;
  border: 1px solid rgba(255, 149, 0, 0.2);
}

.task-status.pending {
  background-color: rgba(142, 142, 147, 0.1);
  color: #8e8e93;
  border: 1px solid rgba(142, 142, 147, 0.2);
}

.task-item:hover .task-status {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.task-subtitle {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-subtitle i {
  color: var(--primary);
  opacity: 0.7;
  font-size: 0.85rem;
}

.task-content {
  position: relative;
}

.task-details {
  color: var(--text-light);
  line-height: 1.7;
  margin-bottom: 25px;
  font-size: 1rem;
  position: relative;
  transition: all 0.3s ease;
  max-height: 100px;
  overflow: hidden;
}

.task-details.expanded {
  max-height: 1000px;
}

.task-details-toggle {
  position: absolute;
  bottom: 0;
  right: 0;
  background: linear-gradient(to left, white 50%, transparent);
  padding: 5px 10px;
  font-size: 0.85rem;
  color: var(--primary);
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.3s ease;
}

.task-details-toggle:hover {
  color: var(--primary-dark);
}

.task-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  font-size: 0.85rem;
  color: var(--text-light);
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px dashed rgba(0, 0, 0, 0.05);
}

.task-meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(67, 97, 238, 0.05);
  padding: 8px 15px;
  border-radius: 30px;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.02);
}

.task-meta-item:hover {
  background: rgba(67, 97, 238, 0.1);
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 8px 20px rgba(67, 97, 238, 0.1);
}

.task-meta-item i {
  color: var(--primary);
  font-size: 0.9rem;
}

/* Task progress indicator */
.task-progress {
  margin: 20px 0;
  position: relative;
}

.progress-bar-container {
  height: 8px;
  background: rgba(67, 97, 238, 0.1);
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(to right, var(--primary), var(--primary-light));
  border-radius: 10px;
  width: 0;
  transition: width 1.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  overflow: hidden;
}

.progress-bar-fill::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 15px;
  background: rgba(255, 255, 255, 0.3);
  filter: blur(5px);
  transform: translateX(5px);
}

.progress-percentage {
  position: absolute;
  top: -25px;
  right: 0;
  background: var(--primary);
  color: white;
  padding: 3px 10px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.4s ease;
}

.task-item:hover .progress-percentage {
  opacity: 1;
  transform: translateY(0);
}

/* Task actions */
.task-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
}

.task-item:hover .task-actions {
  opacity: 1;
  transform: translateY(0);
}

.task-action-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  color: var(--primary);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(67, 97, 238, 0.1);
}

.task-action-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--primary);
  border-radius: 50%;
  transform: scale(0);
  transition: transform 0.3s ease;
  z-index: 0;
}

.task-action-btn i {
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.task-action-btn:hover {
  transform: translateY(-5px) rotate(5deg);
  box-shadow: 0 8px 20px rgba(67, 97, 238, 0.15);
}

.task-action-btn:hover::before {
  transform: scale(1);
}

.task-action-btn:hover i {
  color: white;
  transform: scale(1.2);
}

/* Action button click effect */
.task-action-btn.action-clicked {
  transform: scale(0.9);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.task-action-btn.action-clicked i {
  color: white;
}

.task-action-tooltip {
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%) scale(0.7);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.7rem;
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s ease;
  white-space: nowrap;
  z-index: 20;
}

.task-action-btn:hover .task-action-tooltip {
  opacity: 1;
  transform: translateX(-50%) scale(1);
  bottom: -25px;
}

/* Task card decorative elements */
.task-decoration {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(
    135deg,
    rgba(67, 97, 238, 0.05),
    rgba(76, 201, 240, 0.05)
  );
  opacity: 0.5;
  z-index: 0;
  transition: all 0.5s ease;
}

.task-item:hover .task-decoration {
  transform: scale(1.5) rotate(45deg);
  opacity: 0.8;
}

/* Empty state styling */
.no-tasks {
  padding: 50px;
  text-align: center;
  color: var(--text-light);
  border: 2px dashed rgba(67, 97, 238, 0.1);
  border-radius: 18px;
  margin: 30px 0;
  background: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
}

.no-tasks i {
  font-size: 3rem;
  color: rgba(67, 97, 238, 0.2);
  margin-bottom: 20px;
  display: block;
}

.no-tasks h3 {
  font-size: 1.5rem;
  margin-bottom: 15px;
  color: var(--text-dark);
}

.no-tasks p {
  max-width: 500px;
  margin: 0 auto;
  line-height: 1.7;
}

/* Dark mode adjustments */
body.dark-mode .task-item {
  background: rgba(30, 30, 40, 0.8);
  border-color: rgba(255, 255, 255, 0.05);
}

body.dark-mode .task-details-toggle {
  background: linear-gradient(to left, rgba(30, 30, 40, 0.8) 50%, transparent);
}

body.dark-mode .no-tasks {
  background: rgba(30, 30, 40, 0.3);
  border-color: rgba(255, 255, 255, 0.05);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .tasks-controls {
    flex-direction: column;
    align-items: flex-start;
  }

  .task-header {
    flex-direction: column;
    gap: 10px;
  }

  .task-status {
    align-self: flex-start;
  }

  .task-meta {
    gap: 10px;
  }

  .task-meta-item {
    padding: 6px 12px;
    font-size: 0.8rem;
  }

  .tasks-timeline {
    margin-left: 15px;
    padding-left: 20px;
  }
}

/* Contact section redesign */
.contact-details {
  margin-bottom: 40px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 18px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 18px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.contact-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(67, 97, 238, 0.1);
  background: white;
}

.contact-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(
    135deg,
    rgba(67, 97, 238, 0.1),
    rgba(76, 201, 240, 0.1)
  );
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary);
  font-size: 1.3rem;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.1);
}

.contact-item:hover .contact-icon {
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  color: white;
  transform: scale(1.1) rotate(10deg);
}

.contact-info {
  color: var(--text-light);
  font-size: 1.05rem;
  flex: 1;
}

/* Social links section */
.profile-social {
  margin-top: 30px;
}

.profile-social h3 {
  font-size: 1.3rem;
  margin-bottom: 20px;
  color: var(--text-dark);
  position: relative;
  display: inline-block;
  padding-bottom: 10px;
}

.profile-social h3::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(to right, var(--primary), var(--primary-light));
  border-radius: 3px;
}

.social-links {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.social-link {
  width: 55px;
  height: 55px;
  border-radius: 15px;
  background: linear-gradient(
    135deg,
    rgba(67, 97, 238, 0.1),
    rgba(76, 201, 240, 0.1)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary);
  font-size: 1.4rem;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.social-link::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  top: 100%;
  left: 0;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  z-index: 0;
}

.social-link i {
  position: relative;
  z-index: 1;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.social-link:hover::before {
  top: 0;
}

.social-link:hover {
  color: white;
  transform: translateY(-8px) rotate(5deg);
  box-shadow: 0 12px 30px rgba(67, 97, 238, 0.2);
}

/* Responsive design improvements */
@media (max-width: 992px) {
  .profile-hero {
    flex-direction: column;
    gap: 30px;
    text-align: center;
    padding: 40px 30px;
  }

  .profile-avatar {
    width: 140px;
    height: 140px;
    font-size: 55px;
  }

  .profile-basic-info h1 {
    font-size: 2.2rem;
  }

  .profile-tabs {
    overflow-x: auto;
    white-space: nowrap;
    padding-bottom: 5px;
    justify-content: flex-start;
  }

  .contact-details {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .member-profile-section {
    padding: 40px 0;
  }

  .profile-tab-content {
    padding: 30px 25px;
  }

  .profile-section h2 {
    font-size: 1.4rem;
  }

  .profile-section h2::after {
    width: 40px;
  }
}

@media (max-width: 576px) {
  .profile-tab {
    padding: 18px 20px;
    font-size: 0.9rem;
  }

  .profile-tab-content {
    padding: 25px 20px;
  }

  .profile-section h2 {
    font-size: 1.3rem;
    margin-bottom: 20px;
  }

  .profile-basic-info h1 {
    font-size: 1.8rem;
  }

  .profile-avatar {
    width: 120px;
    height: 120px;
    font-size: 45px;
  }

  .profile-basic-info .member-role {
    font-size: 1rem;
    padding: 8px 20px;
  }

  .task-item {
    padding: 20px;
  }

  .contact-item {
    padding: 15px;
  }
}
