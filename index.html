<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>GradProject - Project Management</title>
    <!-- Import only the main CSS file which imports all others -->
    <link rel="stylesheet" href="css/main.css" />
    <link rel="stylesheet" href="css/components/enhanced-stats.css" />
    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css"
    />
    <link rel="stylesheet" href="css/components/progress.css" />
    <link rel="stylesheet" href="css/components/progress-enhanced.css" />
    <!-- Add our new enhanced sprint CSS -->
    <link rel="stylesheet" href="css/components/sprint-enhanced.css" />
    <link rel="stylesheet" href="css/components/sprint.css" />
    <link rel="stylesheet" href="css/components/member-profile.css" />
  </head>
  <body>
    <header>
      <div class="container header-content">
        <div class="header-left">
          <div class="logo">GradProject</div>
        </div>

        <div class="header-center">
          <nav>
            <ul>
              <li>
                <a href="#overview"
                  ><i class="fas fa-info-circle"></i> Overview</a
                >
              </li>
              <li>
                <a href="#team"><i class="fas fa-users"></i> Team</a>
              </li>
              <li>
                <a href="#progress"><i class="fas fa-tasks"></i> Progress</a>
              </li>
              <li>
                <a href="#documentation"
                  ><i class="fas fa-file-alt"></i> Documentation</a
                >
              </li>
            </ul>
          </nav>
        </div>

        <div class="header-right">
          <div class="theme-toggle" id="themeToggle">
            <i class="fas fa-moon"></i>
            <i class="fas fa-sun"></i>
            <div class="toggle-ball"></div>
          </div>
        </div>
      </div>
    </header>

    <div class="hero" id="hero">
      <div class="hero-background">
        <div class="hero-particles" id="particles-js"></div>
        <div class="hero-shape hero-shape-1"></div>
        <div class="hero-shape hero-shape-2"></div>
        <div class="hero-shape hero-shape-3"></div>
        <div class="hero-shape hero-shape-4"></div>
        <div class="hero-shape hero-shape-5"></div>
        <div class="hero-glow"></div>
        <div class="hero-overlay"></div>
      </div>

      <div class="container hero-content">
        <div class="hero-text-container">
          <div class="badge-container animate-in">
            <span class="hero-badge">Graduation Project 2025</span>
          </div>
          <h1 class="animate-in animate-delay-1 hero-title">
            <span class="gradient-text">CropGuard</span>
            <span class="hero-title-separator">:</span>
            <span class="hero-title-main">Real-Time AI Farming System</span>
          </h1>
          <p class="animate-in animate-delay-2 hero-description">
            Welcome to the official repository of CropGuard, our innovative
            graduation project aimed at transforming modern agriculture through
            AI-powered analytics, real-time monitoring, and embedded systems.
          </p>
          <div class="cta-buttons animate-in animate-delay-3">
            <a href="#overview" class="btn btn-primary btn-glow">
              <span class="btn-text">Explore Project</span>
              <span class="btn-icon"><i class="fas fa-arrow-right"></i></span>
              <span class="btn-hover-effect"></span>
            </a>
            <a
              href="#documentation"
              class="btn btn-outline btn-documentation-hero"
            >
              <span class="btn-text">View Documentation</span>
              <span class="btn-icon"><i class="fas fa-file-alt"></i></span>
              <span class="btn-hover-effect"></span>
            </a>
          </div>
        </div>

        <div class="hero-stats-container animate-in animate-delay-4">
          <div
            class="stat-card"
            data-tilt
            data-tilt-glare
            data-tilt-max-glare="0.3"
          >
            <div class="stat-icon">
              <i class="fas fa-users-cog"></i>
              <div class="icon-particles"></div>
            </div>
            <div class="stat-content">
              <div class="stat-number" data-counter="4">0</div>
              <div class="stat-label">Specialized Teams</div>
            </div>
            <div class="stat-decoration"></div>
            <div class="stat-glow"></div>
          </div>

          <div
            class="stat-card"
            data-tilt
            data-tilt-glare
            data-tilt-max-glare="0.3"
          >
            <div class="stat-icon">
              <i class="fas fa-user-friends"></i>
              <div class="icon-particles"></div>
            </div>
            <div class="stat-content">
              <div class="stat-number" data-counter="15">0</div>
              <div class="stat-label">Team Members</div>
            </div>
            <div class="stat-decoration"></div>
            <div class="stat-glow"></div>
          </div>

          <div
            class="stat-card"
            data-tilt
            data-tilt-glare
            data-tilt-max-glare="0.3"
          >
            <div class="stat-icon">
              <i class="fas fa-code-branch"></i>
              <div class="icon-particles"></div>
            </div>
            <div class="stat-content">
              <div class="stat-number" data-counter="20">0</div>
              <div class="stat-label">Development Sprints</div>
            </div>
            <div class="stat-decoration"></div>
            <div class="stat-glow"></div>
          </div>

          <div
            class="stat-card"
            data-tilt
            data-tilt-glare
            data-tilt-max-glare="0.3"
          >
            <div class="stat-icon">
              <i class="fas fa-list-check"></i>
              <div class="icon-particles"></div>
            </div>
            <div class="stat-content">
              <div class="stat-number" data-counter="85">0</div>
              <div class="stat-label">Completed Tasks</div>
            </div>
            <div class="stat-decoration"></div>
            <div class="stat-glow"></div>
          </div>
        </div>
      </div>

      <div class="hero-scroll-indicator">
        <div class="scroll-arrow">
          <i class="fas fa-chevron-down"></i>
          <span class="scroll-ripple"></span>
        </div>
      </div>
    </div>

    <!-- Table of Contents with improved click handling -->
    <div
      class="toc-container"
      id="tableOfContents"
      role="navigation"
      aria-label="Table of contents"
    >
      <div class="toc-header">
        <h3>Contents</h3>
        <button class="toc-toggle" id="tocToggle" aria-label="Toggle contents">
          <i class="fas fa-chevron-down"></i>
        </button>
      </div>
      <ul class="toc-list" id="tocList">
        <!-- This will be populated by JS -->
      </ul>
    </div>

    <div class="container">
      <section id="overview" class="project-overview">
        <div class="overview-header">
          <h2 class="section-title">Project Overview</h2>
          <div class="overview-badge">Graduation Project 2025</div>
        </div>

        <div class="overview-container">
          <div class="overview-content">
            <p class="overview-intro">
              <span class="highlight-text">CropGuard</span> is a smart farming
              solution that integrates embedded systems, AI, and IoT to enhance
              precision agriculture. Our system uses a mobile-controlled rover
              equipped with sensors and cameras to monitor crop health, detect
              diseases, and optimize resource management.
            </p>
            <p class="overview-detail">
              With AI-powered analytics, real-time data processing, and an
              intuitive mobile/web platform, CropGuard empowers farmers with
              actionable insights for sustainable and efficient farming.
            </p>
            <div class="overview-cta">
              <a href="#documentation" class="btn btn-outline overview-btn">
                <span class="btn-text">Technical Details</span>
                <span class="btn-icon"><i class="fas fa-arrow-right"></i></span>
              </a>
            </div>
          </div>

          <div class="overview-metrics">
            <div class="metric-item">
              <div class="metric-icon"><i class="fas fa-seedling"></i></div>
              <div class="metric-value">85%</div>
              <div class="metric-label">Crop Health Detection</div>
            </div>
            <div class="metric-item">
              <div class="metric-icon"><i class="fas fa-tint"></i></div>
              <div class="metric-value">40%</div>
              <div class="metric-label">Water Conservation</div>
            </div>
            <div class="metric-item">
              <div class="metric-icon"><i class="fas fa-leaf"></i></div>
              <div class="metric-value">95%</div>
              <div class="metric-label">Disease Detection</div>
            </div>
          </div>
        </div>

        <div class="overview-divider">
          <span class="divider-text">Key Features</span>
        </div>

        <div class="features-grid">
          <div class="feature-card" data-tilt data-tilt-scale="1.05">
            <div class="feature-icon">
              <i class="fas fa-tachometer-alt"></i>
            </div>
            <h3>Real-time Crop Monitoring</h3>
            <p>Collect and analyze live data from crops and soil.</p>
            <div class="feature-details">
              <ul class="feature-list">
                <li>
                  <i class="fas fa-check-circle"></i> Soil moisture sensors
                </li>
                <li>
                  <i class="fas fa-check-circle"></i> Temperature monitoring
                </li>
                <li>
                  <i class="fas fa-check-circle"></i> Nutrient composition
                </li>
              </ul>
            </div>
          </div>
          <div class="feature-card" data-tilt data-tilt-scale="1.05">
            <div class="feature-icon"><i class="fas fa-chart-line"></i></div>
            <h3>AI-powered Disease Detection</h3>
            <p>
              Detect and diagnose plant diseases using deep learning models.
            </p>
            <div class="feature-details">
              <ul class="feature-list">
                <li>
                  <i class="fas fa-check-circle"></i> Computer vision algorithms
                </li>
                <li>
                  <i class="fas fa-check-circle"></i> Early detection
                  capabilities
                </li>
                <li>
                  <i class="fas fa-check-circle"></i> Treatment recommendations
                </li>
              </ul>
            </div>
          </div>
          <div class="feature-card" data-tilt data-tilt-scale="1.05">
            <div class="feature-icon"><i class="fas fa-mobile-alt"></i></div>
            <h3>Mobile Control</h3>
            <p>Manage farm operations from anywhere.</p>
            <div class="feature-details">
              <ul class="feature-list">
                <li>
                  <i class="fas fa-check-circle"></i> Remote monitoring
                  dashboard
                </li>
                <li><i class="fas fa-check-circle"></i> Push notifications</li>
                <li>
                  <i class="fas fa-check-circle"></i> Automated scheduling
                </li>
              </ul>
            </div>
          </div>
          <div class="feature-card" data-tilt data-tilt-scale="1.05">
            <div class="feature-icon"><i class="fas fa-shield-alt"></i></div>
            <h3>Online Marketplace</h3>
            <p>Buy, sell, and lease farming equipment.</p>
            <div class="feature-details">
              <ul class="feature-list">
                <li><i class="fas fa-check-circle"></i> Equipment listings</li>
                <li><i class="fas fa-check-circle"></i> Secure transactions</li>
                <li>
                  <i class="fas fa-check-circle"></i> Equipment recommendations
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div class="overview-timeline">
          <div class="timeline-label">Project Phase</div>
          <div class="timeline-progress">
            <div class="timeline-track">
              <div class="timeline-fill"></div>
            </div>
            <div class="timeline-milestones">
              <div class="milestone active" data-phase="Research">Research</div>
              <div class="milestone active" data-phase="Design">Design</div>
              <div class="milestone active" data-phase="Development">
                Development
              </div>
              <div class="milestone" data-phase="Testing">Testing</div>
              <div class="milestone" data-phase="Deployment">Deployment</div>
            </div>
          </div>
        </div>

        <p class="overview-conclusion">
          This project showcases our expertise in AI, embedded systems, web, and
          mobile development, while demonstrating a collaborative approach to
          solving real-world agricultural challenges.
        </p>
      </section>

      <section id="team" class="teams-container">
        <h2 class="section-title">Our Teams</h2>

        <div class="team-tabs">
          <div class="team-tab active" data-team="all">
            <i class="fas fa-users"></i> All Members
          </div>
          <div class="team-tab" data-team="supervisors">
            <i class="fas fa-user-tie"></i> Supervisors
          </div>
          <div class="team-tab" data-team="web">
            <i class="fas fa-globe"></i> Web Development
          </div>
          <div class="team-tab" data-team="mobile">
            <i class="fas fa-mobile-alt"></i> Mobile Development
          </div>
          <div class="team-tab" data-team="ai">
            <i class="fas fa-brain"></i> AI
          </div>
          <div class="team-tab" data-team="embedded">
            <i class="fas fa-microchip"></i> Embedded Systems
          </div>
        </div>

        <!-- Supervisors Team -->
        <div class="team-content supervisors-team" id="supervisors-team">
          <div class="team-intro">
            <h3>Supervisors</h3>
            <p>
              Our project supervisors provide guidance, expertise, and
              mentorship to ensure the success of our project. They offer
              valuable insights and direction throughout the development
              process.
            </p>
          </div>

          <div class="team-members">
            <!-- Supervisor 1 -->
            <div class="member-card">
              <div class="member-img-container">
                <div class="member-img"><i class="fas fa-user"></i></div>
                <div class="view-profile">
                  <i class="fas fa-id-card"></i> View Profile
                </div>
              </div>
              <div class="member-info">
                <h3 class="member-name">Dr. Supervisor</h3>
                <div class="member-role">Project Supervisor</div>
                <div class="member-social">
                  <a href="#" class="social-icon"
                    ><i class="fab fa-github"></i
                  ></a>
                  <a href="#" class="social-icon"
                    ><i class="fab fa-linkedin"></i
                  ></a>
                  <a href="#" class="social-icon"
                    ><i class="fas fa-envelope"></i
                  ></a>
                </div>
              </div>
            </div>
          </div>

          <div class="team-tech">
            <h3 class="tech-title">
              <i class="fas fa-code"></i> Areas of Expertise
            </h3>
            <div class="tech-list">
              <div class="tech-item">
                <i class="fas fa-project-diagram"></i> Project Management
              </div>
              <div class="tech-item">
                <i class="fas fa-brain"></i> Artificial Intelligence
              </div>
              <div class="tech-item">
                <i class="fas fa-microchip"></i> Embedded Systems
              </div>
              <div class="tech-item">
                <i class="fas fa-database"></i> Data Science
              </div>
              <div class="tech-item">
                <i class="fas fa-code"></i> Software Engineering
              </div>
              <div class="tech-item">
                <i class="fas fa-graduation-cap"></i> Academic Research
              </div>
            </div>
          </div>
        </div>

        <!-- Web Development Team -->
        <div class="team-content web-team" id="web-team">
          <div class="team-intro">
            <h3>Web Development Team</h3>
            <p>
              Our Web Development team is responsible for creating the front-end
              and back-end systems that power our platform's user interface and
              data management. They're building responsive, accessible web
              applications that provide intuitive access to our smart city
              services.
            </p>
          </div>

          <div class="team-members">
            <!-- Web Team Member 1 -->
            <div class="member-card">
              <div class="member-img-container">
                <div class="member-img"><i class="fas fa-user"></i></div>
                <div class="view-profile">
                  <i class="fas fa-id-card"></i> View Profile
                </div>
              </div>
              <div class="member-info">
                <h3 class="member-name">John Doe</h3>
                <div class="member-role">Frontend Lead</div>
                <div class="member-social">
                  <a href="#" class="social-icon"
                    ><i class="fab fa-github"></i
                  ></a>
                  <a href="#" class="social-icon"
                    ><i class="fab fa-linkedin"></i
                  ></a>
                  <a href="#" class="social-icon"
                    ><i class="fas fa-envelope"></i
                  ></a>
                </div>
              </div>
            </div>

            <!-- Web Team Member 2 -->
            <div class="member-card">
              <div class="member-img-container">
                <div class="member-img"><i class="fas fa-user"></i></div>
                <div class="view-profile">
                  <i class="fas fa-id-card"></i> View Profile
                </div>
              </div>
              <div class="member-info">
                <h3 class="member-name">Emily Chen</h3>
                <div class="member-role">Backend Developer</div>
                <div class="member-social">
                  <a href="#" class="social-icon"
                    ><i class="fab fa-github"></i
                  ></a>
                  <a href="#" class="social-icon"
                    ><i class="fab fa-linkedin"></i
                  ></a>
                  <a href="#" class="social-icon"
                    ><i class="fas fa-envelope"></i
                  ></a>
                </div>
              </div>
            </div>

            <!-- Web Team Member 3 -->
            <div class="member-card">
              <div class="member-img-container">
                <div class="member-img"><i class="fas fa-user"></i></div>
                <div class="view-profile">
                  <i class="fas fa-id-card"></i> View Profile
                </div>
              </div>
              <div class="member-info">
                <h3 class="member-name">David Kim</h3>
                <div class="member-role">UI/UX Designer</div>
                <div class="member-social">
                  <a href="#" class="social-icon"
                    ><i class="fab fa-github"></i
                  ></a>
                  <a href="#" class="social-icon"
                    ><i class="fab fa-dribbble"></i
                  ></a>
                  <a href="#" class="social-icon"
                    ><i class="fas fa-envelope"></i
                  ></a>
                </div>
              </div>
            </div>

            <!-- Web Team Member 4 -->
            <div class="member-card">
              <div class="member-img-container">
                <div class="member-img"><i class="fas fa-user"></i></div>
                <div class="view-profile">
                  <i class="fas fa-id-card"></i> View Profile
                </div>
              </div>
              <div class="member-info">
                <h3 class="member-name">Maria Rodriguez</h3>
                <div class="member-role">Full-Stack Developer</div>
                <div class="member-social">
                  <a href="#" class="social-icon"
                    ><i class="fab fa-github"></i
                  ></a>
                  <a href="#" class="social-icon"
                    ><i class="fab fa-linkedin"></i
                  ></a>
                  <a href="#" class="social-icon"
                    ><i class="fas fa-envelope"></i
                  ></a>
                </div>
              </div>
            </div>
          </div>

          <div class="team-tech">
            <h3 class="tech-title">
              <i class="fas fa-code"></i> Technologies Used
            </h3>
            <div class="tech-list">
              <div class="tech-item"><i class="fab fa-react"></i> React</div>
              <div class="tech-item">
                <i class="fab fa-node-js"></i> Node.js
              </div>
              <div class="tech-item">
                <i class="fas fa-database"></i> MongoDB
              </div>
              <div class="tech-item"><i class="fab fa-aws"></i> AWS</div>
              <div class="tech-item"><i class="fab fa-js"></i> JavaScript</div>
              <div class="tech-item"><i class="fab fa-sass"></i> SASS</div>
              <div class="tech-item">
                <i class="fas fa-exchange-alt"></i> GraphQL
              </div>
              <div class="tech-item"><i class="fas fa-server"></i> Express</div>
            </div>
          </div>
        </div>

        <!-- Mobile Development Team -->
        <div class="team-content mobile-team" id="mobile-team">
          <div class="team-intro">
            <h3>Mobile Development Team</h3>
            <p>
              The Mobile Development team is building native and cross-platform
              applications that allow users to interact with our smart city
              platform on the go. Their work enables remote monitoring and
              control of connected devices, real-time notifications, and
              location-based services.
            </p>
          </div>

          <div class="team-members">
            <!-- Mobile Team Member 1 -->
            <div class="member-card">
              <div class="member-img-container">
                <div class="member-img"><i class="fas fa-user"></i></div>
                <div class="view-profile">
                  <i class="fas fa-id-card"></i> View Profile
                </div>
              </div>
              <div class="member-info">
                <h3 class="member-name">Sarah Williams</h3>
                <div class="member-role">Mobile Lead</div>
                <div class="member-social">
                  <a href="#" class="social-icon"
                    ><i class="fab fa-github"></i
                  ></a>
                  <a href="#" class="social-icon"
                    ><i class="fab fa-linkedin"></i
                  ></a>
                  <a href="#" class="social-icon"
                    ><i class="fas fa-envelope"></i
                  ></a>
                </div>
              </div>
            </div>

            <!-- Mobile Team Member 2 -->
            <div class="member-card">
              <div class="member-img-container">
                <div class="member-img"><i class="fas fa-user"></i></div>
                <div class="view-profile">
                  <i class="fas fa-id-card"></i> View Profile
                </div>
              </div>
              <div class="member-info">
                <h3 class="member-name">Alex Johnson</h3>
                <div class="member-role">iOS Developer</div>
                <div class="member-social">
                  <a href="#" class="social-icon"
                    ><i class="fab fa-github"></i
                  ></a>
                  <a href="#" class="social-icon"
                    ><i class="fab fa-linkedin"></i
                  ></a>
                  <a href="#" class="social-icon"
                    ><i class="fas fa-envelope"></i
                  ></a>
                </div>
              </div>
            </div>

            <!-- Mobile Team Member 3 -->
            <div class="member-card">
              <div class="member-img-container">
                <div class="member-img"><i class="fas fa-user"></i></div>
                <div class="view-profile">
                  <i class="fas fa-id-card"></i> View Profile
                </div>
              </div>
              <div class="member-info">
                <h3 class="member-name">Michael Smith</h3>
                <div class="member-role">Android Developer</div>
                <div class="member-social">
                  <a href="#" class="social-icon"
                    ><i class="fab fa-github"></i
                  ></a>
                  <a href="#" class="social-icon"
                    ><i class="fab fa-linkedin"></i
                  ></a>
                  <a href="#" class="social-icon"
                    ><i class="fas fa-envelope"></i
                  ></a>
                </div>
              </div>
            </div>
          </div>

          <div class="team-tech">
            <h3 class="tech-title">
              <i class="fas fa-code"></i> Technologies Used
            </h3>
            <div class="tech-list">
              <div class="tech-item"><i class="fab fa-swift"></i> Swift</div>
              <div class="tech-item"><i class="fab fa-android"></i> Kotlin</div>
              <div class="tech-item">
                <i class="fab fa-react"></i> React Native
              </div>
              <div class="tech-item"><i class="fas fa-fire"></i> Firebase</div>
              <div class="tech-item">
                <i class="fas fa-mobile-alt"></i> Flutter
              </div>
              <div class="tech-item">
                <i class="fas fa-cubes"></i> ARKit/ARCore
              </div>
            </div>
          </div>
        </div>

        <!-- AI Team -->
        <div class="team-content ai-team" id="ai-team">
          <div class="team-intro">
            <h3>AI Team</h3>
            <p>
              Our AI team develops intelligent algorithms and models that power
              the core functionality of our application. They specialize in
              machine learning, computer vision, and natural language processing
              to create smarter, more responsive systems.
            </p>
          </div>

          <div class="team-members">
            <!-- AI Team Member 1 -->
            <div class="member-card">
              <div class="member-img-container">
                <div class="member-img"><i class="fas fa-user"></i></div>
                <div class="view-profile">
                  <i class="fas fa-id-card"></i> View Profile
                </div>
              </div>
              <div class="member-info">
                <h3 class="member-name">Dr. James Lin</h3>
                <div class="member-role">ML Research Lead</div>
                <div class="member-social">
                  <a href="#" class="social-icon"
                    ><i class="fab fa-github"></i
                  ></a>
                  <a href="#" class="social-icon"
                    ><i class="fab fa-linkedin"></i
                  ></a>
                  <a href="#" class="social-icon"
                    ><i class="fas fa-envelope"></i
                  ></a>
                </div>
              </div>
            </div>

            <!-- AI Team Member 2 -->
            <div class="member-card">
              <div class="member-img-container">
                <div class="member-img"><i class="fas fa-user"></i></div>
                <div class="view-profile">
                  <i class="fas fa-id-card"></i> View Profile
                </div>
              </div>
              <div class="member-info">
                <h3 class="member-name">Sophia Rodriguez</h3>
                <div class="member-role">Data Scientist</div>
                <div class="member-social">
                  <a href="#" class="social-icon"
                    ><i class="fab fa-github"></i
                  ></a>
                  <a href="#" class="social-icon"
                    ><i class="fab fa-linkedin"></i
                  ></a>
                  <a href="#" class="social-icon"
                    ><i class="fas fa-envelope"></i
                  ></a>
                </div>
              </div>
            </div>

            <!-- AI Team Member 3 -->
            <div class="member-card">
              <div class="member-img-container">
                <div class="member-img"><i class="fas fa-user"></i></div>
                <div class="view-profile">
                  <i class="fas fa-id-card"></i> View Profile
                </div>
              </div>
              <div class="member-info">
                <h3 class="member-name">Omar Hassan</h3>
                <div class="member-role">ML Engineer</div>
                <div class="member-social">
                  <a href="#" class="social-icon"
                    ><i class="fab fa-github"></i
                  ></a>
                  <a href="#" class="social-icon"
                    ><i class="fab fa-linkedin"></i
                  ></a>
                  <a href="#" class="social-icon"
                    ><i class="fas fa-envelope"></i
                  ></a>
                </div>
              </div>
            </div>
          </div>

          <div class="team-tech">
            <h3 class="tech-title">
              <i class="fas fa-code"></i> Technologies Used
            </h3>
            <div class="tech-list">
              <div class="tech-item"><i class="fab fa-python"></i> Python</div>
              <div class="tech-item">
                <i class="fas fa-brain"></i> TensorFlow
              </div>
              <div class="tech-item"><i class="fas fa-fire"></i> PyTorch</div>
              <div class="tech-item">
                <i class="fas fa-robot"></i> Scikit-Learn
              </div>
              <div class="tech-item"><i class="fas fa-eye"></i> OpenCV</div>
              <div class="tech-item">
                <i class="fas fa-chart-bar"></i> Pandas
              </div>
              <div class="tech-item"><i class="fas fa-comment"></i> NLTK</div>
            </div>
          </div>
        </div>

        <!-- Embedded Systems Team -->
        <div class="team-content embedded-team" id="embedded-team">
          <div class="team-intro">
            <h3>Embedded Systems Team</h3>
            <p>
              Our embedded systems team designs and develops the physical
              components and low-level software that interact with the
              environment. They handle sensor integration, circuit design, and
              firmware development for IoT devices.
            </p>
          </div>

          <div class="team-members">
            <!-- Embedded Team Member 1 -->
            <div class="member-card">
              <div class="member-img-container">
                <div class="member-img"><i class="fas fa-user"></i></div>
                <div class="view-profile">
                  <i class="fas fa-id-card"></i> View Profile
                </div>
              </div>
              <div class="member-info">
                <h3 class="member-name">Mark Thompson</h3>
                <div class="member-role">Hardware Engineer</div>
                <div class="member-social">
                  <a href="#" class="social-icon"
                    ><i class="fab fa-github"></i
                  ></a>
                  <a href="#" class="social-icon"
                    ><i class="fab fa-linkedin"></i
                  ></a>
                  <a href="#" class="social-icon"
                    ><i class="fas fa-envelope"></i
                  ></a>
                </div>
              </div>
            </div>

            <!-- Embedded Team Member 2 -->
            <div class="member-card">
              <div class="member-img-container">
                <div class="member-img"><i class="fas fa-user"></i></div>
                <div class="view-profile">
                  <i class="fas fa-id-card"></i> View Profile
                </div>
              </div>
              <div class="member-info">
                <h3 class="member-name">Keiko Tanaka</h3>
                <div class="member-role">Firmware Developer</div>
                <div class="member-social">
                  <a href="#" class="social-icon"
                    ><i class="fab fa-github"></i
                  ></a>
                  <a href="#" class="social-icon"
                    ><i class="fab fa-linkedin"></i
                  ></a>
                  <a href="#" class="social-icon"
                    ><i class="fas fa-envelope"></i
                  ></a>
                </div>
              </div>
            </div>

            <!-- Embedded Team Member 3 -->
            <div class="member-card">
              <div class="member-img-container">
                <div class="member-img"><i class="fas fa-user"></i></div>
                <div class="view-profile">
                  <i class="fas fa-id-card"></i> View Profile
                </div>
              </div>
              <div class="member-info">
                <h3 class="member-name">Carlos Mendez</h3>
                <div class="member-role">Circuit Designer</div>
                <div class="member-social">
                  <a href="#" class="social-icon"
                    ><i class="fab fa-github"></i
                  ></a>
                  <a href="#" class="social-icon"
                    ><i class="fab fa-linkedin"></i
                  ></a>
                  <a href="#" class="social-icon"
                    ><i class="fas fa-envelope"></i
                  ></a>
                </div>
              </div>
            </div>
          </div>

          <div class="team-tech">
            <h3 class="tech-title">
              <i class="fas fa-code"></i> Technologies Used
            </h3>
            <div class="tech-list">
              <div class="tech-item">
                <i class="fas fa-microchip"></i> Arduino
              </div>
              <div class="tech-item">
                <i class="fas fa-raspberry-pi"></i> Raspberry Pi
              </div>
              <div class="tech-item"><i class="fas fa-code"></i> C/C++</div>
              <div class="tech-item"><i class="fas fa-cogs"></i> ESP32</div>
              <div class="tech-item">
                <i class="fas fa-project-diagram"></i> PCB Design
              </div>
              <div class="tech-item">
                <i class="fas fa-network-wired"></i> MQTT
              </div>
            </div>
          </div>
        </div>

        <!-- All Members Team -->
        <div class="team-content all-team active" id="all-team">
          <div class="team-intro">
            <h3>All Team Members</h3>
            <p>
              Our complete project team consists of talented professionals
              across various disciplines working together to create an
              innovative solution.
            </p>
          </div>

          <div class="team-members all-members">
            <!-- This will be populated with all team members from JS -->
          </div>
        </div>
      </section>

      <!-- progress-section -->
      <section id="progress" class="progress-section">
        <div class="progress-bg-decoration">
          <div class="progress-shape shape-1"></div>
          <div class="progress-shape shape-2"></div>
          <div class="progress-shape shape-3"></div>
        </div>

        <div class="container">
          <h2 class="section-title" data-aos="fade-up">Project Progress</h2>
          <p class="progress-intro" data-aos="fade-up" data-aos-delay="100">
            Track our team's progress across different aspects of the project
            with our interactive dashboard.
          </p>

          <div class="progress-dashboard">
            <!-- Overall Progress Card with Circular Indicator -->
            <div
              class="progress-card overall-progress"
              data-aos="zoom-in"
              data-aos-delay="200"
            >
              <div class="progress-content-wrapper">
                <div class="progress-text">
                  <h3>Overall <span class="highlight">Completion</span></h3>
                  <p class="progress-description">
                    We're making excellent progress toward our project goals
                    with 85% completion across all components. Our team is
                    currently focused on testing and optimization phases.
                  </p>
                  <div class="progress-milestones">
                    <div class="milestone completed">
                      <span class="milestone-dot"></span>
                      <span class="milestone-label">Planning</span>
                    </div>
                    <div class="milestone completed">
                      <span class="milestone-dot"></span>
                      <span class="milestone-label">Development</span>
                    </div>
                    <div class="milestone active">
                      <span class="milestone-dot"></span>
                      <span class="milestone-label">Testing</span>
                    </div>
                    <div class="milestone">
                      <span class="milestone-dot"></span>
                      <span class="milestone-label">Deployment</span>
                    </div>
                  </div>
                </div>
                <div class="progress-visual">
                  <div class="circular-progress" data-percentage="85">
                    <svg
                      class="progress-ring"
                      width="200"
                      height="200"
                      viewBox="0 0 200 200"
                    >
                      <defs>
                        <linearGradient
                          id="progressGradient"
                          x1="0%"
                          y1="0%"
                          x2="100%"
                          y2="0%"
                        >
                          <stop offset="0%" stop-color="#4895ef" />
                          <stop offset="50%" stop-color="#4361ee" />
                          <stop offset="100%" stop-color="#f72585" />
                        </linearGradient>
                      </defs>
                      <circle
                        class="progress-ring-circle-bg"
                        cx="100"
                        cy="100"
                        r="85"
                      />
                      <circle
                        class="progress-ring-circle"
                        cx="100"
                        cy="100"
                        r="85"
                        stroke-dasharray="534"
                        stroke-dashoffset="534"
                      />
                    </svg>
                    <div class="progress-value">
                      <span class="percentage-value">85</span
                      ><span class="percentage-symbol">%</span>
                      <span class="progress-label">Completed</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Progress Grid -->
            <div class="progress-grid">
              <!-- Frontend Card -->
              <div
                class="progress-card"
                data-aos="fade-up"
                data-aos-delay="300"
              >
                <div class="card-decoration">
                  <div class="decoration-circle"></div>
                </div>
                <div class="progress-header">
                  <div class="progress-icon">
                    <i class="fas fa-laptop-code"></i>
                  </div>
                  <h3>Frontend</h3>
                  <div class="progress-badge">90%</div>
                </div>
                <div class="progress-chart">
                  <div class="chart-bar">
                    <div class="chart-fill" style="width: 90%"></div>
                  </div>
                </div>
                <ul class="progress-details">
                  <li class="completed">
                    <i class="fas fa-check"></i> User Interface
                  </li>
                  <li class="completed">
                    <i class="fas fa-check"></i> Responsive Design
                  </li>
                  <li class="completed">
                    <i class="fas fa-check"></i> Interactive Elements
                  </li>
                  <li class="in-progress">
                    <i class="fas fa-spinner fa-spin"></i> Final Polishing
                  </li>
                </ul>
              </div>

              <!-- Backend Card -->
              <div
                class="progress-card"
                data-aos="fade-up"
                data-aos-delay="400"
              >
                <div class="card-decoration">
                  <div class="decoration-circle"></div>
                </div>
                <div class="progress-header">
                  <div class="progress-icon">
                    <i class="fas fa-server"></i>
                  </div>
                  <h3>Backend</h3>
                  <div class="progress-badge">85%</div>
                </div>
                <div class="progress-chart">
                  <div class="chart-bar">
                    <div class="chart-fill" style="width: 85%"></div>
                  </div>
                </div>
                <ul class="progress-details">
                  <li class="completed">
                    <i class="fas fa-check"></i> API Development
                  </li>
                  <li class="completed">
                    <i class="fas fa-check"></i> Database Design
                  </li>
                  <li class="in-progress">
                    <i class="fas fa-spinner fa-spin"></i> Performance
                    Optimization
                  </li>
                  <li class="pending">
                    <i class="fas fa-clock"></i> Security Audit
                  </li>
                </ul>
              </div>

              <!-- Mobile App Card -->
              <div
                class="progress-card"
                data-aos="fade-up"
                data-aos-delay="500"
              >
                <div class="card-decoration">
                  <div class="decoration-circle"></div>
                </div>
                <div class="progress-header">
                  <div class="progress-icon">
                    <i class="fas fa-mobile-alt"></i>
                  </div>
                  <h3>Mobile App</h3>
                  <div class="progress-badge">75%</div>
                </div>
                <div class="progress-chart">
                  <div class="chart-bar">
                    <div class="chart-fill" style="width: 75%"></div>
                  </div>
                </div>
                <ul class="progress-details">
                  <li class="completed">
                    <i class="fas fa-check"></i> Core Functionality
                  </li>
                  <li class="completed">
                    <i class="fas fa-check"></i> Authentication
                  </li>
                  <li class="in-progress">
                    <i class="fas fa-spinner fa-spin"></i> Push Notifications
                  </li>
                  <li class="pending">
                    <i class="fas fa-clock"></i> App Store Submission
                  </li>
                </ul>
              </div>

              <!-- AI Components Card -->
              <div
                class="progress-card"
                data-aos="fade-up"
                data-aos-delay="600"
              >
                <div class="card-decoration">
                  <div class="decoration-circle"></div>
                </div>
                <div class="progress-header">
                  <div class="progress-icon">
                    <i class="fas fa-brain"></i>
                  </div>
                  <h3>AI Components</h3>
                  <div class="progress-badge">80%</div>
                </div>
                <div class="progress-chart">
                  <div class="chart-bar">
                    <div class="chart-fill" style="width: 80%"></div>
                  </div>
                </div>
                <ul class="progress-details">
                  <li class="completed">
                    <i class="fas fa-check"></i> Data Processing
                  </li>
                  <li class="completed">
                    <i class="fas fa-check"></i> Model Training
                  </li>
                  <li class="in-progress">
                    <i class="fas fa-spinner fa-spin"></i> Optimization
                  </li>
                  <li class="pending">
                    <i class="fas fa-clock"></i> Final Integration
                  </li>
                </ul>
              </div>
            </div>

            <!-- Hardware Components Card -->
            <div
              class="progress-card hardware-progress"
              data-aos="fade-up"
              data-aos-delay="700"
            >
              <div class="card-decoration">
                <div class="decoration-circle"></div>
              </div>
              <div class="progress-header">
                <div class="progress-icon">
                  <i class="fas fa-microchip"></i>
                </div>
                <h3>Hardware Components</h3>
                <div class="progress-badge">95%</div>
              </div>
              <div class="progress-chart">
                <div class="chart-bar">
                  <div class="chart-fill" style="width: 95%"></div>
                </div>
              </div>
              <div class="hardware-details">
                <div class="hardware-item">
                  <div class="hardware-icon">
                    <i class="fas fa-microchip"></i>
                  </div>
                  <div class="hardware-info">
                    <h4>Sensors</h4>
                    <div class="mini-progress">
                      <div class="mini-bar" style="width: 100%"></div>
                    </div>
                    <span class="completion-text">100% Complete</span>
                  </div>
                </div>
                <div class="hardware-item">
                  <div class="hardware-icon">
                    <i class="fas fa-broadcast-tower"></i>
                  </div>
                  <div class="hardware-info">
                    <h4>Connectivity</h4>
                    <div class="mini-progress">
                      <div class="mini-bar" style="width: 90%"></div>
                    </div>
                    <span class="completion-text">90% Complete</span>
                  </div>
                </div>
                <div class="hardware-item">
                  <div class="hardware-icon">
                    <i class="fas fa-cogs"></i>
                  </div>
                  <div class="hardware-info">
                    <h4>Firmware</h4>
                    <div class="mini-progress">
                      <div class="mini-bar" style="width: 95%"></div>
                    </div>
                    <span class="completion-text">95% Complete</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Team Velocity Card -->
            <div
              class="progress-card team-velocity"
              data-aos="fade-up"
              data-aos-delay="800"
            >
              <div class="card-decoration">
                <div class="decoration-circle"></div>
              </div>
              <div class="progress-header">
                <div class="progress-icon">
                  <i class="fas fa-tachometer-alt"></i>
                </div>
                <h3>Team Velocity</h3>
              </div>
              <div class="velocity-metrics">
                <div class="velocity-metric">
                  <div class="velocity-circle">
                    <svg
                      class="velocity-ring"
                      width="120"
                      height="120"
                      viewBox="0 0 120 120"
                    >
                      <circle class="velocity-ring-bg" cx="60" cy="60" r="50" />
                      <circle
                        class="velocity-ring-fill sprint"
                        cx="60"
                        cy="60"
                        r="50"
                        stroke-dasharray="314.16"
                        stroke-dashoffset="78.54"
                      />
                    </svg>
                    <div class="velocity-value">75%</div>
                  </div>
                  <div class="velocity-label">Sprint Goals</div>
                </div>
                <div class="velocity-metric">
                  <div class="velocity-circle">
                    <svg
                      class="velocity-ring"
                      width="120"
                      height="120"
                      viewBox="0 0 120 120"
                    >
                      <circle class="velocity-ring-bg" cx="60" cy="60" r="50" />
                      <circle
                        class="velocity-ring-fill quality"
                        cx="60"
                        cy="60"
                        r="50"
                        stroke-dasharray="314.16"
                        stroke-dashoffset="31.42"
                      />
                    </svg>
                    <div class="velocity-value">90%</div>
                  </div>
                  <div class="velocity-label">Quality Score</div>
                </div>
                <div class="velocity-metric">
                  <div class="velocity-circle">
                    <svg
                      class="velocity-ring"
                      width="120"
                      height="120"
                      viewBox="0 0 120 120"
                    >
                      <circle class="velocity-ring-bg" cx="60" cy="60" r="50" />
                      <circle
                        class="velocity-ring-fill efficiency"
                        cx="60"
                        cy="60"
                        r="50"
                        stroke-dasharray="314.16"
                        stroke-dashoffset="62.83"
                      />
                    </svg>
                    <div class="velocity-value">80%</div>
                  </div>
                  <div class="velocity-label">Team Efficiency</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!--roadmap section -->
      <section id="roadmap" class="roadmap-section">
        <div class="decorative-shape shape-1"></div>
        <div class="decorative-shape shape-2"></div>
        <div class="decorative-shape shape-3"></div>
        <div class="decorative-shape shape-4"></div>
        <!-- The floating icons and particles will be added via JavaScript -->
        <div class="container">
          <h2 class="section-title" data-aos="fade-up">Project Roadmap</h2>
          <p class="roadmap-intro" data-aos="fade-up" data-aos-delay="100">
            Our development path from concept to completion.
          </p>

          <div class="roadmap-summary" data-aos="fade-up" data-aos-delay="200">
            <!-- Particle background container -->
            <div class="particles-container" id="roadmap-particles"></div>

            <!-- 3D Progress Circular with enhanced effects -->
            <div
              class="progress-circular"
              data-tilt
              data-tilt-max="10"
              data-tilt-perspective="1000"
            >
              <div class="progress-glow"></div>
              <div class="progress-circular-outer">
                <div class="progress-circular-track"></div>
                <div
                  class="progress-circular-fill"
                  style="--progress-value: 67%"
                ></div>
                <div class="progress-circular-inner">
                  <div
                    class="progress-circular-text"
                    style="
                      animation: 0.5s ease-in-out 0s 1 normal none running
                        number-celebrate;
                    "
                  >
                    67%
                  </div>
                  <div class="progress-circular-dots">
                    <span class="dot dot-1"></span>
                    <span class="dot dot-2"></span>
                    <span class="dot dot-3"></span>
                  </div>
                </div>
              </div>
              <div class="progress-label">Overall Completion</div>
              <div class="progress-shine"></div>
            </div>

            <!-- Enhanced stats with interactive elements -->
            <div class="roadmap-stats">
              <div
                class="stat-item"
                data-aos="fade-up"
                data-aos-delay="250"
                data-tilt
                data-tilt-max="15"
              >
                <div class="stat-glow"></div>
                <div class="metric-icon">
                  <i class="fas fa-flag-checkered"></i>
                  <div class="icon-particles"></div>
                </div>
                <div class="stat-content">
                  <span class="metric-value counter-value" data-target="4"
                    >4</span
                  >
                  <span class="metric-separator">/</span>
                  <span class="metric-total">6</span>
                </div>
                <span class="milestone">Milestones Achieved</span>
                <div class="stat-progress">
                  <div class="stat-progress-bar">
                    <div class="stat-progress-fill" style="width: 67%"></div>
                  </div>
                </div>
              </div>

              <div
                class="stat-item"
                data-aos="fade-up"
                data-aos-delay="300"
                data-tilt
                data-tilt-max="15"
              >
                <div class="stat-glow"></div>
                <div class="metric-icon">
                  <i class="fas fa-tasks"></i>
                  <div class="icon-particles"></div>
                </div>
                <div class="stat-content">
                  <span class="metric-value counter-value" data-target="3"
                    >3</span
                  >
                </div>
                <span class="milestone">Sprints Completed</span>
                <div class="stat-progress">
                  <div class="stat-progress-bar">
                    <div class="stat-progress-fill" style="width: 50%"></div>
                  </div>
                </div>
              </div>

              <div
                class="stat-item"
                data-aos="fade-up"
                data-aos-delay="350"
                data-tilt
                data-tilt-max="15"
              >
                <div class="stat-glow"></div>
                <div class="metric-icon">
                  <i class="fas fa-calendar-day"></i>
                  <div class="icon-particles"></div>
                </div>
                <div class="stat-content">
                  <span class="metric-value counter-value" data-target="8"
                    >8</span
                  >
                </div>
                <span class="milestone">Days to Next Milestone</span>
                <div class="stat-badge pulse-badge">Coming Soon!</div>
              </div>
            </div>
          </div>

          <div class="roadmap-filters" data-aos="fade-up" data-aos-delay="400">
            <button class="roadmap-filter active" data-filter="all">
              <i class="fas fa-th-list"></i> All Phases
            </button>
            <button class="roadmap-filter" data-filter="complete">
              <i class="fas fa-check-circle"></i> Completed
            </button>
            <button class="roadmap-filter" data-filter="current">
              <i class="fas fa-sync"></i> In Progress
            </button>
            <button class="roadmap-filter" data-filter="upcoming">
              <i class="fas fa-hourglass-start"></i> Upcoming
            </button>
          </div>

          <div
            class="roadmap-view-toggle"
            data-aos="fade-up"
            data-aos-delay="450"
          >
            <button class="view-toggle active" data-view="timeline">
              <i class="fas fa-stream"></i> Timeline
            </button>
            <button class="view-toggle" data-view="cards">
              <i class="fas fa-th-large"></i> Cards
            </button>
          </div>

          <div
            class="roadmap-container timeline-view active"
            data-aos="fade-up"
            data-aos-delay="500"
          >
            <div class="roadmap-timeline-wrapper">
              <div class="roadmap-line"></div>
              <div class="roadmap-progress-line" style="height: 67%"></div>
            </div>

            <!-- Roadmap Items -->
            <div class="roadmap-item roadmap-complete" data-phase="complete">
              <div class="roadmap-date">January 2025</div>
              <div class="roadmap-marker" data-tooltip="Sprint 1"></div>
              <div class="roadmap-content">
                <div class="content-header">
                  <h3>Project Inception</h3>
                  <div class="roadmap-status">Completed</div>
                </div>
                <p>
                  Initial concept development, team formation, and project
                  planning.
                </p>
                <div class="roadmap-details">
                  <span class="detail-item"
                    ><i class="fas fa-tasks"></i> 12 Tasks</span
                  >
                  <span class="detail-item"
                    ><i class="fas fa-calendar-check"></i> On Time</span
                  >
                </div>
                <button class="expand-details">
                  <span class="expand-text">View Details</span>
                  <i class="fas fa-chevron-down"></i>
                </button>
                <div class="expanded-content">
                  <h4>Key Achievements</h4>
                  <ul class="achievement-list">
                    <li>
                      <i class="fas fa-check"></i> Project charter defined
                    </li>
                    <li><i class="fas fa-check"></i> Team roles assigned</li>
                    <li>
                      <i class="fas fa-check"></i> Initial requirements gathered
                    </li>
                    <li>
                      <i class="fas fa-check"></i> Project timeline established
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="roadmap-item roadmap-complete" data-phase="complete">
              <div class="roadmap-date">February 2025</div>
              <div class="roadmap-marker" data-tooltip="Sprint 2"></div>
              <div class="roadmap-content">
                <div class="content-header">
                  <h3>Design Phase</h3>
                  <div class="roadmap-status">Completed</div>
                </div>
                <p>
                  User interface design, database schema, and system
                  architecture.
                </p>
                <div class="roadmap-details">
                  <span class="detail-item"
                    ><i class="fas fa-tasks"></i> 15 Tasks</span
                  >
                  <span class="detail-item"
                    ><i class="fas fa-calendar-check"></i> On Time</span
                  >
                </div>
                <button class="expand-details">
                  <span class="expand-text">View Details</span>
                  <i class="fas fa-chevron-down"></i>
                </button>
                <div class="expanded-content">
                  <h4>Key Achievements</h4>
                  <ul class="achievement-list">
                    <li>
                      <i class="fas fa-check"></i> UI/UX wireframes finalized
                    </li>
                    <li>
                      <i class="fas fa-check"></i> Database architecture
                      approved
                    </li>
                    <li>
                      <i class="fas fa-check"></i> System interactions mapped
                    </li>
                    <li>
                      <i class="fas fa-check"></i> Technology stack selected
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="roadmap-item roadmap-complete" data-phase="complete">
              <div class="roadmap-date">March 2025</div>
              <div class="roadmap-marker" data-tooltip="Sprint 3"></div>
              <div class="roadmap-content">
                <div class="content-header">
                  <h3>Core Development</h3>
                  <div class="roadmap-status">Completed</div>
                </div>
                <p>
                  Backend API development, frontend implementation, and database
                  setup.
                </p>
                <div class="roadmap-details">
                  <span class="detail-item"
                    ><i class="fas fa-tasks"></i> 18 Tasks</span
                  >
                  <span class="detail-item"
                    ><i class="fas fa-calendar-check"></i> On Time</span
                  >
                </div>
                <button class="expand-details">
                  <span class="expand-text">View Details</span>
                  <i class="fas fa-chevron-down"></i>
                </button>
                <div class="expanded-content">
                  <h4>Key Achievements</h4>
                  <ul class="achievement-list">
                    <li><i class="fas fa-check"></i> Core APIs implemented</li>
                    <li><i class="fas fa-check"></i> Data models created</li>
                    <li>
                      <i class="fas fa-check"></i> Frontend components built
                    </li>
                    <li>
                      <i class="fas fa-check"></i> Initial integration testing
                      completed
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="roadmap-item roadmap-current" data-phase="current">
              <div class="roadmap-date">April 2025</div>
              <div class="roadmap-marker" data-tooltip="Sprint 4"></div>
              <div class="roadmap-content">
                <div class="content-header">
                  <h3>Integration Phase</h3>
                  <div class="roadmap-status">In Progress</div>
                </div>
                <p>
                  Connecting all components, hardware integration, and initial
                  testing.
                </p>
                <div class="roadmap-progress">
                  <div class="mini-progress">
                    <div class="mini-bar" style="width: 65%"></div>
                  </div>
                  <span>65% Complete</span>
                </div>
                <div class="roadmap-details">
                  <span class="detail-item"
                    ><i class="fas fa-tasks"></i> 10/16 Tasks</span
                  >
                  <span class="detail-item"
                    ><i class="fas fa-calendar-alt"></i> 8 Days Left</span
                  >
                </div>
                <button class="expand-details">
                  <span class="expand-text">View Details</span>
                  <i class="fas fa-chevron-down"></i>
                </button>
                <div class="expanded-content">
                  <h4>In Progress Activities</h4>
                  <ul class="achievement-list">
                    <li>
                      <i class="fas fa-check"></i> Sensor integration completed
                    </li>
                    <li>
                      <i class="fas fa-check"></i> Mobile app connected to
                      back-end
                    </li>
                    <li>
                      <i class="fas fa-spinner fa-spin"></i> Hardware-software
                      interface testing
                    </li>
                    <li>
                      <i class="fas fa-spinner fa-spin"></i> Performance
                      optimization
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="roadmap-item" data-phase="upcoming">
              <div class="roadmap-date">May 2025</div>
              <div class="roadmap-marker" data-tooltip="Sprint 5"></div>
              <div class="roadmap-content">
                <div class="content-header">
                  <h3>Testing & Refinement</h3>
                  <div class="roadmap-status">Upcoming</div>
                </div>
                <p>
                  Quality assurance, user testing, and performance optimization.
                </p>
                <div class="roadmap-details">
                  <span class="detail-item"
                    ><i class="fas fa-tasks"></i> 14 Tasks Planned</span
                  >
                  <span class="detail-item"
                    ><i class="fas fa-calendar-alt"></i> 30 Days</span
                  >
                </div>
                <button class="expand-details">
                  <span class="expand-text">View Details</span>
                  <i class="fas fa-chevron-down"></i>
                </button>
                <div class="expanded-content">
                  <h4>Planned Activities</h4>
                  <ul class="achievement-list">
                    <li>
                      <i class="fas fa-hourglass-start"></i> Quality assurance
                      testing
                    </li>
                    <li>
                      <i class="fas fa-hourglass-start"></i> User acceptance
                      testing
                    </li>
                    <li>
                      <i class="fas fa-hourglass-start"></i> Performance
                      benchmarking
                    </li>
                    <li>
                      <i class="fas fa-hourglass-start"></i> Security audit
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="roadmap-item" data-phase="upcoming">
              <div class="roadmap-date">June 2025</div>
              <div class="roadmap-marker" data-tooltip="Sprint 6"></div>
              <div class="roadmap-content">
                <div class="content-header">
                  <h3>Deployment</h3>
                  <div class="roadmap-status">Upcoming</div>
                </div>
                <p>
                  Final deployment, documentation, and project presentation.
                </p>
                <div class="roadmap-details">
                  <span class="detail-item"
                    ><i class="fas fa-tasks"></i> 10 Tasks Planned</span
                  >
                  <span class="detail-item"
                    ><i class="fas fa-calendar-alt"></i> 21 Days</span
                  >
                </div>
                <button class="expand-details">
                  <span class="expand-text">View Details</span>
                  <i class="fas fa-chevron-down"></i>
                </button>
                <div class="expanded-content">
                  <h4>Planned Activities</h4>
                  <ul class="achievement-list">
                    <li>
                      <i class="fas fa-hourglass-start"></i> Final
                      infrastructure setup
                    </li>
                    <li>
                      <i class="fas fa-hourglass-start"></i> Production
                      deployment
                    </li>
                    <li>
                      <i class="fas fa-hourglass-start"></i> Technical
                      documentation finalization
                    </li>
                    <li>
                      <i class="fas fa-hourglass-start"></i> Project
                      demonstration and presentation
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- Card View (Alternative Layout) -->
          <div
            class="roadmap-container cards-view"
            data-aos="fade-up"
            data-aos-delay="500"
          >
            <div class="roadmap-cards">
              <!-- Card for each phase -->
              <div class="roadmap-card roadmap-complete" data-phase="complete">
                <div class="card-status-indicator"></div>
                <div class="card-header">
                  <div class="card-date">January 2025</div>
                  <div class="roadmap-status">Completed</div>
                </div>
                <h3>Project Inception</h3>
                <p>
                  Initial concept development, team formation, and project
                  planning.
                </p>
                <div class="roadmap-details">
                  <span class="detail-item"
                    ><i class="fas fa-tasks"></i> 12 Tasks</span
                  >
                  <span class="detail-item"
                    ><i class="fas fa-calendar-check"></i> On Time</span
                  >
                </div>
                <button class="view-card-details">View Details</button>
              </div>

              <!-- Remaining roadmap cards -->
              <!-- ... Similar cards for each phase ... -->
            </div>
          </div>

          <div class="roadmap-legend" data-aos="fade-up" data-aos-delay="600">
            <div class="legend-item">
              <span class="legend-dot completed"></span> Completed
            </div>
            <div class="legend-item">
              <span class="legend-dot current"></span> In Progress
            </div>
            <div class="legend-item">
              <span class="legend-dot upcoming"></span> Upcoming
            </div>
          </div>
        </div>
      </section>

      <!-- sprint-progress-section -->
      <section
        id="sprint-progress"
        class="sprint-progress-section"
        role="region"
        aria-labelledby="heading-sprint-progress"
      >
        <div class="section-background-wave"></div>
        <div class="container">
          <h2 class="section-title animate-in" id="heading-sprint-progress">
            Sprint Progress
          </h2>
          <p class="sprint-intro animate-in animate-delay-1">
            Detailed progress tracking of our development sprints with velocity
            metrics and completion analysis.
          </p>

          <div class="sprint-overview animate-in animate-delay-2">
            <div class="sprint-chart-container">
              <div class="chart-header">
                <h3>Burndown Analysis</h3>
                <div class="chart-controls">
                  <select id="burndownChartType" aria-label="Chart type">
                    <option value="burndown">Burndown</option>
                    <option value="velocity">Velocity</option>
                  </select>
                </div>
              </div>
              <canvas id="sprintBurndownChart"></canvas>
            </div>

            <div class="sprint-metrics">
              <div class="metric-card" data-tilt data-tilt-scale="1.05">
                <div class="metric-icon">
                  <i class="fas fa-calendar-check pulse"></i>
                </div>
                <h3>Total Sprints</h3>
                <div class="metric-value counter" data-target="20">20</div>
                <div class="metric-progress">
                  <div class="metric-progress-bar">
                    <div class="metric-progress-fill" style="width: 20%"></div>
                  </div>
                  <div class="metric-progress-text">4/20 Completed</div>
                </div>
              </div>

              <div class="metric-card" data-tilt data-tilt-scale="1.05">
                <div class="metric-icon">
                  <i class="fas fa-tasks pulse"></i>
                </div>
                <h3>Total Tasks</h3>
                <div class="metric-value counter" data-target="85">85</div>
                <div class="metric-progress">
                  <div class="metric-progress-bar">
                    <div class="metric-progress-fill" style="width: 65%"></div>
                  </div>
                  <div class="metric-progress-text">55/85 Completed</div>
                </div>
              </div>

              <div class="metric-card" data-tilt data-tilt-scale="1.05">
                <div class="metric-icon"><i class="fas fa-bug pulse"></i></div>
                <h3>Issues Resolved</h3>
                <div class="metric-value counter" data-target="32">32</div>
                <div class="metric-progress">
                  <div class="metric-progress-bar">
                    <div class="metric-progress-fill" style="width: 91%"></div>
                  </div>
                  <div class="metric-progress-text">29/32 Resolved</div>
                </div>
              </div>

              <div
                class="metric-card highlight-card"
                data-tilt
                data-tilt-scale="1.05"
              >
                <div class="metric-icon"><i class="fas fa-chart-line"></i></div>
                <h3 class="highlight-title">Team Velocity</h3>
                <div class="metric-value">42</div>
                <div class="velocity-trend">
                  <span class="trend-icon positive"
                    ><i class="fas fa-arrow-up"></i
                  ></span>
                  <span class="trend-value">+15%</span> from last sprint
                </div>
              </div>
            </div>
          </div>

          <div class="sprint-details-container animate-in animate-delay-3">
            <div class="sprint-tabs-wrapper">
              <button
                class="sprint-nav-button prev-sprints"
                aria-label="View previous sprints"
              >
                <i class="fas fa-chevron-left"></i>
              </button>

              <div class="sprint-tabs" role="tablist">
                <button
                  class="sprint-tab active"
                  role="tab"
                  aria-selected="true"
                  aria-controls="sprint-1-panel"
                  data-sprint="1"
                >
                  <span class="tab-status completed" aria-hidden="true"></span>
                  Sprint 1
                </button>
                <button
                  class="sprint-tab"
                  role="tab"
                  aria-selected="false"
                  aria-controls="sprint-2-panel"
                  data-sprint="2"
                >
                  <span class="tab-status completed" aria-hidden="true"></span>
                  Sprint 2
                </button>
                <button
                  class="sprint-tab"
                  role="tab"
                  aria-selected="false"
                  aria-controls="sprint-3-panel"
                  data-sprint="3"
                >
                  <span class="tab-status completed" aria-hidden="true"></span>
                  Sprint 3
                </button>
                <button
                  class="sprint-tab"
                  role="tab"
                  aria-selected="false"
                  aria-controls="sprint-4-panel"
                  data-sprint="4"
                >
                  <span
                    class="tab-status in-progress"
                    aria-hidden="true"
                  ></span>
                  Sprint 4
                </button>
                <button
                  class="sprint-tab"
                  role="tab"
                  aria-selected="false"
                  aria-controls="sprint-5-panel"
                  data-sprint="5"
                >
                  <span class="tab-status upcoming" aria-hidden="true"></span>
                  Sprint 5
                </button>
                <button
                  class="sprint-tab"
                  role="tab"
                  aria-selected="false"
                  aria-controls="sprint-6-panel"
                  data-sprint="6"
                >
                  <span class="tab-status upcoming" aria-hidden="true"></span>
                  Sprint 6
                </button>
                <button
                  class="sprint-tab"
                  role="tab"
                  aria-selected="false"
                  aria-controls="sprint-7-panel"
                  data-sprint="7"
                >
                  <span class="tab-status upcoming" aria-hidden="true"></span>
                  Sprint 7
                </button>
                <button
                  class="sprint-tab"
                  role="tab"
                  aria-selected="false"
                  aria-controls="sprint-8-panel"
                  data-sprint="8"
                >
                  <span class="tab-status upcoming" aria-hidden="true"></span>
                  Sprint 8
                </button>
                <button
                  class="sprint-tab"
                  role="tab"
                  aria-selected="false"
                  aria-controls="sprint-9-panel"
                  data-sprint="9"
                >
                  <span class="tab-status upcoming" aria-hidden="true"></span>
                  Sprint 9
                </button>
                <button
                  class="sprint-tab"
                  role="tab"
                  aria-selected="false"
                  aria-controls="sprint-10-panel"
                  data-sprint="10"
                >
                  <span class="tab-status upcoming" aria-hidden="true"></span>
                  Sprint 10
                </button>
                <button
                  class="sprint-tab"
                  role="tab"
                  aria-selected="false"
                  aria-controls="sprint-11-panel"
                  data-sprint="11"
                >
                  <span class="tab-status upcoming" aria-hidden="true"></span>
                  Sprint 11
                </button>
                <button
                  class="sprint-tab"
                  role="tab"
                  aria-selected="false"
                  aria-controls="sprint-12-panel"
                  data-sprint="12"
                >
                  <span class="tab-status upcoming" aria-hidden="true"></span>
                  Sprint 12
                </button>
                <button
                  class="sprint-tab"
                  role="tab"
                  aria-selected="false"
                  aria-controls="sprint-13-panel"
                  data-sprint="13"
                >
                  <span class="tab-status upcoming" aria-hidden="true"></span>
                  Sprint 13
                </button>
                <button
                  class="sprint-tab"
                  role="tab"
                  aria-selected="false"
                  aria-controls="sprint-14-panel"
                  data-sprint="14"
                >
                  <span class="tab-status upcoming" aria-hidden="true"></span>
                  Sprint 14
                </button>
                <button
                  class="sprint-tab"
                  role="tab"
                  aria-selected="false"
                  aria-controls="sprint-15-panel"
                  data-sprint="15"
                >
                  <span class="tab-status upcoming" aria-hidden="true"></span>
                  Sprint 15
                </button>
                <button
                  class="sprint-tab"
                  role="tab"
                  aria-selected="false"
                  aria-controls="sprint-16-panel"
                  data-sprint="16"
                >
                  <span class="tab-status upcoming" aria-hidden="true"></span>
                  Sprint 16
                </button>
                <button
                  class="sprint-tab"
                  role="tab"
                  aria-selected="false"
                  aria-controls="sprint-17-panel"
                  data-sprint="17"
                >
                  <span class="tab-status upcoming" aria-hidden="true"></span>
                  Sprint 17
                </button>
                <button
                  class="sprint-tab"
                  role="tab"
                  aria-selected="false"
                  aria-controls="sprint-18-panel"
                  data-sprint="18"
                >
                  <span class="tab-status upcoming" aria-hidden="true"></span>
                  Sprint 18
                </button>
                <button
                  class="sprint-tab"
                  role="tab"
                  aria-selected="false"
                  aria-controls="sprint-19-panel"
                  data-sprint="19"
                >
                  <span class="tab-status upcoming" aria-hidden="true"></span>
                  Sprint 19
                </button>
                <button
                  class="sprint-tab"
                  role="tab"
                  aria-selected="false"
                  aria-controls="sprint-20-panel"
                  data-sprint="20"
                >
                  <span class="tab-status upcoming" aria-hidden="true"></span>
                  Sprint 20
                </button>
              </div>

              <button
                class="sprint-nav-button next-sprints"
                aria-label="View next sprints"
              >
                <i class="fas fa-chevron-right"></i>
              </button>
            </div>

            <div
              class="sprint-detail active"
              id="sprint-1"
              role="tabpanel"
              id="sprint-1-panel"
            >
              <div class="sprint-header">
                <div class="sprint-title-section">
                  <h3>Sprint 1: Project Setup</h3>
                  <div class="sprint-badges">
                    <div class="sprint-status completed">Completed</div>
                    <div class="sprint-timeframe">
                      <i class="far fa-calendar-alt"></i> Jan 1 - Jan 14, 2025
                    </div>
                  </div>
                </div>
                <div class="sprint-summary">
                  Project initialization and foundational setup with 100%
                  completion rate
                </div>
              </div>

              <div class="sprint-stats-row">
                <div class="sprint-stat" data-tilt data-tilt-scale="1.02">
                  <div class="stat-number counter" data-target="12">12/12</div>
                  <div class="stat-label">Tasks Completed</div>
                </div>
                <div class="sprint-stat" data-tilt data-tilt-scale="1.02">
                  <div class="stat-number counter" data-target="100">100%</div>
                  <div class="stat-label">Completion Rate</div>
                </div>
                <div class="sprint-stat" data-tilt data-tilt-scale="1.02">
                  <div class="stat-number counter" data-target="5">5</div>
                  <div class="stat-label">Issues Resolved</div>
                </div>
                <div
                  class="sprint-stat highlight-stat"
                  data-tilt
                  data-tilt-scale="1.02"
                >
                  <div class="stat-number">2</div>
                  <div class="stat-label">Days Ahead</div>
                </div>
              </div>

              <div class="sprint-content-grid">
                <div class="task-distribution">
                  <h4><i class="fas fa-pie-chart"></i> Task Distribution</h4>
                  <div class="distribution-chart-container">
                    <canvas id="taskDistChart1"></canvas>
                  </div>
                </div>

                <div class="key-achievements">
                  <h4><i class="fas fa-trophy"></i> Key Achievements</h4>
                  <ul class="achievement-list">
                    <li class="achievement-item">
                      <span class="achievement-check"
                        ><i class="fas fa-check-circle"></i
                      ></span>
                      Project kickoff completed with all stakeholders
                    </li>
                    <li class="achievement-item">
                      <span class="achievement-check"
                        ><i class="fas fa-check-circle"></i
                      ></span>
                      Team onboarding and setup completed
                    </li>
                    <li class="achievement-item">
                      <span class="achievement-check"
                        ><i class="fas fa-check-circle"></i
                      ></span>
                      Initial requirements documented and approved
                    </li>
                    <li class="achievement-item">
                      <span class="achievement-check"
                        ><i class="fas fa-check-circle"></i
                      ></span>
                      Development environment configured and tested
                    </li>
                  </ul>

                  <div class="sprint-documents">
                    <a href="#" class="document-link">
                      <i class="fas fa-file-pdf"></i> Sprint Review Document
                    </a>
                    <a href="#" class="document-link">
                      <i class="fas fa-file-alt"></i> Technical Specifications
                    </a>
                  </div>
                </div>
              </div>

              <div class="sprint-health">
                <h4>
                  <i class="fas fa-heartbeat"></i> Sprint Health Dashboard
                </h4>
                <div class="health-metrics-container">
                  <div class="health-metric">
                    <div class="metric-gauge">
                      <svg viewBox="0 0 120 120" class="gauge">
                        <circle
                          class="gauge-bg"
                          cx="60"
                          cy="60"
                          r="50"
                        ></circle>
                        <circle
                          class="gauge-fill"
                          cx="60"
                          cy="60"
                          r="50"
                          style="stroke-dashoffset: 25"
                        ></circle>
                        <text
                          class="gauge-value"
                          x="60"
                          y="65"
                          text-anchor="middle"
                        >
                          85%
                        </text>
                      </svg>
                      <div class="gauge-label">Scope Adherence</div>
                    </div>
                  </div>

                  <div class="health-metric">
                    <div class="metric-gauge">
                      <svg viewBox="0 0 120 120" class="gauge">
                        <circle
                          class="gauge-bg"
                          cx="60"
                          cy="60"
                          r="50"
                        ></circle>
                        <circle
                          class="gauge-fill quality"
                          cx="60"
                          cy="60"
                          r="50"
                          style="stroke-dashoffset: 50"
                        ></circle>
                        <text
                          class="gauge-value"
                          x="60"
                          y="65"
                          text-anchor="middle"
                        >
                          90%
                        </text>
                      </svg>
                      <div class="gauge-label">Quality</div>
                    </div>
                  </div>

                  <div class="health-metric">
                    <div class="metric-gauge">
                      <svg viewBox="0 0 120 120" class="gauge">
                        <circle
                          class="gauge-bg"
                          cx="60"
                          cy="60"
                          r="50"
                        ></circle>
                        <circle
                          class="gauge-fill velocity"
                          cx="60"
                          cy="60"
                          r="50"
                          style="stroke-dashoffset: 40"
                        ></circle>
                        <text
                          class="gauge-value"
                          x="60"
                          y="65"
                          text-anchor="middle"
                        >
                          110%
                        </text>
                      </svg>
                      <div class="gauge-label">Velocity</div>
                    </div>
                  </div>

                  <div class="health-metric">
                    <div class="metric-gauge">
                      <svg viewBox="0 0 120 120" class="gauge">
                        <circle
                          class="gauge-bg"
                          cx="60"
                          cy="60"
                          r="50"
                        ></circle>
                        <circle
                          class="gauge-fill satisfaction"
                          cx="60"
                          cy="60"
                          r="50"
                          style="stroke-dashoffset: 35"
                        ></circle>
                        <text
                          class="gauge-value"
                          x="60"
                          y="65"
                          text-anchor="middle"
                        >
                          4.5
                        </text>
                      </svg>
                      <div class="gauge-label">Team Satisfaction</div>
                    </div>
                  </div>
                </div>

                <div class="sprint-insights">
                  <div class="insight-title">Key Insights</div>
                  <ul class="insights-list">
                    <li>
                      <span class="insight-badge positive"
                        ><i class="fas fa-arrow-up"></i
                      ></span>
                      Team velocity exceeded expectations by 10%
                    </li>
                    <li>
                      <span class="insight-badge neutral"
                        ><i class="fas fa-minus"></i
                      ></span>
                      Scope remained stable with only minor adjustments
                    </li>
                    <li>
                      <span class="insight-badge positive"
                        ><i class="fas fa-check"></i
                      ></span>
                      Zero critical bugs reported during this sprint
                    </li>
                    <li>
                      <span class="insight-badge negative"
                        ><i class="fas fa-arrow-down"></i
                      ></span>
                      Technical debt increased slightly due to tight deadlines
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div
              class="sprint-detail"
              id="sprint-2"
              role="tabpanel"
              id="sprint-2-panel"
            >
              <div class="sprint-header">
                <div class="sprint-title-section">
                  <h3>Sprint 2: Design Phase</h3>
                  <div class="sprint-badges">
                    <div class="sprint-status completed">Completed</div>
                    <div class="sprint-timeframe">
                      <i class="far fa-calendar-alt"></i> Jan 15 - Jan 28, 2025
                    </div>
                  </div>
                </div>
                <div class="sprint-summary">
                  User interface design, database schema, and system
                  architecture with 95% completion rate
                </div>
              </div>

              <div class="sprint-stats-row">
                <div class="sprint-stat" data-tilt data-tilt-scale="1.02">
                  <div class="stat-number counter" data-target="14">14/15</div>
                  <div class="stat-label">Tasks Completed</div>
                </div>
                <div class="sprint-stat" data-tilt data-tilt-scale="1.02">
                  <div class="stat-number counter" data-target="95">95%</div>
                  <div class="stat-label">Completion Rate</div>
                </div>
                <div class="sprint-stat" data-tilt data-tilt-scale="1.02">
                  <div class="stat-number counter" data-target="7">7</div>
                  <div class="stat-label">Issues Resolved</div>
                </div>
                <div
                  class="sprint-stat highlight-stat"
                  data-tilt
                  data-tilt-scale="1.02"
                >
                  <div class="stat-number">1</div>
                  <div class="stat-label">Days Ahead</div>
                </div>
              </div>

              <div class="sprint-content-grid">
                <div class="task-distribution">
                  <h4><i class="fas fa-pie-chart"></i> Task Distribution</h4>
                  <div class="distribution-chart-container">
                    <canvas id="taskDistChart2"></canvas>
                  </div>
                </div>

                <div class="key-achievements">
                  <h4><i class="fas fa-trophy"></i> Key Achievements</h4>
                  <ul class="achievement-list">
                    <li class="achievement-item">
                      <span class="achievement-check"
                        ><i class="fas fa-check-circle"></i
                      ></span>
                      UI/UX wireframes finalized and approved
                    </li>
                    <li class="achievement-item">
                      <span class="achievement-check"
                        ><i class="fas fa-check-circle"></i
                      ></span>
                      Database architecture designed and documented
                    </li>
                    <li class="achievement-item">
                      <span class="achievement-check"
                        ><i class="fas fa-check-circle"></i
                      ></span>
                      System interactions mapped and validated
                    </li>
                    <li class="achievement-item">
                      <span class="achievement-check"
                        ><i class="fas fa-check-circle"></i
                      ></span>
                      Technology stack selected and justified
                    </li>
                  </ul>

                  <div class="sprint-documents">
                    <a href="#" class="document-link">
                      <i class="fas fa-file-pdf"></i> Design Documentation
                    </a>
                    <a href="#" class="document-link">
                      <i class="fas fa-file-alt"></i> Architecture Diagram
                    </a>
                  </div>
                </div>
              </div>

              <div class="sprint-health">
                <h4>
                  <i class="fas fa-heartbeat"></i> Sprint Health Dashboard
                </h4>
                <div class="health-metrics-container">
                  <div class="health-metric">
                    <div class="metric-gauge">
                      <svg viewBox="0 0 120 120" class="gauge">
                        <circle
                          class="gauge-bg"
                          cx="60"
                          cy="60"
                          r="50"
                        ></circle>
                        <circle
                          class="gauge-fill"
                          cx="60"
                          cy="60"
                          r="50"
                          style="stroke-dashoffset: 30"
                        ></circle>
                        <text
                          class="gauge-value"
                          x="60"
                          y="65"
                          text-anchor="middle"
                        >
                          80%
                        </text>
                      </svg>
                      <div class="gauge-label">Scope Adherence</div>
                    </div>
                  </div>

                  <div class="health-metric">
                    <div class="metric-gauge">
                      <svg viewBox="0 0 120 120" class="gauge">
                        <circle
                          class="gauge-bg"
                          cx="60"
                          cy="60"
                          r="50"
                        ></circle>
                        <circle
                          class="gauge-fill quality"
                          cx="60"
                          cy="60"
                          r="50"
                          style="stroke-dashoffset: 45"
                        ></circle>
                        <text
                          class="gauge-value"
                          x="60"
                          y="65"
                          text-anchor="middle"
                        >
                          95%
                        </text>
                      </svg>
                      <div class="gauge-label">Quality</div>
                    </div>
                  </div>

                  <div class="health-metric">
                    <div class="metric-gauge">
                      <svg viewBox="0 0 120 120" class="gauge">
                        <circle
                          class="gauge-bg"
                          cx="60"
                          cy="60"
                          r="50"
                        ></circle>
                        <circle
                          class="gauge-fill velocity"
                          cx="60"
                          cy="60"
                          r="50"
                          style="stroke-dashoffset: 45"
                        ></circle>
                        <text
                          class="gauge-value"
                          x="60"
                          y="65"
                          text-anchor="middle"
                        >
                          105%
                        </text>
                      </svg>
                      <div class="gauge-label">Velocity</div>
                    </div>
                  </div>

                  <div class="health-metric">
                    <div class="metric-gauge">
                      <svg viewBox="0 0 120 120" class="gauge">
                        <circle
                          class="gauge-bg"
                          cx="60"
                          cy="60"
                          r="50"
                        ></circle>
                        <circle
                          class="gauge-fill satisfaction"
                          cx="60"
                          cy="60"
                          r="50"
                          style="stroke-dashoffset: 40"
                        ></circle>
                        <text
                          class="gauge-value"
                          x="60"
                          y="65"
                          text-anchor="middle"
                        >
                          4.2
                        </text>
                      </svg>
                      <div class="gauge-label">Team Satisfaction</div>
                    </div>
                  </div>
                </div>

                <div class="sprint-insights">
                  <div class="insight-title">Key Insights</div>
                  <ul class="insights-list">
                    <li>
                      <span class="insight-badge positive"
                        ><i class="fas fa-arrow-up"></i
                      ></span>
                      Design reviews improved overall quality by 15%
                    </li>
                    <li>
                      <span class="insight-badge neutral"
                        ><i class="fas fa-minus"></i
                      ></span>
                      One task carried over to next sprint due to complexity
                    </li>
                    <li>
                      <span class="insight-badge positive"
                        ><i class="fas fa-check"></i
                      ></span>
                      Stakeholder feedback incorporated successfully
                    </li>
                    <li>
                      <span class="insight-badge negative"
                        ><i class="fas fa-arrow-down"></i
                      ></span>
                      Team satisfaction slightly lower due to design challenges
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div
              class="sprint-detail"
              id="sprint-3"
              role="tabpanel"
              id="sprint-3-panel"
            >
              <div class="sprint-header">
                <div class="sprint-title-section">
                  <h3>Sprint 3: Core Development</h3>
                  <div class="sprint-badges">
                    <div class="sprint-status completed">Completed</div>
                    <div class="sprint-timeframe">
                      <i class="far fa-calendar-alt"></i> Jan 29 - Feb 11, 2025
                    </div>
                  </div>
                </div>
                <div class="sprint-summary">
                  Backend API development, frontend implementation, and database
                  setup with 92% completion rate
                </div>
              </div>

              <div class="sprint-stats-row">
                <div class="sprint-stat" data-tilt data-tilt-scale="1.02">
                  <div class="stat-number counter" data-target="16">16/18</div>
                  <div class="stat-label">Tasks Completed</div>
                </div>
                <div class="sprint-stat" data-tilt data-tilt-scale="1.02">
                  <div class="stat-number counter" data-target="92">92%</div>
                  <div class="stat-label">Completion Rate</div>
                </div>
                <div class="sprint-stat" data-tilt data-tilt-scale="1.02">
                  <div class="stat-number counter" data-target="9">9</div>
                  <div class="stat-label">Issues Resolved</div>
                </div>
                <div
                  class="sprint-stat highlight-stat"
                  data-tilt
                  data-tilt-scale="1.02"
                >
                  <div class="stat-number">3</div>
                  <div class="stat-label">Critical Bugs Fixed</div>
                </div>
              </div>

              <div class="sprint-content-grid">
                <div class="task-distribution">
                  <h4><i class="fas fa-pie-chart"></i> Task Distribution</h4>
                  <div class="distribution-chart-container">
                    <canvas id="taskDistChart3"></canvas>
                  </div>
                </div>

                <div class="key-achievements">
                  <h4><i class="fas fa-trophy"></i> Key Achievements</h4>
                  <ul class="achievement-list">
                    <li class="achievement-item">
                      <span class="achievement-check"
                        ><i class="fas fa-check-circle"></i
                      ></span>
                      Core API endpoints implemented and tested
                    </li>
                    <li class="achievement-item">
                      <span class="achievement-check"
                        ><i class="fas fa-check-circle"></i
                      ></span>
                      Database schema implemented and populated with test data
                    </li>
                    <li class="achievement-item">
                      <span class="achievement-check"
                        ><i class="fas fa-check-circle"></i
                      ></span>
                      Frontend components developed for main features
                    </li>
                    <li class="achievement-item">
                      <span class="achievement-check"
                        ><i class="fas fa-check-circle"></i
                      ></span>
                      Authentication system implemented and secured
                    </li>
                  </ul>

                  <div class="sprint-documents">
                    <a href="#" class="document-link">
                      <i class="fas fa-file-pdf"></i> API Documentation
                    </a>
                    <a href="#" class="document-link">
                      <i class="fas fa-file-alt"></i> Test Coverage Report
                    </a>
                  </div>
                </div>
              </div>

              <div class="sprint-health">
                <h4>
                  <i class="fas fa-heartbeat"></i> Sprint Health Dashboard
                </h4>
                <div class="health-metrics-container">
                  <div class="health-metric">
                    <div class="metric-gauge">
                      <svg viewBox="0 0 120 120" class="gauge">
                        <circle
                          class="gauge-bg"
                          cx="60"
                          cy="60"
                          r="50"
                        ></circle>
                        <circle
                          class="gauge-fill"
                          cx="60"
                          cy="60"
                          r="50"
                          style="stroke-dashoffset: 35"
                        ></circle>
                        <text
                          class="gauge-value"
                          x="60"
                          y="65"
                          text-anchor="middle"
                        >
                          75%
                        </text>
                      </svg>
                      <div class="gauge-label">Scope Adherence</div>
                    </div>
                  </div>

                  <div class="health-metric">
                    <div class="metric-gauge">
                      <svg viewBox="0 0 120 120" class="gauge">
                        <circle
                          class="gauge-bg"
                          cx="60"
                          cy="60"
                          r="50"
                        ></circle>
                        <circle
                          class="gauge-fill quality"
                          cx="60"
                          cy="60"
                          r="50"
                          style="stroke-dashoffset: 55"
                        ></circle>
                        <text
                          class="gauge-value"
                          x="60"
                          y="65"
                          text-anchor="middle"
                        >
                          88%
                        </text>
                      </svg>
                      <div class="gauge-label">Quality</div>
                    </div>
                  </div>

                  <div class="health-metric">
                    <div class="metric-gauge">
                      <svg viewBox="0 0 120 120" class="gauge">
                        <circle
                          class="gauge-bg"
                          cx="60"
                          cy="60"
                          r="50"
                        ></circle>
                        <circle
                          class="gauge-fill velocity"
                          cx="60"
                          cy="60"
                          r="50"
                          style="stroke-dashoffset: 50"
                        ></circle>
                        <text
                          class="gauge-value"
                          x="60"
                          y="65"
                          text-anchor="middle"
                        >
                          100%
                        </text>
                      </svg>
                      <div class="gauge-label">Velocity</div>
                    </div>
                  </div>

                  <div class="health-metric">
                    <div class="metric-gauge">
                      <svg viewBox="0 0 120 120" class="gauge">
                        <circle
                          class="gauge-bg"
                          cx="60"
                          cy="60"
                          r="50"
                        ></circle>
                        <circle
                          class="gauge-fill satisfaction"
                          cx="60"
                          cy="60"
                          r="50"
                          style="stroke-dashoffset: 30"
                        ></circle>
                        <text
                          class="gauge-value"
                          x="60"
                          y="65"
                          text-anchor="middle"
                        >
                          4.0
                        </text>
                      </svg>
                      <div class="gauge-label">Team Satisfaction</div>
                    </div>
                  </div>
                </div>

                <div class="sprint-insights">
                  <div class="insight-title">Key Insights</div>
                  <ul class="insights-list">
                    <li>
                      <span class="insight-badge neutral"
                        ><i class="fas fa-minus"></i
                      ></span>
                      Team maintained consistent velocity despite challenges
                    </li>
                    <li>
                      <span class="insight-badge negative"
                        ><i class="fas fa-arrow-down"></i
                      ></span>
                      Scope creep required adjustments to sprint goals
                    </li>
                    <li>
                      <span class="insight-badge positive"
                        ><i class="fas fa-check"></i
                      ></span>
                      Test coverage exceeded target at 92%
                    </li>
                    <li>
                      <span class="insight-badge negative"
                        ><i class="fas fa-arrow-down"></i
                      ></span>
                      Integration issues required additional effort to resolve
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div
              class="sprint-detail"
              id="sprint-4"
              role="tabpanel"
              id="sprint-4-panel"
            >
              <div class="sprint-header">
                <div class="sprint-title-section">
                  <h3>Sprint 4: Integration Phase</h3>
                  <div class="sprint-badges">
                    <div class="sprint-status in-progress">In Progress</div>
                    <div class="sprint-timeframe">
                      <i class="far fa-calendar-alt"></i> Feb 12 - Feb 25, 2025
                    </div>
                  </div>
                </div>
                <div class="sprint-summary">
                  Connecting all components, hardware integration, and initial
                  testing with 65% completion rate
                </div>
              </div>

              <div class="sprint-stats-row">
                <div class="sprint-stat" data-tilt data-tilt-scale="1.02">
                  <div class="stat-number counter" data-target="10">10/16</div>
                  <div class="stat-label">Tasks Completed</div>
                </div>
                <div class="sprint-stat" data-tilt data-tilt-scale="1.02">
                  <div class="stat-number counter" data-target="65">65%</div>
                  <div class="stat-label">Completion Rate</div>
                </div>
                <div class="sprint-stat" data-tilt data-tilt-scale="1.02">
                  <div class="stat-number counter" data-target="6">6</div>
                  <div class="stat-label">Issues Resolved</div>
                </div>
                <div
                  class="sprint-stat highlight-stat"
                  data-tilt
                  data-tilt-scale="1.02"
                >
                  <div class="stat-number">8</div>
                  <div class="stat-label">Days Left</div>
                </div>
              </div>

              <div class="sprint-content-grid">
                <div class="task-distribution">
                  <h4><i class="fas fa-pie-chart"></i> Task Distribution</h4>
                  <div class="distribution-chart-container">
                    <canvas id="taskDistChart4"></canvas>
                  </div>
                </div>

                <div class="key-achievements">
                  <h4><i class="fas fa-trophy"></i> Key Achievements</h4>
                  <ul class="achievement-list">
                    <li class="achievement-item">
                      <span class="achievement-check"
                        ><i class="fas fa-check-circle"></i
                      ></span>
                      Frontend-backend integration completed
                    </li>
                    <li class="achievement-item">
                      <span class="achievement-check"
                        ><i class="fas fa-check-circle"></i
                      ></span>
                      Hardware sensors connected to main system
                    </li>
                    <li class="achievement-item">
                      <span class="achievement-check"
                        ><i class="fas fa-spinner fa-spin"></i
                      ></span>
                      End-to-end testing in progress
                    </li>
                    <li class="achievement-item">
                      <span class="achievement-check"
                        ><i class="fas fa-spinner fa-spin"></i
                      ></span>
                      Performance optimization ongoing
                    </li>
                  </ul>

                  <div class="sprint-documents">
                    <a href="#" class="document-link">
                      <i class="fas fa-file-pdf"></i> Integration Report
                    </a>
                    <a href="#" class="document-link">
                      <i class="fas fa-file-alt"></i> Testing Strategy
                    </a>
                  </div>
                </div>
              </div>

              <div class="sprint-health">
                <h4>
                  <i class="fas fa-heartbeat"></i> Sprint Health Dashboard
                </h4>
                <div class="health-metrics-container">
                  <div class="health-metric">
                    <div class="metric-gauge">
                      <svg viewBox="0 0 120 120" class="gauge">
                        <circle
                          class="gauge-bg"
                          cx="60"
                          cy="60"
                          r="50"
                        ></circle>
                        <circle
                          class="gauge-fill"
                          cx="60"
                          cy="60"
                          r="50"
                          style="stroke-dashoffset: 40"
                        ></circle>
                        <text
                          class="gauge-value"
                          x="60"
                          y="65"
                          text-anchor="middle"
                        >
                          70%
                        </text>
                      </svg>
                      <div class="gauge-label">Scope Adherence</div>
                    </div>
                  </div>

                  <div class="health-metric">
                    <div class="metric-gauge">
                      <svg viewBox="0 0 120 120" class="gauge">
                        <circle
                          class="gauge-bg"
                          cx="60"
                          cy="60"
                          r="50"
                        ></circle>
                        <circle
                          class="gauge-fill quality"
                          cx="60"
                          cy="60"
                          r="50"
                          style="stroke-dashoffset: 60"
                        ></circle>
                        <text
                          class="gauge-value"
                          x="60"
                          y="65"
                          text-anchor="middle"
                        >
                          85%
                        </text>
                      </svg>
                      <div class="gauge-label">Quality</div>
                    </div>
                  </div>

                  <div class="health-metric">
                    <div class="metric-gauge">
                      <svg viewBox="0 0 120 120" class="gauge">
                        <circle
                          class="gauge-bg"
                          cx="60"
                          cy="60"
                          r="50"
                        ></circle>
                        <circle
                          class="gauge-fill velocity"
                          cx="60"
                          cy="60"
                          r="50"
                          style="stroke-dashoffset: 55"
                        ></circle>
                        <text
                          class="gauge-value"
                          x="60"
                          y="65"
                          text-anchor="middle"
                        >
                          95%
                        </text>
                      </svg>
                      <div class="gauge-label">Velocity</div>
                    </div>
                  </div>

                  <div class="health-metric">
                    <div class="metric-gauge">
                      <svg viewBox="0 0 120 120" class="gauge">
                        <circle
                          class="gauge-bg"
                          cx="60"
                          cy="60"
                          r="50"
                        ></circle>
                        <circle
                          class="gauge-fill satisfaction"
                          cx="60"
                          cy="60"
                          r="50"
                          style="stroke-dashoffset: 25"
                        ></circle>
                        <text
                          class="gauge-value"
                          x="60"
                          y="65"
                          text-anchor="middle"
                        >
                          4.3
                        </text>
                      </svg>
                      <div class="gauge-label">Team Satisfaction</div>
                    </div>
                  </div>
                </div>

                <div class="sprint-insights">
                  <div class="insight-title">Key Insights</div>
                  <ul class="insights-list">
                    <li>
                      <span class="insight-badge positive"
                        ><i class="fas fa-arrow-up"></i
                      ></span>
                      Integration proceeding faster than anticipated
                    </li>
                    <li>
                      <span class="insight-badge neutral"
                        ><i class="fas fa-minus"></i
                      ></span>
                      Hardware compatibility issues being addressed
                    </li>
                    <li>
                      <span class="insight-badge positive"
                        ><i class="fas fa-check"></i
                      ></span>
                      Team morale improved with visible progress
                    </li>
                    <li>
                      <span class="insight-badge negative"
                        ><i class="fas fa-arrow-down"></i
                      ></span>
                      Performance testing revealed optimization needs
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- More sprint details... -->
          </div>
        </div>
      </section>

      <!-- Documentation Section -->
      <section id="documentation" class="documentation-section">
        <h2 class="section-title animate-in">Documentation</h2>
        <p class="doc-intro animate-in animate-delay-1">
          Access comprehensive documentation about our project, including
          technical specifications, user guides, and research papers.
        </p>

        <div class="doc-cards">
          <!-- Technical Spec -->
          <div class="doc-card animate-in animate-delay-1">
            <div class="doc-icon">
              <i class="fas fa-file-code"></i>
            </div>
            <h3 class="doc-title">Technical Specification</h3>
            <p class="doc-desc">
              Detailed system architecture, API documentation, and
              implementation details.
            </p>
            <a href="#" class="doc-link"
              >View Document <i class="fas fa-chevron-right"></i
            ></a>
          </div>

          <!-- User Guide -->
          <div class="doc-card animate-in animate-delay-2">
            <div class="doc-icon">
              <i class="fas fa-book-open"></i>
            </div>
            <h3 class="doc-title">User Guide</h3>
            <p class="doc-desc">
              Step-by-step instructions for using the application and its
              features.
            </p>
            <a href="#" class="doc-link"
              >View Document <i class="fas fa-chevron-right"></i
            ></a>
          </div>

          <!-- Research Paper -->
          <div class="doc-card animate-in animate-delay-3">
            <div class="doc-icon">
              <i class="fas fa-microscope"></i>
            </div>
            <h3 class="doc-title">Research Paper</h3>
            <p class="doc-desc">
              Academic publication detailing our methodology and findings.
            </p>
            <a href="#" class="doc-link"
              >View Document <i class="fas fa-chevron-right"></i
            ></a>
          </div>

          <!-- Presentation -->
          <div class="doc-card animate-in animate-delay-4">
            <div class="doc-icon">
              <i class="fas fa-desktop"></i>
            </div>
            <h3 class="doc-title">Presentation Slides</h3>
            <p class="doc-desc">
              Project demonstration slides and visual materials.
            </p>
            <a href="#" class="doc-link"
              >View Document <i class="fas fa-chevron-right"></i
            ></a>
          </div>
        </div>
      </section>

      <section id="gallery" class="gallery-section">
        <h2 class="section-title">Project Gallery</h2>
        <p class="gallery-intro">
          See our smart city solution in action through these visual highlights.
        </p>

        <div class="gallery-tabs">
          <div class="gallery-tab active" data-gallery="all">All</div>
          <div class="gallery-tab" data-gallery="interface">Interface</div>
          <div class="gallery-tab" data-gallery="hardware">Hardware</div>
          <div class="gallery-tab" data-gallery="analytics">Analytics</div>
        </div>

        <div class="gallery-grid">
          <div class="gallery-item interface" data-category="interface">
            <div class="gallery-img-container">
              <img
                src="https://via.placeholder.com/600x400?text=Dashboard+Interface"
                alt="Dashboard Interface"
              />
              <div class="gallery-overlay">
                <div class="gallery-actions">
                  <span class="gallery-zoom"
                    ><i class="fas fa-search-plus"></i
                  ></span>
                  <span class="gallery-title">Dashboard Interface</span>
                </div>
              </div>
            </div>
          </div>
          <div class="gallery-item hardware" data-category="hardware">
            <div class="gallery-img-container">
              <img
                src="https://via.placeholder.com/600x400?text=IoT+Device+Prototype"
                alt="IoT Device Prototype"
              />
              <div class="gallery-overlay">
                <div class="gallery-actions">
                  <span class="gallery-zoom"
                    ><i class="fas fa-search-plus"></i
                  ></span>
                  <span class="gallery-title">IoT Device Prototype</span>
                </div>
              </div>
            </div>
          </div>
          <div class="gallery-item analytics" data-category="analytics">
            <div class="gallery-img-container">
              <img
                src="https://via.placeholder.com/600x400?text=Data+Visualization"
                alt="Data Visualization"
              />
              <div class="gallery-overlay">
                <div class="gallery-actions">
                  <span class="gallery-zoom"
                    ><i class="fas fa-search-plus"></i
                  ></span>
                  <span class="gallery-title">Data Visualization</span>
                </div>
              </div>
            </div>
          </div>
          <div class="gallery-item interface" data-category="interface">
            <div class="gallery-img-container">
              <img
                src="https://via.placeholder.com/600x400?text=Mobile+App+Screens"
                alt="Mobile App Screens"
              />
              <div class="gallery-overlay">
                <div class="gallery-actions">
                  <span class="gallery-zoom"
                    ><i class="fas fa-search-plus"></i
                  ></span>
                  <span class="gallery-title">Mobile App Screens</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section id="testimonials" class="testimonials-section">
        <div class="container">
          <div class="testimonials-header">
            <h2 class="section-title animate-in">What People Say</h2>
            <p class="testimonials-intro animate-in animate-delay-1">
              Feedback from professors, mentors, and industry professionals
              who've reviewed our project.
            </p>
          </div>

          <div class="testimonial-filters animate-in animate-delay-1">
            <button class="testimonial-filter active" data-filter="all">
              All Feedback
            </button>
            <button class="testimonial-filter" data-filter="professor">
              Professors
            </button>
            <button class="testimonial-filter" data-filter="industry">
              Industry
            </button>
            <button class="testimonial-filter" data-filter="mentor">
              Mentors
            </button>
          </div>

          <div class="testimonials-slider">
            <div
              class="testimonial-item animate-in animate-delay-2"
              data-category="professor"
            >
              <div class="testimonial-content">
                <div class="quote"><i class="fas fa-quote-left"></i></div>
                <div class="rating">
                  <i class="fas fa-star"></i><i class="fas fa-star"></i
                  ><i class="fas fa-star"></i><i class="fas fa-star"></i
                  ><i class="fas fa-star"></i>
                </div>
                <p>
                  This team has demonstrated exceptional problem-solving skills
                  and technical expertise in developing their smart city
                  solution.
                </p>
                <div class="testimonial-author">
                  <div class="author-img"><i class="fas fa-user-tie"></i></div>
                  <div class="author-info">
                    <h4>Dr. Sarah Johnson</h4>
                    <span>Faculty Advisor</span>
                  </div>
                </div>
              </div>
            </div>
            <!-- More testimonials will be added via JavaScript -->
          </div>

          <div class="testimonial-navigation">
            <div class="testimonial-arrows">
              <div class="arrow-btn prev-testimonial">
                <i class="fas fa-chevron-left"></i>
              </div>
              <div class="arrow-btn next-testimonial">
                <i class="fas fa-chevron-right"></i>
              </div>
            </div>
            <!-- Nav dots will be added by JS -->
          </div>

          <div class="testimonial-cta animate-in animate-delay-3">
            <p>Want to provide your feedback about our project?</p>
            <a href="#contact" class="btn btn-outline btn-sm"
              >Share Your Thoughts</a
            >
          </div>
        </div>
      </section>

      <section id="contact" class="contact-section">
        <div class="container">
          <h2 class="section-title">Get In Touch</h2>
          <p class="contact-intro">
            Have questions about our project? Feel free to reach out to our
            team.
          </p>

          <div class="contact-container">
            <div class="contact-info">
              <div class="contact-card">
                <div class="contact-icon">
                  <i class="fas fa-map-marker-alt"></i>
                </div>
                <h3>Location</h3>
                <p>University Campus, Building 4, <br />Innovation Lab</p>
              </div>

              <div class="contact-card">
                <div class="contact-icon">
                  <i class="fas fa-envelope"></i>
                </div>
                <h3>Email</h3>
                <p><EMAIL></p>
                <p><EMAIL></p>
              </div>

              <div class="contact-card">
                <div class="contact-icon">
                  <i class="fas fa-phone-alt"></i>
                </div>
                <h3>Phone</h3>
                <p>+****************</p>
                <p>+****************</p>
              </div>
            </div>

            <div class="contact-form-container">
              <form
                class="contact-form"
                id="contactForm"
                action="https://formspree.io/f/xrbpdbkk"
                method="POST"
                data-ajax="true"
              >
                <div class="form-group">
                  <label for="name">Name</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    placeholder="Your Name"
                  />
                </div>

                <div class="form-group">
                  <label for="email">Email</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    placeholder="Your Email"
                  />
                </div>

                <div class="form-group">
                  <label for="subject">Subject</label>
                  <input
                    type="text"
                    id="subject"
                    name="subject"
                    placeholder="Subject"
                  />
                </div>

                <div class="form-group">
                  <label for="message">Message</label>
                  <textarea
                    id="message"
                    rows="6"
                    name="message"
                    placeholder="Your Message"
                  ></textarea>
                </div>

                <button type="submit" class="submit-btn">
                  <span>Send Message</span>
                  <i class="fas fa-paper-plane"></i>
                </button>
              </form>
            </div>
          </div>
        </div>
      </section>
    </div>

    <footer>
      <div class="container">
        <div class="footer-content">
          <div class="footer-column">
            <h3>About Project</h3>
            <p>
              Our graduation project represents months of collaborative work and
              dedication to create an innovative solution for smart city
              management.
            </p>
          </div>
          <div class="footer-column">
            <h3>Quick Links</h3>
            <div class="footer-links">
              <a href="#overview"
                ><i class="fas fa-angle-right"></i> Project Overview</a
              >
              <a href="#team"
                ><i class="fas fa-angle-right"></i> Team Members</a
              >
              <a href="#sprints"
                ><i class="fas fa-angle-right"></i> Sprint Progress</a
              >
              <a href="#documentation"
                ><i class="fas fa-angle-right"></i> Documentation</a
              >
            </div>
          </div>
          <div class="footer-column">
            <h3>Contact</h3>
            <div class="footer-links">
              <a href="mailto:<EMAIL>"
                ><i class="fas fa-envelope"></i> <EMAIL></a
              >
              <a href="#"><i class="fas fa-phone"></i> +****************</a>
              <a href="#"
                ><i class="fas fa-map-marker-alt"></i> University Campus,
                Building 4</a
              >
            </div>
          </div>
        </div>
        <div class="copyright">
          &copy; 2025 Graduation Project Team. All Rights Reserved.
        </div>
      </div>
    </footer>

    <!-- JavaScript Files -->
    <script src="js/modals.js"></script>
    <script src="js/teams.js"></script>
    <script src="js/theme.js"></script>
    <script src="js/gallery.js"></script>

    <!-- External Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanilla-tilt@1.8.0/dist/vanilla-tilt.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.11.4/dist/gsap.min.js"></script>

    <!-- Include all module scripts first -->
    <script src="js/utils/debounce.js"></script>
    <script src="js/components/toc.js"></script>
    <script src="js/components/animations.js"></script>
    <script src="js/components/search.js"></script>
    <script src="js/components/progress-enhanced.js"></script>
    <script src="js/components/roadmap.js"></script>
    <script src="js/components/roadmap-enhanced.js"></script>
    <script src="js/components/roadmap-particles.js"></script>
    <script src="js/components/contact-form.js"></script>
    <script src="js/components/testimonials.js"></script>
    <script src="js/components/counters.js"></script>
    <script src="js/components/sprint-progress.js"></script>
    <script src="js/components/sprint-enhanced.js"></script>
    <script src="js/components/overview-metrics.js"></script>
    <script src="js/services/performance.js"></script>
    <script src="js/services/accessibility.js"></script>
    <script src="js/services/service-worker.js"></script>

    <!-- Main entry point -->
    <script src="js/main.js"></script>
    <script src="js/particles-config.js"></script>
    <script src="js/hero-animations.js"></script>
  </body>
</html>
