function enhanceAccessibility() {
    // Improve focus visibility
    const style = document.createElement("style");
    style.textContent = `
    :focus {
      outline: 3px solid var(--primary);
      outline-offset: 3px;
    }
  `;
    document.head.appendChild(style);


    // Add appropriate ARIA roles to sections
    const sections = document.querySelectorAll("section");
    sections.forEach((section) => {
        section.setAttribute("role", "region");

        const heading = section.querySelector("h2");
        if (heading) {
            section.setAttribute(
                "aria-labelledby",
                heading.id || `heading-${section.id}`
            );
            if (!heading.id) {
                heading.id = `heading-${section.id}`;
            }
        }
    });

    // Announce theme changes for screen readers
    const themeToggle = document.getElementById("themeToggle");
    if (themeToggle) {
        const srAnnouncer = document.createElement("div");
        srAnnouncer.className = "sr-only";
        srAnnouncer.setAttribute("aria-live", "polite");
        document.body.appendChild(srAnnouncer);

        const originalClickHandler = themeToggle.onclick;
        themeToggle.onclick = function (e) {
            const currentTheme =
                document.documentElement.getAttribute("data-theme") || "light";
            const newTheme = currentTheme === "light" ? "dark" : "light";

            srAnnouncer.textContent = `Theme changed to ${newTheme} mode`;

            if (originalClickHandler) {
                originalClickHandler.call(this, e);
            }
        };
    }
}
