/* Team Members Styles - Enhanced with modern UI effects */
.teams-container {
  padding: 60px 0 90px;
  position: relative;
  overflow: hidden;
}

/* Animated background elements */
.team-bg-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(
    45deg,
    rgba(67, 97, 238, 0.05),
    rgba(76, 201, 240, 0.05)
  );
  filter: blur(60px);
  z-index: 0;
}

.team-bg-circle-1 {
  width: 500px;
  height: 500px;
  top: -100px;
  right: -200px;
  animation: float-slow 15s infinite alternate ease-in-out;
}

.team-bg-circle-2 {
  width: 300px;
  height: 300px;
  bottom: 100px;
  left: -100px;
  animation: float-slow 10s infinite alternate-reverse ease-in-out;
}

@keyframes float-slow {
  0% {
    transform: translate(0, 0) rotate(0deg);
  }
  100% {
    transform: translate(30px, 30px) rotate(10deg);
  }
}

.team-intro {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 50px;
  position: relative;
  z-index: 2;
}

.team-intro h3 {
  font-size: 2.2rem;
  margin-bottom: 20px;
  color: var(--primary-dark);
  position: relative;
  display: inline-block;
}

.team-intro h3::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary), var(--primary-light));
  border-radius: 3px;
}

.team-intro p {
  color: var(--text-light);
  line-height: 1.7;
  font-size: 1.1rem;
}

/* Enhanced team tabs navigation */
.team-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 50px;
  justify-content: center;
  position: relative;
  z-index: 2;
}

.team-tabs::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 5px;
  border-radius: 10px;
  background-color: rgba(0, 0, 0, 0.05);
}

.team-tab {
  padding: 14px 22px;
  background-color: var(--bg-white);
  border-radius: 30px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.06);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
}

.team-tab::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(67, 97, 238, 0.1) 0%,
    rgba(76, 201, 240, 0.1) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.team-tab:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(67, 97, 238, 0.2);
}

.team-tab:hover::before {
  opacity: 1;
}

.team-tab i {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.team-tab:hover i {
  transform: translateY(-2px);
}

.team-tab.active {
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transform: translateY(-5px);
  box-shadow: 0 12px 20px rgba(67, 97, 238, 0.3);
}

.team-tab.active i {
  animation: pulse-icon 1s ease-in-out infinite alternate;
}

@keyframes pulse-icon {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.2);
  }
}

.team-content {
  display: none;
  animation: fadeIn 0.8s cubic-bezier(0.16, 1, 0.3, 1);
  position: relative;
  z-index: 2;
}

.team-content.active {
  display: block;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Staggered animation for team members */
.team-members {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 30px;
  margin-bottom: 50px;
}

.member-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  position: relative;
  transform-style: preserve-3d;
  perspective: 1000px;
  border: 1px solid rgba(255, 255, 255, 0.7);
  transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  opacity: 0;
  transform: translateY(30px);
  animation: appear 0.6s forwards cubic-bezier(0.16, 1, 0.3, 1);
  animation-delay: calc(var(--card-index, 0) * 0.1s);
}

@keyframes appear {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.member-card::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-shadow: 0 20px 40px rgba(67, 97, 238, 0.2);
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: -1;
}

.member-card:hover {
  transform: translateY(-15px) rotateY(5deg);
  box-shadow: 0 30px 50px rgba(67, 97, 238, 0.3);
  border-color: var(--primary-light);
}

.member-card:hover::after {
  opacity: 1;
}

.member-img-container {
  height: 240px;
  overflow: hidden;
  position: relative;
  clip-path: polygon(0 0, 100% 0, 100% 85%, 0 100%);
  background: linear-gradient(
    135deg,
    var(--primary-light) 0%,
    var(--primary) 100%
  );
}

.member-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.7s ease;
  background: linear-gradient(45deg, #e3f2fd, #bbdefb);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary);
  font-size: 4rem;
}

.member-card:hover .member-img {
  transform: translateZ(30px) scale(1.1);
  filter: brightness(1.05);
}

.view-profile {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(67, 97, 238, 0.9), transparent);
  color: white;
  padding: 40px 20px 20px;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.view-profile i {
  margin-right: 8px;
  font-size: 1.1rem;
}

.member-card:hover .view-profile {
  opacity: 1;
  transform: translateY(0);
}

.member-info {
  padding: 25px;
  text-align: center;
  transition: transform 0.5s ease;
  position: relative;
}

.member-card:hover .member-info {
  transform: translateZ(20px);
}

.member-name {
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--text-dark);
  transition: color 0.3s ease;
}

.member-card:hover .member-name {
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.member-role {
  display: inline-block;
  color: var(--primary-light);
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 15px;
  padding: 6px 16px;
  background-color: rgba(67, 97, 238, 0.1);
  border-radius: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 3px 10px rgba(67, 97, 238, 0.1);
}

.member-card:hover .member-role {
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  color: white;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
}

.member-social {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 20px;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.5s ease;
  transition-delay: 0.1s;
}

.member-card:hover .member-social {
  opacity: 1;
  transform: translateY(0);
}

.social-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--bg-light);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-dark);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.social-icon:hover {
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  color: white;
  transform: translateY(-5px) scale(1.1);
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
}

/* Redesigned team tech sections */
.team-tech {
  margin-top: 60px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
  padding: 30px;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.7);
  position: relative;
  overflow: hidden;
}

.team-tech::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 6px;
  height: 100%;
  background: linear-gradient(to bottom, var(--primary), var(--primary-light));
  border-radius: 3px;
}

.tech-title {
  font-size: 1.5rem;
  margin-bottom: 25px;
  color: var(--primary-dark);
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
}

.tech-title i {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
}

.tech-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.tech-item {
  padding: 10px 18px;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 0.5) 100%
  );
  border-radius: 30px;
  color: var(--text-dark);
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1rem;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  border: 1px solid rgba(255, 255, 255, 0.7);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.tech-item:hover {
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  color: white;
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(67, 97, 238, 0.2);
  border-color: transparent;
}

.tech-item i {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.tech-item:hover i {
  transform: rotate(360deg);
}

/* Enhanced filter buttons */
.member-filters {
  margin: 30px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.filter-label {
  margin-bottom: 5px;
  font-weight: 600;
  color: var(--text-dark);
  position: relative;
  display: inline-block;
  font-size: 1.1rem;
}

.filter-label::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 2px;
  background: linear-gradient(90deg, var(--primary), var(--primary-light));
  border-radius: 2px;
}

.filter-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
}

.filter-btn {
  padding: 10px 18px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 30px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  font-size: 0.95rem;
  font-weight: 500;
  color: var(--text-dark);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.filter-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(67, 97, 238, 0.1) 0%,
    rgba(76, 201, 240, 0.1) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 0;
}

.filter-btn:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.filter-btn:hover::before {
  opacity: 1;
}

.filter-btn.active {
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  color: white;
  border-color: transparent;
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(67, 97, 238, 0.3);
}

.filter-counter {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  width: 22px;
  height: 22px;
  font-size: 0.75rem;
  font-weight: 600;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.filter-btn.active .filter-counter {
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

/* Skill meters with enhanced styling */
.skill-meters {
  display: flex;
  flex-direction: column;
  gap: 18px;
  margin-top: 25px;
}

.skill-meter {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.skill-label {
  font-weight: 600;
  color: var(--text-dark);
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1rem;
}

.skill-label::before {
  content: "\f058";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  color: var(--primary);
  font-size: 0.9rem;
}

.meter-bar {
  height: 12px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.meter-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary), var(--primary-light));
  border-radius: 10px;
  position: relative;
  animation: fillAnimation 1.8s ease-out forwards;
  transform-origin: left;
  transform: scaleX(0);
  box-shadow: 0 0 10px rgba(67, 97, 238, 0.3);
}

.meter-fill::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shine 2s infinite;
  transform: skewX(-20deg);
}

@keyframes shine {
  0% {
    transform: translateX(-100%) skewX(-20deg);
  }
  100% {
    transform: translateX(200%) skewX(-20deg);
  }
}

.meter-fill span {
  position: absolute;
  right: 0;
  top: -25px;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--primary);
  background: white;
  padding: 3px 8px;
  border-radius: 10px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  opacity: 0;
  transform: translateY(10px);
  animation: showPercent 0.5s forwards 1.5s;
}

@keyframes showPercent {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fillAnimation {
  to {
    transform: scaleX(1);
  }
}

/* Team member search */
.team-search {
  position: relative;
  max-width: 500px;
  margin: 0 auto 30px;
}

.team-search input {
  width: 100%;
  padding: 15px 20px 15px 50px;
  border: none;
  border-radius: 30px;
  background: white;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  font-size: 1rem;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.team-search input:focus {
  outline: none;
  box-shadow: 0 8px 25px rgba(67, 97, 238, 0.2);
  border-color: rgba(67, 97, 238, 0.3);
}

.team-search i {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary);
  font-size: 1.2rem;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .team-tabs {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
  }

  .team-tab {
    width: auto;
    margin: 5px;
  }

  .team-members {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }
}

@media (max-width: 768px) {
  .team-tech {
    padding: 25px;
  }

  .tech-title {
    font-size: 1.3rem;
  }

  .tech-list {
    gap: 10px;
  }

  .tech-item {
    padding: 8px 15px;
    font-size: 0.9rem;
  }

  .member-card:hover {
    transform: translateY(-10px);
  }
}

@media (max-width: 576px) {
  .team-intro h3 {
    font-size: 1.8rem;
  }

  .team-tab {
    padding: 10px 16px;
    font-size: 0.9rem;
  }

  .member-name {
    font-size: 1.2rem;
  }

  .member-role {
    font-size: 0.9rem;
  }

  .team-members {
    grid-template-columns: 1fr;
    max-width: 320px;
    margin-left: auto;
    margin-right: auto;
  }
}
