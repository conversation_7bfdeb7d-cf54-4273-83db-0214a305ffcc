/**
 * Theme Toggle Functionality
 * Handles switching between light and dark mode
 */

document.addEventListener("DOMContentLoaded", function () {
  const themeToggle = document.getElementById("themeToggle");
  const body = document.body;

  // Check for saved theme preference
  const savedTheme = localStorage.getItem("theme") || "light";

  // Apply saved theme on initial load
  if (savedTheme === "dark") {
    body.classList.add("dark-mode");
  }

  // Toggle theme on click
  if (themeToggle) {
    themeToggle.addEventListener("click", function () {
      // Add transition class for smooth changes
      body.classList.add("theme-transition");

      // Add active class for animation
      themeToggle.classList.add("active");

      // Toggle dark-mode class on body
      body.classList.toggle("dark-mode");

      // Save preference to localStorage
      const isDarkMode = body.classList.contains("dark-mode");
      localStorage.setItem("theme", isDarkMode ? "dark" : "light");

      // Remove transition class after transitions complete
      setTimeout(() => {
        body.classList.remove("theme-transition");
        themeToggle.classList.remove("active");
      }, 600);

      // Announce theme change for screen readers
      const srAnnouncement = document.createElement("div");
      srAnnouncement.setAttribute("aria-live", "polite");
      srAnnouncement.classList.add("sr-only");
      srAnnouncement.textContent = `Theme changed to ${
        isDarkMode ? "dark" : "light"
      } mode`;
      document.body.appendChild(srAnnouncement);

      // Remove the announcement after it's been read
      setTimeout(() => {
        if (srAnnouncement.parentNode) {
          srAnnouncement.parentNode.removeChild(srAnnouncement);
        }
      }, 1000);
    });
  }

  // Apply system preference if no saved preference exists
  if (!localStorage.getItem("theme")) {
    const prefersDarkMode = window.matchMedia(
      "(prefers-color-scheme: dark)"
    ).matches;
    if (prefersDarkMode) {
      body.classList.add("dark-mode");
    }
  }

  // Listen for system theme changes
  window
    .matchMedia("(prefers-color-scheme: dark)")
    .addEventListener("change", (e) => {
      if (!localStorage.getItem("theme")) {
        if (e.matches) {
          body.classList.add("dark-mode");
        } else {
          body.classList.remove("dark-mode");
        }
      }
    });
});
