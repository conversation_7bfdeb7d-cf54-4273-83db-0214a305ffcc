/**
 * Contact Form Handler
 * Provides form validation, submission, and feedback functionality
 */

// Main function to setup the contact form
function setupContactForm() {
  const contactForm = document.getElementById("contactForm");
  if (!contactForm) return;

  // Initialize form with validation
  setupFormValidationListeners(contactForm);
  // Add custom styles for the form
  injectFormValidationStyles();
}

/**
 * Initialize form validation and submission handling
 * @param {HTMLFormElement} form - The contact form element
 */
function setupFormValidationListeners(form) {
  form.addEventListener("submit", function (e) {
    e.preventDefault();

    if (validateForm(form)) {
      submitFormAsync(form);
    }
  });
}

/**
 * Validate all form fields
 * @param {HTMLFormElement} form - The form to validate
 * @returns {boolean} - Whether the form is valid
 */
function validateForm(form) {
  // Get form fields
  const name = document.getElementById("name");
  const email = document.getElementById("email");
  const subject = document.getElementById("subject");
  const message = document.getElementById("message");

  // Reset previous validation errors
  clearValidationErrors(form);

  let isValid = true;

  // Validate required fields
  if (!name.value.trim()) {
    displayFieldError(name, "Please enter your name");
    isValid = false;
  }

  if (!email.value.trim()) {
    displayFieldError(email, "Please enter your email address");
    isValid = false;
  } else if (!validateEmail(email.value)) {
    displayFieldError(email, "Please enter a valid email address");
    isValid = false;
  }

  // Add validation for subject field
  if (!subject.value.trim()) {
    displayFieldError(subject, "Please enter a subject");
    isValid = false;
  }

  if (!message.value.trim()) {
    displayFieldError(message, "Please enter your message");
    isValid = false;
  }

  return isValid;
}

/**
 * Clear all validation error messages
 * @param {HTMLFormElement} form - The form to clear errors from
 */
function clearValidationErrors(form) {
  const errorElements = form.querySelectorAll(".error-message");
  errorElements.forEach((el) => el.remove());

  const errorFields = form.querySelectorAll(".error");
  errorFields.forEach((field) => field.classList.remove("error"));
}

/**
 * Add error message to a form field
 * @param {HTMLElement} field - The field with error
 * @param {string} message - The error message
 */
function displayFieldError(field, message) {
  const error = document.createElement("div");
  error.className = "error-message";
  error.textContent = message;
  field.parentNode.appendChild(error);
  field.classList.add("error");

  // Remove error when field is focused
  field.addEventListener("focus", function () {
    field.classList.remove("error");
    const errorEl = field.parentNode.querySelector(".error-message");
    if (errorEl) errorEl.remove();
  });
}

/**
 * Validate email format
 * @param {string} email - Email to validate
 * @returns {boolean} - Whether email is valid
 */
function validateEmail(email) {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(String(email).toLowerCase());
}

/**
 * Submit the form via AJAX
 * @param {HTMLFormElement} form - The form to submit
 */
function submitFormAsync(form) {
  const formData = new FormData(form);

  // Show loading indication
  displayLoadingIndicator();

  fetch(form.action, {
    method: "POST",
    body: formData,
    headers: {
      Accept: "application/json",
    },
  })
    .then(processFormSubmissionResponse)
    .then(() => {
      processFormSuccess(form);
    })
    .catch((error) => {
      displayFormError(error);
    });
}

/**
 * Handle the fetch response
 * @param {Response} response - The fetch response
 * @returns {Promise} - JSON response or error
 */
function processFormSubmissionResponse(response) {
  if (response.ok) {
    return response.json();
  } else {
    throw new Error(
      `Server responded with ${response.status}: ${response.statusText}`
    );
  }
}

/**
 * Show loading indicator using SweetAlert
 */
function displayLoadingIndicator() {
  Swal.fire({
    title: "Sending...",
    text: "Please wait while we send your message",
    allowOutsideClick: false,
    didOpen: () => {
      Swal.showLoading();
    },
  });
}

/**
 * Handle successful form submission
 * @param {HTMLFormElement} form - The submitted form
 */
function processFormSuccess(form) {
  // Show success message using SweetAlert
  Swal.fire({
    title: "Message Sent!",
    text: "Thank you for contacting us. We'll get back to you soon.",
    icon: "success",
    confirmButtonText: "Great!",
    confirmButtonColor: "var(--primary)",
  });

  // Reset form
  form.reset();

  // Replace form with success message
  hideFormFieldsAndButtons(form);
  displayFormSuccessMessage(form);
}

/**
 * Handle form submission error
 * @param {Error} error - The error that occurred
 */
function displayFormError(error) {
  Swal.fire({
    title: "Oops!",
    text: "Something went wrong. Please try again later.",
    icon: "error",
    confirmButtonText: "OK",
    confirmButtonColor: "var(--primary)",
  });
  console.error("Form submission error:", error);
}

/**
 * Hide form elements after successful submission
 * @param {HTMLFormElement} form - The form to hide elements in
 */
function hideFormFieldsAndButtons(form) {
  const formGroups = form.querySelectorAll(".form-group");
  formGroups.forEach((group) => {
    group.style.display = "none";
  });

  const submitBtn = form.querySelector(".submit-btn");
  if (submitBtn) {
    submitBtn.style.display = "none";
  }
}

/**
 * Show success message in the form
 * @param {HTMLFormElement} form - The form to show message in
 */
function displayFormSuccessMessage(form) {
  const successMessage = document.createElement("div");
  successMessage.className = "success-message";

  successMessage.innerHTML = `
    <div class="success-icon"><i class="fas fa-check-circle"></i></div>
    <h3>Message Sent!</h3>
    <p>Thank you for contacting us. We'll get back to you soon.</p>
  `;

  form.appendChild(successMessage);

  // Force reflow to ensure the transition works
  successMessage.offsetHeight;

  // Add the show class to trigger the transition
  successMessage.classList.add("show");
}

/**
 * Add styles for form validation and success message
 */
function injectFormValidationStyles() {
  const style = document.createElement("style");
  style.textContent = `
    .form-group {
      position: relative;
      margin-bottom: 25px;
    }
    
    .error-message {
      color: #e74c3c;
      font-size: 0.85rem;
      margin-top: 5px;
      animation: fadeIn 0.3s ease;
    }
    
    input.error, textarea.error {
      border-color: #e74c3c;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(-10px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    .success-message {
      text-align: center;
      padding: 30px 20px;
      opacity: 0;
      transition: opacity 0.5s ease;
    }
    
    .success-message.show {
      opacity: 1;
    }
    
    .success-icon {
      font-size: 50px;
      color: #28a745;
      margin-bottom: 15px;
    }
    
    .success-message h3 {
      margin-bottom: 10px;
      color: #333;
    }
    
    .success-message p {
      color: #666;
    }
  `;
  document.head.appendChild(style);
}

/**
 * Load SweetAlert library if not already available
 * @returns {Promise} - Resolves when SweetAlert is loaded
 */
function loadSweetAlertLibrary() {
  return new Promise((resolve) => {
    if (typeof Swal !== "undefined") {
      resolve();
      return;
    }

    const script = document.createElement("script");
    script.src = "https://cdn.jsdelivr.net/npm/sweetalert2@11";
    script.onload = resolve;
    script.onerror = () => {
      console.error("Failed to load SweetAlert library");
      resolve(); // Resolve anyway to prevent blocking
    };
    document.head.appendChild(script);
  });
}

// Initialize the form when DOM is loaded
document.addEventListener("DOMContentLoaded", async function () {
  try {
    await loadSweetAlertLibrary();
    setupContactForm();
  } catch (error) {
    console.error("Failed to initialize contact form:", error);
  }
});
