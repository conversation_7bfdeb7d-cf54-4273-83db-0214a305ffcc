/**
 * Enhanced Roadmap Functionality
 * Adds interactive features and animations to the project roadmap
 */

document.addEventListener("DOMContentLoaded", function () {
  initEnhancedRoadmap();
});

// Load GSAP library if not already available
function loadGSAPLibrary() {
  return new Promise((resolve, reject) => {
    if (typeof gsap !== "undefined") {
      console.log("GSAP already loaded");
      resolve();
      return;
    }

    console.log("Loading GSAP library...");
    const script = document.createElement("script");
    script.src =
      "https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.4/gsap.min.js";
    script.onload = () => {
      console.log("GSAP loaded successfully");
      resolve();
    };
    script.onerror = (error) => {
      console.error("Failed to load GSAP library:", error);
      reject(error);
    };
    document.head.appendChild(script);
  });
}

async function initEnhancedRoadmap() {
  // Load GSAP library first
  await loadGSAPLibrary();

  // Initialize AOS library if available (for animations)
  if (typeof AOS !== "undefined") {
    AOS.init({
      duration: 800,
      easing: "ease-out",
      once: true,
    });
  } else {
    // Fallback animation if AOS is not available
    document.querySelectorAll("[data-aos]").forEach((item) => {
      item.classList.add("visible");
    });
  }

  // Initialize roadmap items animation with intersection observer
  initRoadmapItemsAnimation();

  // Initialize expandable details
  initExpandableDetails();

  // Initialize view toggle
  initViewToggle();

  // Initialize filter functionality
  initFilters();

  // Initialize tooltip functionality
  initTooltips();

  // Initialize progress line animation
  initProgressLineAnimation();

  // Initialize mini-progress bars
  initMiniProgressBars();

  // Initialize circular progress animation
  initCircularProgressAnimation();

  // Initialize parallax effect for decorative shapes
  initParallaxEffect();

  // Add floating roadmap icons
  addFloatingIcons();

  // Add interactive particle background
  initParticleBackground();
}

function initRoadmapItemsAnimation() {
  const roadmapItems = document.querySelectorAll(
    ".roadmap-item, .roadmap-card"
  );

  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("visible");
          observer.unobserve(entry.target);
        }
      });
    },
    {
      threshold: 0.1,
    }
  );

  roadmapItems.forEach((item) => {
    observer.observe(item);
  });
}

function initExpandableDetails() {
  const expandButtons = document.querySelectorAll(".expand-details");

  expandButtons.forEach((button) => {
    button.addEventListener("click", function () {
      // Toggle the active class on the button
      this.classList.toggle("active");

      // Get the expanded content
      const expandedContent = this.nextElementSibling;
      expandedContent.classList.toggle("active");

      // Update button text
      const expandText = this.querySelector(".expand-text");
      if (expandText) {
        expandText.textContent = expandedContent.classList.contains("active")
          ? "Hide Details"
          : "View Details";
      }
    });
  });

  // Add modal functionality for card view details
  const cardDetailButtons = document.querySelectorAll(".view-card-details");
  cardDetailButtons.forEach((button) => {
    button.addEventListener("click", function () {
      const card = this.closest(".roadmap-card");
      const phase = card.getAttribute("data-phase");
      const title = card.querySelector("h3").textContent;

      // Show modal with details (implementation would depend on your modal system)
      showRoadmapModal(title, phase);
    });
  });
}

function showRoadmapModal(title, phase) {
  console.log("Opening modal for:", title, phase); // Debug logging

  // Create a modal dynamically
  const modal = document.createElement("div");
  modal.className = "roadmap-modal-overlay";
  modal.style.zIndex = "9999"; // Ensure high z-index

  // Add content based on the phase
  let content = "";
  switch (phase) {
    case "complete":
      content = `<h3>Phase Complete: ${title}</h3>
                <p>This phase has been successfully completed with all deliverables met.</p>
                <ul>
                  <li>12 tasks completed</li>
                  <li>Completed on schedule</li>
                  <li>All acceptance criteria met</li>
                </ul>`;
      break;
    case "current":
      content = `<h3>In Progress: ${title}</h3>
                <p>This phase is currently in progress with active development.</p>
                <div class="progress-indicator">
                  <div class="progress-bar" style="width: 65%"></div>
                  <span>65% Complete</span>
                </div>`;
      break;
    default:
      content = `<h3>Upcoming Phase: ${title}</h3>
                <p>This phase is scheduled to begin soon.</p>
                <p>Preparation work and resource allocation in progress.</p>`;
  }

  modal.innerHTML = `
    <div class="roadmap-modal ${phase}">
      <button class="modal-close"><i class="fas fa-times"></i></button>
      <div class="modal-content">
        ${content}
      </div>
    </div>
  `;

  // Ensure any existing modals are removed first
  const existingModal = document.querySelector(".roadmap-modal-overlay");
  if (existingModal) {
    existingModal.remove();
  }

  document.body.appendChild(modal);

  // Add close functionality
  setTimeout(() => {
    modal.classList.add("active");

    const closeBtn = modal.querySelector(".modal-close");
    closeBtn.addEventListener("click", () => {
      modal.classList.remove("active");
      setTimeout(() => {
        modal.remove();
      }, 300);
    });

    // Close on overlay click
    modal.addEventListener("click", (e) => {
      if (e.target === modal) {
        modal.classList.remove("active");
        setTimeout(() => {
          modal.remove();
        }, 300);
      }
    });
  }, 10);

  // Add styles for the modal if not already present
  if (!document.getElementById("roadmap-modal-styles")) {
    const style = document.createElement("style");
    style.id = "roadmap-modal-styles";
    style.textContent = `
      .roadmap-modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: auto;
      }
      
      .roadmap-modal-overlay.active {
        opacity: 1;
        visibility: visible;
      }
      
      .roadmap-modal {
        background: white;
        border-radius: 12px;
        padding: 30px;
        max-width: 500px;
        width: 90%;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
        transform: translateY(20px);
        transition: transform 0.3s ease;
        position: relative;
        z-index: 10000;
      }
      
      .roadmap-modal-overlay.active .roadmap-modal {
        transform: translateY(0);
      }
      
      .modal-close {
        position: absolute;
        top: 15px;
        right: 15px;
        background: none;
        border: none;
        font-size: 1.2rem;
        cursor: pointer;
        color: #888;
        transition: color 0.3s;
        z-index: 10001;
      }
      
      .modal-close:hover {
        color: #333;
      }
      
      .progress-indicator {
        margin-top: 20px;
      }
      
      .progress-bar {
        height: 8px;
        background: var(--primary, #4a6cf7);
        border-radius: 4px;
        margin-bottom: 8px;
      }

      .roadmap-modal h3 {
        margin-top: 0;
        color: #333;
        font-size: 1.5rem;
        margin-bottom: 15px;
      }

      .roadmap-modal p {
        margin-bottom: 15px;
        line-height: 1.5;
      }

      .roadmap-modal ul {
        margin-left: 20px;
        margin-bottom: 15px;
      }

      .roadmap-modal li {
        margin-bottom: 8px;
      }
    `;
    document.head.appendChild(style);
  }

  // Debug check - force display
  setTimeout(() => {
    if (!modal.classList.contains("active")) {
      console.log("Modal not activated automatically, forcing...");
      modal.classList.add("active");
      modal.style.opacity = "1";
      modal.style.visibility = "visible";
    }
  }, 100);
}

function initViewToggle() {
  const viewToggleButtons = document.querySelectorAll(".view-toggle");
  const timelineView = document.querySelector(".timeline-view");
  const cardsView = document.querySelector(".cards-view");
  const roadmapCards = document.querySelector(".roadmap-cards");

  // Check if cards need to be generated (only one card exists as a template)
  const generateCards = roadmapCards && roadmapCards.children.length <= 1;

  if (generateCards) {
    // Get all timeline items to convert to cards
    const timelineItems = document.querySelectorAll(".roadmap-item");

    // Clear existing cards first
    if (roadmapCards) {
      roadmapCards.innerHTML = "";

      // Create cards for each timeline item
      timelineItems.forEach((item) => {
        const phase = item.getAttribute("data-phase") || "upcoming";
        const title = item.querySelector("h3")?.textContent || "Roadmap Item";
        const date = item.querySelector(".roadmap-date")?.textContent || "";
        const description = item.querySelector("p")?.textContent || "";
        const details = item.querySelector(".roadmap-details")?.innerHTML || "";

        // Create new card element
        const card = document.createElement("div");
        card.className = `roadmap-card roadmap-${phase}`;
        card.setAttribute("data-phase", phase);

        // Set card HTML
        let statusText = "Upcoming";
        if (phase === "complete") statusText = "Completed";
        if (phase === "current") statusText = "In Progress";

        card.innerHTML = `
          <div class="card-status-indicator"></div>
          <div class="card-header">
            <div class="card-date">${date}</div>
            <div class="roadmap-status">${statusText}</div>
          </div>
          <h3>${title}</h3>
          <p>${description}</p>
          <div class="roadmap-details">
            ${details}
          </div>
          <button class="view-card-details">View Details</button>
        `;

        // Add to container
        roadmapCards.appendChild(card);
      });

      // Reinitialize detail buttons for new cards
      initCardDetailButtons();
    }
  }

  viewToggleButtons.forEach((button) => {
    button.addEventListener("click", function () {
      // Remove active class from all buttons
      viewToggleButtons.forEach((btn) => {
        btn.classList.remove("active");
      });

      // Add active class to clicked button
      this.classList.add("active");

      // Show the corresponding view
      const viewType = this.getAttribute("data-view");
      if (viewType === "timeline") {
        timelineView.classList.add("active");
        cardsView.classList.remove("active");
      } else {
        cardsView.classList.add("active");
        timelineView.classList.remove("active");

        // Trigger animation for cards
        setTimeout(() => {
          document.querySelectorAll(".roadmap-card").forEach((card, index) => {
            setTimeout(() => {
              card.classList.add("visible");
            }, index * 100);
          });
        }, 100);
      }
    });
  });
}

// Helper function to initialize detail buttons for dynamically created cards
function initCardDetailButtons() {
  // Add modal functionality for card view details
  const cardDetailButtons = document.querySelectorAll(".view-card-details");

  // First remove any existing event listeners by cloning and replacing each button
  cardDetailButtons.forEach((button) => {
    const newButton = button.cloneNode(true);
    if (button.parentNode) {
      button.parentNode.replaceChild(newButton, button);
    }
  });

  // Now add fresh event listeners to all buttons
  document.querySelectorAll(".view-card-details").forEach((button) => {
    // Add a direct click handler with debugging
    button.addEventListener("click", function (e) {
      // Prevent event bubbling
      e.preventDefault();
      e.stopPropagation();

      // Get the data from the card
      const card = this.closest(".roadmap-card");
      const phase = card.getAttribute("data-phase");
      const title = card.querySelector("h3").textContent;

      // Call the modal function directly
      showRoadmapModal(title, phase);
    });
  });
}

function initFilters() {
  const filterButtons = document.querySelectorAll(".roadmap-filter");
  const roadmapItems = document.querySelectorAll(
    ".roadmap-item, .roadmap-card"
  );

  filterButtons.forEach((button) => {
    button.addEventListener("click", function () {
      // Remove active class from all buttons
      filterButtons.forEach((btn) => {
        btn.classList.remove("active");
      });

      // Add active class to clicked button
      this.classList.add("active");

      const filter = this.getAttribute("data-filter");

      // Filter roadmap items with animation
      roadmapItems.forEach((item) => {
        item.classList.remove("visible");

        setTimeout(() => {
          if (filter === "all" || item.getAttribute("data-phase") === filter) {
            item.style.display = "";
            setTimeout(() => {
              item.classList.add("visible");
            }, 50);
          } else {
            item.style.display = "none";
          }
        }, 300);
      });

      // Make sure button handlers are reattached for cards that are now visible
      setTimeout(() => {
        // Reinitialize detail buttons for cards that are now visible
        initCardDetailButtons();
      }, 350);
    });
  });
}

function initTooltips() {
  const markers = document.querySelectorAll(".roadmap-marker");

  markers.forEach((marker) => {
    const tooltip = marker.getAttribute("data-tooltip");
    if (!tooltip) return;

    // Show tooltip on hover
    marker.addEventListener("mouseenter", function (e) {
      createTooltip(this, tooltip);
    });

    marker.addEventListener("mouseleave", function () {
      removeTooltip();
    });
  });

  function createTooltip(element, text) {
    removeTooltip(); // Remove any existing tooltips

    const tooltip = document.createElement("div");
    tooltip.className = "roadmap-tooltip";
    tooltip.textContent = text;
    document.body.appendChild(tooltip);

    // Position the tooltip above the marker
    const rect = element.getBoundingClientRect();
    tooltip.style.left =
      rect.left + rect.width / 2 - tooltip.offsetWidth / 2 + "px";
    tooltip.style.top =
      rect.top - tooltip.offsetHeight - 10 + window.scrollY + "px";

    // Add fade-in animation
    setTimeout(() => {
      tooltip.classList.add("active");
    }, 10);

    // Add tooltip styles if not already present
    if (!document.getElementById("roadmap-tooltip-styles")) {
      const style = document.createElement("style");
      style.id = "roadmap-tooltip-styles";
      style.textContent = `
        .roadmap-tooltip {
          position: absolute;
          background: #333;
          color: white;
          padding: 5px 10px;
          border-radius: 4px;
          font-size: 0.8rem;
          z-index: 100;
          pointer-events: none;
          opacity: 0;
          transform: translateY(5px);
          transition: opacity 0.2s, transform 0.2s;
        }
        
        .roadmap-tooltip::after {
          content: '';
          position: absolute;
          bottom: -5px;
          left: 50%;
          transform: translateX(-50%);
          border-width: 5px 5px 0;
          border-style: solid;
          border-color: #333 transparent transparent;
        }
        
        .roadmap-tooltip.active {
          opacity: 1;
          transform: translateY(0);
        }
      `;
      document.head.appendChild(style);
    }
  }

  function removeTooltip() {
    const tooltip = document.querySelector(".roadmap-tooltip");
    if (tooltip) {
      tooltip.classList.remove("active");

      setTimeout(() => {
        tooltip.remove();
      }, 200);
    }
  }
}

function initProgressLineAnimation() {
  const progressLine = document.querySelector(".roadmap-progress-line");
  if (!progressLine) return;

  // Reset to 0 first
  progressLine.style.height = "0%";

  // Create an observer to trigger animation when in view
  const progressObserver = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          // Add small delay for better visual effect
          setTimeout(() => {
            progressLine.style.height = "67%";
          }, 500);
          progressObserver.unobserve(entry.target);
        }
      });
    },
    { threshold: 0.2 }
  );

  // Get the roadmap section to observe
  const roadmapSection = document.getElementById("roadmap");
  if (roadmapSection) {
    progressObserver.observe(roadmapSection);
  } else {
    // Fallback - observe the timeline wrapper
    const timelineWrapper = document.querySelector(".roadmap-timeline-wrapper");
    if (timelineWrapper) {
      progressObserver.observe(timelineWrapper);
    }
  }
}

function initParallaxEffect() {
  const roadmapSection = document.querySelector(".roadmap-section");
  const shapes = document.querySelectorAll(".decorative-shape");
  const icons = document.querySelectorAll(".roadmap-icon");

  if (!roadmapSection || (!shapes.length && !icons.length)) return;

  roadmapSection.addEventListener("mousemove", (e) => {
    const { left, top, width, height } = roadmapSection.getBoundingClientRect();
    const x = e.clientX - left;
    const y = e.clientY - top;

    const moveX = (x - width / 2) / width;
    const moveY = (y - height / 2) / height;

    // Apply to shapes with varying intensity
    shapes.forEach((shape, index) => {
      const factor = (index + 1) * 5;
      const moveFactorX = moveX * factor;
      const moveFactorY = moveY * factor;

      shape.style.transform = `translate(${moveFactorX}px, ${moveFactorY}px)`;
    });

    // Apply to icons with opposing movement for depth effect
    icons.forEach((icon, index) => {
      const factor = ((index % 3) + 1) * -3; // Opposite direction with varying intensity
      const moveFactorX = moveX * factor;
      const moveFactorY = moveY * factor;

      // Extract the current transform values
      const computedStyle = window.getComputedStyle(icon);
      const matrix = new DOMMatrix(computedStyle.transform);

      // Add parallax effect without disturbing the existing animation transforms
      icon.style.transform = `translate(${moveFactorX}px, ${moveFactorY}px) scale(${matrix.m11})`;
    });
  });

  // Reset positions when mouse leaves the section
  roadmapSection.addEventListener("mouseleave", () => {
    shapes.forEach((shape) => {
      shape.style.transform = "translate(0, 0)";
    });

    icons.forEach((icon) => {
      icon.style.transform = "";
    });
  });
}

function addFloatingIcons() {
  const roadmapSection = document.querySelector(".roadmap-section");
  if (!roadmapSection) return;

  // Icon definitions: icon class, position, animation variables, color
  const icons = [
    {
      icon: "fa-flag",
      top: "15%",
      left: "5%",
      delay: "0s",
      translateX: "40px",
      translateY: "-20px",
      rotate: "10deg",
      color: "var(--primary)",
    },
    {
      icon: "fa-check-circle",
      top: "25%",
      right: "8%",
      delay: "2s",
      translateX: "-30px",
      translateY: "-40px",
      rotate: "-5deg",
      color: "var(--success)",
    },
    {
      icon: "fa-tasks",
      top: "45%",
      left: "12%",
      delay: "4s",
      translateX: "25px",
      translateY: "-15px",
      rotate: "8deg",
      color: "var(--primary-light)",
    },
    {
      icon: "fa-code-branch",
      top: "60%",
      right: "15%",
      delay: "1s",
      translateX: "-20px",
      translateY: "-30px",
      rotate: "-12deg",
      color: "var(--info)",
    },
    {
      icon: "fa-cogs",
      top: "75%",
      left: "20%",
      delay: "3s",
      translateX: "15px",
      translateY: "-25px",
      rotate: "15deg",
      color: "var(--secondary)",
    },
    {
      icon: "fa-calendar-check",
      top: "80%",
      right: "10%",
      delay: "5s",
      translateX: "-35px",
      translateY: "-10px",
      rotate: "-8deg",
      color: "var(--success)",
    },
    {
      icon: "fa-chart-line",
      top: "10%",
      right: "20%",
      delay: "2.5s",
      translateX: "-15px",
      translateY: "-35px",
      rotate: "6deg",
      color: "var(--primary)",
    },
    {
      icon: "fa-rocket",
      top: "35%",
      left: "7%",
      delay: "1.5s",
      translateX: "30px",
      translateY: "-45px",
      rotate: "-10deg",
      color: "var(--warning)",
    },
    {
      icon: "fa-lightbulb",
      top: "50%",
      right: "5%",
      delay: "3.5s",
      translateX: "-25px",
      translateY: "-30px",
      rotate: "7deg",
      color: "var(--warning)",
    },
    {
      icon: "fa-bug",
      top: "65%",
      left: "3%",
      delay: "2.8s",
      translateX: "20px",
      translateY: "-15px",
      rotate: "-9deg",
      color: "var(--danger)",
    },
    {
      icon: "fa-code",
      top: "20%",
      left: "15%",
      delay: "1.2s",
      translateX: "35px",
      translateY: "-25px",
      rotate: "12deg",
      color: "var(--info)",
    },
    {
      icon: "fa-server",
      top: "40%",
      right: "3%",
      delay: "4.5s",
      translateX: "-15px",
      translateY: "-35px",
      rotate: "-11deg",
      color: "var(--secondary)",
    },
    {
      icon: "fa-database",
      top: "55%",
      left: "25%",
      delay: "0.5s",
      translateX: "25px",
      translateY: "-20px",
      rotate: "8deg",
      color: "var(--primary-dark)",
    },
    {
      icon: "fa-shield-alt",
      top: "70%",
      right: "25%",
      delay: "2.2s",
      translateX: "-30px",
      translateY: "-15px",
      rotate: "-6deg",
      color: "var(--success)",
    },
    {
      icon: "fa-file-code",
      top: "85%",
      left: "15%",
      delay: "3.8s",
      translateX: "20px",
      translateY: "-30px",
      rotate: "9deg",
      color: "var(--primary-light)",
    },
    {
      icon: "fa-users",
      top: "30%",
      right: "30%",
      delay: "1.8s",
      translateX: "-25px",
      translateY: "-20px",
      rotate: "-8deg",
      color: "var(--info)",
    },
    {
      icon: "fa-mobile-alt",
      top: "5%",
      left: "30%",
      delay: "2.7s",
      translateX: "30px",
      translateY: "-25px",
      rotate: "11deg",
      color: "var(--warning)",
    },
    {
      icon: "fa-sitemap",
      top: "90%",
      right: "20%",
      delay: "4.2s",
      translateX: "-20px",
      translateY: "-15px",
      rotate: "-7deg",
      color: "var(--primary-dark)",
    },
    {
      icon: "fa-terminal",
      top: "15%",
      left: "40%",
      delay: "3.3s",
      translateX: "15px",
      translateY: "-30px",
      rotate: "10deg",
      color: "var(--secondary)",
    },
  ];

  // Create and append each icon
  icons.forEach((config) => {
    const iconElement = document.createElement("i");
    iconElement.className = `roadmap-icon fas ${config.icon}`;
    iconElement.style.top = config.top;
    config.left && (iconElement.style.left = config.left);
    config.right && (iconElement.style.right = config.right);
    iconElement.style.setProperty("--delay", config.delay);
    iconElement.style.setProperty("--translateX", config.translateX);
    iconElement.style.setProperty("--translateY", config.translateY);
    iconElement.style.setProperty("--rotate", config.rotate);
    iconElement.style.setProperty("--icon-color", config.color);
    iconElement.style.fontSize = Math.floor(Math.random() * 12 + 14) + "px"; // Random size between 14-26px

    // Add emitter div as a trigger point for animations
    const emitter = document.createElement("div");
    emitter.className = "emitter";
    iconElement.appendChild(emitter);

    // Add click handler for explosion effect
    iconElement.addEventListener("click", function (e) {
      createExplosionEffect(e, this);
    });

    roadmapSection.appendChild(iconElement);
  });

  // Add explosion animation styles if not already present
  if (!document.getElementById("explosion-styles")) {
    const style = document.createElement("style");
    style.id = "explosion-styles";
    style.textContent = `
      .emitter {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        border-radius: 50%;
        pointer-events: none;
      }
      
      .explosion-particle {
        position: fixed;
        background-color: var(--icon-color, var(--primary));
        border-radius: 50%;
        width: 5px;
        height: 5px;
        pointer-events: none;
        z-index: 9999;
        box-shadow: 0 0 8px 2px var(--icon-color, var(--primary));
      }
      
      .shockwave {
        position: fixed;
        border-radius: 50%;
        background-color: transparent;
        border: 2px solid var(--icon-color, var(--primary));
        opacity: 0.8;
        pointer-events: none;
        z-index: 9998;
        transform: translate(-50%, -50%);
      }
      
      .roadmap-icon.exploding {
        pointer-events: none;
      }
      
      .particle-container {
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 10000;
      }
      
      /* Fallback animations if GSAP fails to load */
      @keyframes shockwave-fallback {
        0% {
          width: 0;
          height: 0;
          opacity: 0.8;
        }
        100% {
          width: 150px;
          height: 150px;
          opacity: 0;
        }
      }
      
      @keyframes particle-fallback {
        0% {
          transform: translate(0, 0) scale(1) rotate(0deg);
          opacity: 1;
        }
        100% {
          transform: translate(${Math.random() * 200 - 100}px, ${
      Math.random() * 200 - 100
    }px) scale(0) rotate(${Math.random() * 360}deg);
          opacity: 0;
        }
      }
    `;
    document.head.appendChild(style);
  }
}

// Function to create icon fragments for explosion effect
function createIconFragments(x, y, iconElement, iconColor) {
  // Create multiple small copies of the icon that will fly in different directions
  const fragmentCount = 6 + Math.floor(Math.random() * 4);

  for (let i = 0; i < fragmentCount; i++) {
    const fragment = document.createElement("i");
    // Use the same icon class for realistic fragments
    fragment.className = `fas ${iconElement.className
      .replace("roadmap-icon fas ", "")
      .replace(" exploding", "")}`;
    fragment.style.position = "fixed";
    fragment.style.left = `${x}px`;
    fragment.style.top = `${y}px`;
    fragment.style.color = iconColor;
    fragment.style.fontSize = iconElement.style.fontSize || "16px";
    fragment.style.opacity = "0.9";
    fragment.style.zIndex = "9999";
    fragment.style.pointerEvents = "none";
    fragment.style.transformOrigin = "center center";
    document.body.appendChild(fragment);

    // Calculate random direction for each fragment
    const angle = Math.random() * Math.PI * 2;
    const speedX = Math.cos(angle) * (5 + Math.random() * 10);
    const speedY = Math.sin(angle) * (5 + Math.random() * 10);
    const rotation = Math.random() * 720 - 360;

    // Animation with GSAP if available
    if (typeof gsap !== "undefined") {
      gsap.to(fragment, {
        x: speedX * 10,
        y: speedY * 10,
        rotation: rotation,
        opacity: 0,
        scale: 0.2,
        duration: 0.8 + Math.random() * 0.4,
        ease: "power2.out",
        onComplete: () => fragment.remove(),
      });
    } else {
      // Fallback animation
      fragment.style.transition = "all 1s ease-out";
      setTimeout(() => {
        fragment.style.transform = `translate(${speedX * 10}px, ${
          speedY * 10
        }px) rotate(${rotation}deg) scale(0.2)`;
        fragment.style.opacity = "0";
      }, 10);
      setTimeout(() => fragment.remove(), 1000);
    }
  }
}

// Enhanced explosion effect when icon is clicked with GSAP animations
function createExplosionEffect(event, iconElement) {
  // Don't process if already exploding
  if (iconElement.classList.contains("exploding")) return;

  console.log("Creating explosion effect");

  // Get the current position and color of the icon
  const rect = iconElement.getBoundingClientRect();
  const centerX = rect.left + rect.width / 2;
  const centerY = rect.top + rect.height / 2;
  const iconColor =
    iconElement.style.getPropertyValue("--icon-color") || "var(--primary)";

  // Create icon fragments that fly outward
  createIconFragments(centerX, centerY, iconElement, iconColor);

  // Create the shockwave effect
  const shockwave = document.createElement("div");
  shockwave.className = "shockwave";
  shockwave.style.left = `${centerX}px`;
  shockwave.style.top = `${centerY}px`;
  shockwave.style.setProperty("--icon-color", iconColor);
  document.body.appendChild(shockwave);

  // Check if GSAP is available
  if (typeof gsap === "undefined") {
    console.error("GSAP not loaded, using fallback animation");
    // Fallback animation without GSAP
    shockwave.style.animation = "shockwave-fallback 0.6s forwards";
    setTimeout(() => shockwave.remove(), 600);
  } else {
    // Animate shockwave with GSAP
    gsap.fromTo(
      shockwave,
      { width: 0, height: 0, opacity: 0.8 },
      {
        width: 150,
        height: 150,
        opacity: 0,
        duration: 0.6,
        ease: "power2.out",
        onComplete: () => {
          shockwave.remove();
        },
      }
    );

    // Make the original icon explode with more dramatic effect
    gsap.to(iconElement, {
      scale: 1.5,
      opacity: 1,
      duration: 0.2,
      ease: "power1.out",
      onComplete: () => {
        gsap.to(iconElement, {
          scale: 0,
          opacity: 0,
          duration: 0.3,
          ease: "power2.in",
        });
      },
    });
  }

  // Add exploding class to trigger some CSS effects
  iconElement.classList.add("exploding");

  // Create particles for the explosion with more variety
  const particleCount = 40; // More particles for a richer effect
  const colors = [
    iconColor,
    "#FF577F",
    "#FF884B",
    "#FFD384",
    "#4361EE",
    "#3A0CA3",
    "#F72585",
  ];

  console.log(`Creating ${particleCount} particles`);

  // Create a container for all particles to improve performance
  const particleContainer = document.createElement("div");
  particleContainer.className = "particle-container";
  particleContainer.style.position = "fixed";
  particleContainer.style.left = "0";
  particleContainer.style.top = "0";
  particleContainer.style.width = "100%";
  particleContainer.style.height = "100%";
  particleContainer.style.pointerEvents = "none";
  particleContainer.style.zIndex = "10000";
  document.body.appendChild(particleContainer);

  // Create multiple particles with different properties
  for (let i = 0; i < particleCount; i++) {
    setTimeout(() => {
      createParticleWithGSAP(
        centerX,
        centerY,
        iconColor,
        colors,
        particleContainer
      );
    }, i * 5); // Stagger particle creation for more natural effect
  }

  // Remove the particle container after all animations complete
  setTimeout(() => {
    particleContainer.remove();
  }, 2000);

  // Remove and recreate the icon after animation completes
  setTimeout(() => {
    // Store the icon properties before removing
    const iconConfig = {
      icon: iconElement.className
        .replace("roadmap-icon fas ", "")
        .replace(" exploding", ""),
      top: iconElement.style.top,
      left: iconElement.style.left,
      right: iconElement.style.right,
      delay: iconElement.style.getPropertyValue("--delay"),
      translateX: iconElement.style.getPropertyValue("--translateX"),
      translateY: iconElement.style.getPropertyValue("--translateY"),
      rotate: iconElement.style.getPropertyValue("--rotate"),
      color: iconElement.style.getPropertyValue("--icon-color"),
      fontSize: iconElement.style.fontSize,
    };

    // Remove the original icon
    iconElement.remove();

    // Create new icon with the same properties
    const newIcon = document.createElement("i");
    newIcon.className = `roadmap-icon fas ${iconConfig.icon}`;
    newIcon.style.top = iconConfig.top;
    newIcon.style.left = iconConfig.left;
    newIcon.style.right = iconConfig.right;
    newIcon.style.setProperty("--delay", iconConfig.delay);
    newIcon.style.setProperty("--translateX", iconConfig.translateX);
    newIcon.style.setProperty("--translateY", iconConfig.translateY);
    newIcon.style.setProperty("--rotate", iconConfig.rotate);
    newIcon.style.setProperty("--icon-color", iconConfig.color);
    newIcon.style.fontSize = iconConfig.fontSize;

    // Add the emitter div to the new icon
    const emitter = document.createElement("div");
    emitter.className = "emitter";
    newIcon.appendChild(emitter);

    // Add the click event listener to the new icon
    newIcon.addEventListener("click", function (e) {
      createExplosionEffect(e, this);
    });

    // Append the new icon to the roadmap section
    const roadmapSection = document.querySelector(".roadmap-section");
    if (roadmapSection) {
      roadmapSection.appendChild(newIcon);
    }
  }, 700); // Match this to the animation duration
}

// Create enhanced particles with GSAP animations
function createParticleWithGSAP(x, y, baseColor, colors, container) {
  const particle = document.createElement("div");
  particle.className = "explosion-particle";

  // More variety in particle appearance
  const randomColor = colors[Math.floor(Math.random() * colors.length)];
  particle.style.backgroundColor = randomColor;

  // Set initial position at the center of the explosion
  particle.style.position = "absolute";
  particle.style.left = `${x}px`;
  particle.style.top = `${y}px`;
  particle.style.transform = "translate(-50%, -50%)"; // Center particle on coordinates

  // Generate random properties for diverse particles
  const size = 3 + Math.random() * 7;
  particle.style.width = `${size}px`;
  particle.style.height = `${size}px`;

  // Random shape variation
  const shapes = ["50%", "2px", "0", `${Math.floor(Math.random() * 4 + 2)}px`];
  particle.style.borderRadius =
    shapes[Math.floor(Math.random() * shapes.length)];

  // Add more visual interest
  particle.style.boxShadow = `0 0 ${Math.random() * 5 + 3}px ${randomColor}`;
  particle.style.opacity = "1";

  // Add to the container
  if (container) {
    container.appendChild(particle);
  } else {
    document.body.appendChild(particle);
  }

  // Check if GSAP is available
  if (typeof gsap === "undefined") {
    console.error("GSAP not loaded, using fallback CSS animation");
    // Apply CSS animation as fallback
    particle.style.transition = "all 1s ease-out";
    particle.style.transitionProperty = "transform, opacity";

    setTimeout(() => {
      const randomX = (Math.random() - 0.5) * 200;
      const randomY = (Math.random() - 0.5) * 200;
      particle.style.transform = `translate(${randomX}px, ${randomY}px) scale(0.2) rotate(${
        Math.random() * 360
      }deg)`;
      particle.style.opacity = "0";
    }, 10);

    setTimeout(() => particle.remove(), 1000);
  } else {
    // Create more dynamic movement
    const angle = Math.random() * Math.PI * 2; // Random angle in radians
    const velocity = 5 + Math.random() * 15; // Random velocity
    const gravity = 0.5 + Math.random() * 0.5; // Random gravity effect
    const friction = 0.95 + Math.random() * 0.03; // Add friction for varied deceleration

    const velocityX = Math.cos(angle) * velocity;
    const velocityY = Math.sin(angle) * velocity;

    // Animate with GSAP for physics-driven movement
    gsap.to(particle, {
      duration: 0.01, // Start with initial position
      scale: 1,
      opacity: 1,
    });

    gsap.to(particle, {
      keyframes: [
        {
          // First keyframe - initial burst outward
          x: velocityX * 5,
          y: velocityY * 5,
          scale: Math.random() * 0.5 + 0.8,
          rotation: Math.random() * 180,
          duration: 0.2,
          ease: "power1.out",
        },
        {
          // Second keyframe - apply gravity and friction
          x: velocityX * 20 * friction,
          y: velocityY * 20 * friction + gravity * 30,
          scale: 0.3,
          opacity: Math.random() * 0.5 + 0.3,
          rotation: Math.random() * 360,
          duration: 0.4,
          ease: "power2.out",
        },
        {
          // Final keyframe - fade out with gravity
          x: velocityX * 25 * friction * friction,
          y: velocityY * 25 * friction * friction + gravity * 100,
          opacity: 0,
          scale: 0.1,
          rotation: Math.random() * 720,
          duration: 0.5,
          ease: "power1.in",
        },
      ],
      onComplete: () => {
        particle.remove();
      },
    });
  }
}

function initParticleBackground() {
  const roadmapSection = document.querySelector(".roadmap-section");
  if (!roadmapSection) return;

  // Create canvas element
  const canvas = document.createElement("canvas");
  canvas.className = "roadmap-particles";
  canvas.style.position = "absolute";
  canvas.style.top = "0";
  canvas.style.left = "0";
  canvas.style.width = "100%";
  canvas.style.height = "100%";
  canvas.style.pointerEvents = "none";
  canvas.style.zIndex = "1";
  canvas.style.opacity = "0.3";

  roadmapSection.insertBefore(canvas, roadmapSection.firstChild);

  // Only initialize if browser supports canvas
  if (canvas.getContext) {
    const ctx = canvas.getContext("2d");
    let particles = [];

    // Resize handler for canvas
    function resizeCanvas() {
      canvas.width = roadmapSection.offsetWidth;
      canvas.height = roadmapSection.offsetHeight;
      initParticles(); // Reinitialize particles on resize
    }

    // Initialize particles
    function initParticles() {
      particles = [];
      const particleCount = Math.min(
        Math.floor((canvas.width * canvas.height) / 20000),
        50
      );

      for (let i = 0; i < particleCount; i++) {
        particles.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          radius: Math.random() * 3 + 1,
          color: `rgba(67, 97, 238, ${Math.random() * 0.2 + 0.1})`,
          speedX: Math.random() * 0.5 - 0.25,
          speedY: Math.random() * 0.5 - 0.25,
        });
      }
    }

    // Animation loop
    function animateParticles() {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      particles.forEach((particle) => {
        // Move particle
        particle.x += particle.speedX;
        particle.y += particle.speedY;

        // Bounce off edges
        if (particle.x < 0 || particle.x > canvas.width) particle.speedX *= -1;
        if (particle.y < 0 || particle.y > canvas.height) particle.speedY *= -1;

        // Draw particle
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2);
        ctx.fillStyle = particle.color;
        ctx.fill();
      });

      requestAnimationFrame(animateParticles);
    }

    // Initialize system
    window.addEventListener("resize", resizeCanvas);
    resizeCanvas();
    animateParticles();
  }
}

function initMiniProgressBars() {
  const miniBars = document.querySelectorAll(".mini-bar");

  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          // Store original width
          const targetWidth = entry.target.style.width;

          // Reset first
          entry.target.style.width = "0";
          entry.target.style.transform = "scaleX(0)";

          // Force reflow
          entry.target.offsetWidth;

          // Start animation with slight delay
          setTimeout(() => {
            entry.target.style.transform = "scaleX(1)";
            entry.target.style.width = targetWidth;
          }, 200);

          observer.unobserve(entry.target);
        }
      });
    },
    { threshold: 0.2 }
  );

  miniBars.forEach((bar) => observer.observe(bar));
}

function initCircularProgressAnimation() {
  const circularProgressElements =
    document.querySelectorAll(".progress-circular");

  circularProgressElements.forEach((element) => {
    const progressText = element.querySelector(".progress-circular-text");
    const targetValue = parseInt(progressText.textContent);

    if (!isNaN(targetValue)) {
      // Reset the text content to start from 0
      progressText.textContent = "0%";

      // Create an intersection observer to start the animation when in view
      const observer = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting) {
            // Animate the counter
            animateProgressCounter(progressText, targetValue);

            // Add interactive effects
            element.addEventListener("mouseenter", () => {
              element.classList.add("highlight");
              const progressOuter = element.querySelector(
                ".progress-circular-outer"
              );
              if (progressOuter) {
                progressOuter.style.filter =
                  "drop-shadow(0 8px 15px rgba(67, 97, 238, 0.4))";
              }
            });

            element.addEventListener("mouseleave", () => {
              element.classList.remove("highlight");
              const progressOuter = element.querySelector(
                ".progress-circular-outer"
              );
              if (progressOuter) {
                progressOuter.style.filter = "";
              }
            });

            // Stop observing once animation starts
            observer.unobserve(element);
          }
        },
        { threshold: 0.2 }
      );

      observer.observe(element);
    }
  });
}

function animateProgressCounter(element, targetValue) {
  let startValue = 0;
  const duration = 2000; // 2 seconds
  const step = (timestamp) => {
    if (!startTime) startTime = timestamp;
    const progress = Math.min((timestamp - startTime) / duration, 1);
    const currentValue = Math.floor(progress * targetValue);

    element.textContent = `${currentValue}%`;

    if (progress < 1) {
      window.requestAnimationFrame(step);
    } else {
      element.textContent = `${targetValue}%`;
      // Add a final celebration effect
      element.style.animation = "number-celebrate 0.5s ease-in-out";
    }
  };

  let startTime = null;
  window.requestAnimationFrame(step);
}
