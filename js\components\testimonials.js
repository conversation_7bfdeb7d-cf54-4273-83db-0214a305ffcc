// Testimonial slider functionality
async function initTestimonialSlider() {
  const slider = document.querySelector(".testimonials-slider");
  if (!slider) return;

  // First, add more testimonials with categories
  await addMoreTestimonials();

  // Then add navigation elements
  addTestimonialNavigation();

  // Initialize filters
  initTestimonialFilters();

  // Initialize the slider
  const testimonials = document.querySelectorAll(".testimonial-item");
  // Query for navigation elements, regardless of when they were added
  const navDots = document.querySelectorAll(".nav-dot");
  let currentIndex = 0;
  let testimonialWidth;
  let autoplayInterval;
  let filteredTestimonials = [...testimonials];

  // Add animation style for smooth transitions and bounce effect
  const style = document.createElement("style");
  style.textContent = `
        @keyframes testimonial-bounce {
            0% { transform: scale(0.95) translateY(10px); }
            50% { transform: scale(1.02) translateY(-5px); }
            100% { transform: scale(1) translateY(0); }
        }
        
        .testimonial-item {
            opacity: 0;
            transition: opacity 0.5s ease, transform 0.5s cubic-bezier(0.19, 1, 0.22, 1);
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            transform-style: preserve-3d;
        }
        
        .testimonial-item.active {
            opacity: 1;
            z-index: 2;
            transform: translateX(0) scale(1) rotateY(0);
            filter: blur(0);
        }
        
        .testimonial-item.prev {
            opacity: 0.4;
            transform: translateX(-50px) scale(0.85) rotateY(-5deg);
            z-index: 1;
            filter: blur(1px);
        }
        
        .testimonial-item.next {
            opacity: 0.4;
            transform: translateX(50px) scale(0.85) rotateY(5deg);
            z-index: 1;
            filter: blur(1px);
        }
        
        .testimonial-item.bouncing {
            animation: testimonial-bounce 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }
        
        /* Added smooth animations for filter buttons */
        .testimonial-filter {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .testimonial-filter::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: rgba(67, 97, 238, 0.1);
            transition: transform 0.4s ease;
            z-index: -1;
        }
        
        .testimonial-filter:hover::before {
            transform: translateX(100%);
        }
        
        .testimonial-filter.active {
            transform: translateY(-3px);
            transition: transform 0.3s ease, background 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
        }
        
        /* Animation for filters when they become active */
        @keyframes filter-active {
            0% { transform: scale(0.95); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .testimonial-filter.animate-active {
            animation: filter-active 0.4s ease forwards;
        }
        
        /* Adding animation for the rating stars */
        .rating i {
            display: inline-block;
            transition: transform 0.3s ease;
            transform: translateY(0);
        }
        
        .testimonial-item.active .rating i {
            animation: star-pulse 0.5s ease forwards;
            animation-delay: calc(0.1s * var(--i));
        }
        
        @keyframes star-pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }
    `;
  document.head.appendChild(style);

  // Make all testimonials visible for animation
  testimonials.forEach((item, i) => {
    setTimeout(() => {
      item.classList.add("visible");
    }, i * 200);
  });

  // Set up the slider dimensions and positions
  function setupSlider() {
    testimonialWidth = testimonials[0]?.offsetWidth + 30 || 300; // Width + margin
    updateSlider();
  }

  // Update the slider position with improved animation
  function updateSlider() {
    // First set the height of the slider container
    slider.style.height = `${
      filteredTestimonials[currentIndex]?.offsetHeight || 350
    }px`;

    // Apply the correct classes instead of direct style manipulation
    filteredTestimonials.forEach((item, i) => {
      // Remove all position classes first
      item.classList.remove("active", "prev", "next", "bouncing");

      if (i === currentIndex) {
        item.classList.add("active");

        // Add animation-delay to each star
        const stars = item.querySelectorAll(".rating i");
        stars.forEach((star, index) => {
          star.style.setProperty("--i", index);
        });
      } else if (i < currentIndex) {
        item.classList.add("prev");
      } else {
        item.classList.add("next");
      }
    });

    // Update navigation dots
    navDots.forEach((dot, i) => {
      dot.classList.toggle("active", i === currentIndex);
      // Only show dots for filtered testimonials
      dot.style.display = i < filteredTestimonials.length ? "block" : "none";
    });
  }

  // Apply bounce animation separately from position transitions
  function applyBounceAnimation() {
    if (filteredTestimonials[currentIndex]) {
      // Remove any existing animation
      filteredTestimonials[currentIndex].classList.remove("bouncing");
      // Force a reflow to restart the animation
      void filteredTestimonials[currentIndex].offsetWidth;
      // Apply the bounce animation
      filteredTestimonials[currentIndex].classList.add("bouncing");
    }
  }

  // Navigation functions
  function nextSlide() {
    if (filteredTestimonials.length === 0) return;
    currentIndex = (currentIndex + 1) % filteredTestimonials.length;
    updateSlider();
    // Apply bounce animation after position transition
    setTimeout(applyBounceAnimation, 50);
  }

  function prevSlide() {
    if (filteredTestimonials.length === 0) return;
    currentIndex =
      (currentIndex - 1 + filteredTestimonials.length) %
      filteredTestimonials.length;
    updateSlider();
    // Apply bounce animation after position transition
    setTimeout(applyBounceAnimation, 50);
  }

  function goToSlide(index) {
    if (
      filteredTestimonials.length === 0 ||
      index >= filteredTestimonials.length
    )
      return;
    currentIndex = index;
    updateSlider();
    // Apply bounce animation after position transition
    setTimeout(applyBounceAnimation, 50);
  }

  // Find and properly attach event listeners to navigation buttons
  const nextButtons = document.querySelectorAll(".next-testimonial");
  const prevButtons = document.querySelectorAll(".prev-testimonial");

  // Ensure we have proper listeners
  if (nextButtons.length === 0 || prevButtons.length === 0) {
    console.warn("Testimonial navigation buttons not found");
  }

  // Set up event listeners with direct click handling
  nextButtons.forEach((btn) => {
    btn.addEventListener("click", function (e) {
      e.preventDefault();
      nextSlide();
      resetAutoplay();
    });
  });

  prevButtons.forEach((btn) => {
    btn.addEventListener("click", function (e) {
      e.preventDefault();
      prevSlide();
      resetAutoplay();
    });
  });

  // Setup navigation dots
  navDots.forEach((dot, i) => {
    dot.addEventListener("click", () => {
      goToSlide(i);
      resetAutoplay();
    });
  });

  // Setup autoplay
  function startAutoplay() {
    autoplayInterval = setInterval(() => {
      nextSlide();
    }, 5000);
  }

  function resetAutoplay() {
    clearInterval(autoplayInterval);
    startAutoplay();
  }

  // Add keyboard navigation
  document.addEventListener("keydown", (e) => {
    // Only trigger if we're in the viewport area of the testimonials
    const rect = slider.getBoundingClientRect();
    const inView = rect.top < window.innerHeight && rect.bottom > 0;
    if (!inView) return;

    if (e.key === "ArrowRight") {
      nextSlide();
      resetAutoplay();
    } else if (e.key === "ArrowLeft") {
      prevSlide();
      resetAutoplay();
    }
  });

  // Add touch support
  let touchStartX = 0;
  let touchEndX = 0;

  slider.addEventListener(
    "touchstart",
    (e) => {
      touchStartX = e.changedTouches[0].screenX;
    },
    { passive: true }
  );

  slider.addEventListener(
    "touchend",
    (e) => {
      touchEndX = e.changedTouches[0].screenX;
      handleSwipe();
    },
    { passive: true }
  );

  function handleSwipe() {
    const minSwipeDistance = 50;
    if (touchEndX < touchStartX - minSwipeDistance) {
      nextSlide();
      resetAutoplay();
    }
    if (touchEndX > touchStartX + minSwipeDistance) {
      prevSlide();
      resetAutoplay();
    }
  }

  // Initialize slider
  setupSlider();
  startAutoplay();

  // Update on resize
  window.addEventListener("resize", setupSlider);

  // Store slider instance methods on the element for external access
  slider.updateSlider = updateSlider;
  slider.nextSlide = nextSlide;
  slider.prevSlide = prevSlide;
  slider.currentIndex = currentIndex;

  // Expose filteredTestimonials to the window for debugging
  window.sliderDebug = {
    slider,
    filteredTestimonials,
    nextSlide,
    prevSlide,
  };
}

// Add more testimonials
async function addMoreTestimonials() {
  const sliderContainer = document.querySelector(".testimonials-slider");
  if (!sliderContainer) return;

  // Store a reference to the first testimonial (for structure)
  const existingTestimonial =
    sliderContainer.querySelector(".testimonial-item");
  if (!existingTestimonial) return;

  // Clear existing testimonials
  while (sliderContainer.firstChild) {
    sliderContainer.removeChild(sliderContainer.firstChild);
  }

  try {
    // Fetch testimonials from JSON file
    const response = await fetch("/data/testimonials.json");
    if (!response.ok) {
      throw new Error("Failed to load testimonials data");
    }

    const testimonials = await response.json();

    // Create and append the new testimonials
    testimonials.forEach((testimonial) => {
      const newItem = document.createElement("div");
      newItem.className = "testimonial-item animate-in";
      newItem.dataset.category = testimonial.category;

      // Generate stars based on rating
      const ratingHTML = Array(5)
        .fill()
        .map(
          (_, i) =>
            `<i class="fas fa-star${
              i >= testimonial.rating ? " far" : ""
            }"></i>`
        )
        .join("");

      newItem.innerHTML = `
        <div class="testimonial-content">
          <div class="quote"><i class="fas fa-quote-left"></i></div>
          <div class="rating">${ratingHTML}</div>
          <p>${testimonial.text}</p>
          <div class="testimonial-author">
            <div class="author-img"><i class="fas fa-user-tie"></i></div>
            <div class="author-info">
              <h4>${testimonial.author}</h4>
              <span>${testimonial.role}</span>
            </div>
          </div>
        </div>
      `;
      sliderContainer.appendChild(newItem);
    });
  } catch (error) {
    console.error("Error loading testimonials:", error);
    // Fallback to a minimal testimonial if JSON fetch fails
    const fallbackTestimonial = {
      text: "This project demonstrates excellent technical skills and innovation.",
      author: "Fallback Reviewer",
      role: "Technical Expert",
      rating: 5,
      category: "industry",
    };

    const newItem = document.createElement("div");
    newItem.className = "testimonial-item animate-in";
    newItem.dataset.category = fallbackTestimonial.category;

    // Generate stars based on rating
    const ratingHTML = Array(5)
      .fill()
      .map(
        (_, i) =>
          `<i class="fas fa-star${
            i >= fallbackTestimonial.rating ? " far" : ""
          }"></i>`
      )
      .join("");

    newItem.innerHTML = `
      <div class="testimonial-content">
        <div class="quote"><i class="fas fa-quote-left"></i></div>
        <div class="rating">${ratingHTML}</div>
        <p>${fallbackTestimonial.text}</p>
        <div class="testimonial-author">
          <div class="author-img"><i class="fas fa-user-tie"></i></div>
          <div class="author-info">
            <h4>${fallbackTestimonial.author}</h4>
            <span>${fallbackTestimonial.role}</span>
          </div>
        </div>
      </div>
    `;
    sliderContainer.appendChild(newItem);
  }
}

// Add navigation to testimonials
function addTestimonialNavigation() {
  const sliderContainer = document.querySelector(".testimonials-slider");
  if (!sliderContainer) return;

  const testimonials = sliderContainer.querySelectorAll(".testimonial-item");

  // Remove existing navigation dots if any
  const existingNav = document.querySelector(".testimonial-nav");
  if (existingNav) {
    existingNav.remove();
  }

  // Add navigation dots
  const navDots = document.createElement("div");
  navDots.className = "testimonial-nav";

  testimonials.forEach((_, i) => {
    const dot = document.createElement("div");
    dot.className = i === 0 ? "nav-dot active" : "nav-dot";
    navDots.appendChild(dot);
  });

  // Append navigation dots to the navigation container
  const navContainer = document.querySelector(".testimonial-navigation");
  if (navContainer) {
    navContainer.appendChild(navDots);
  } else {
    // If no navigation container, append to slider
    sliderContainer.appendChild(navDots);
  }

  // Position the testimonial items
  testimonials.forEach((item) => {
    item.style.position = "absolute";
    item.style.top = "0";
    item.style.left = "0";
    item.style.width = "100%";
  });
}

// Initialize testimonial filters
function initTestimonialFilters() {
  const filters = document.querySelectorAll(".testimonial-filter");
  const testimonials = document.querySelectorAll(".testimonial-item");
  const slider = document.querySelector(".testimonials-slider");

  if (!filters.length || !testimonials.length || !slider) return;

  filters.forEach((filter) => {
    filter.addEventListener("click", () => {
      // Don't reapply if already active
      if (filter.classList.contains("active")) return;

      // Update active filter with animation
      filters.forEach((f) => {
        f.classList.remove("active", "animate-active");
      });

      // Add active class
      filter.classList.add("active");

      // Add and then remove animation class to trigger it
      filter.classList.add("animate-active");

      // Remove animation class after it completes
      setTimeout(() => {
        filter.classList.remove("animate-active");
      }, 400);

      const category = filter.dataset.filter;

      // SIMPLER APPROACH: First mark all as hidden
      testimonials.forEach((testimonial) => {
        testimonial.style.display = "none";
      });

      // Then collect and show only the visible ones
      const visibleTestimonials = Array.from(testimonials).filter(
        (testimonial) => {
          const isVisible =
            category === "all" || testimonial.dataset.category === category;

          if (isVisible) {
            testimonial.style.display = "block";
            return true;
          }
          return false;
        }
      );

      console.log(
        `Filter ${category}: Found ${visibleTestimonials.length} testimonials`
      );

      if (visibleTestimonials.length === 0) {
        console.warn("No testimonials match the selected filter");
        return;
      }

      // Update global array with our filtered testimonials
      window.filteredTestimonials = visibleTestimonials;

      // Reset current index when filtering
      currentIndex = 0;

      // Force correct positioning before updating
      visibleTestimonials.forEach((item) => {
        item.style.position = "absolute";
        item.style.top = "0";
        item.style.left = "0";
        item.style.width = "100%";
        item.style.opacity = "1"; // Make visible
      });

      // Update slider to position everything
      const mainSlider = document.querySelector(".testimonials-slider");
      if (mainSlider && mainSlider.updateSlider) {
        mainSlider.currentIndex = 0;
        mainSlider.updateSlider();

        // Trigger a bounce animation after a short delay
        setTimeout(() => {
          if (visibleTestimonials[0]) {
            visibleTestimonials[0].classList.add("bouncing");
            setTimeout(() => {
              visibleTestimonials[0].classList.remove("bouncing");
            }, 500);
          }
        }, 50);
      }

      // Update navigation dots
      updateNavigationDots(visibleTestimonials);

      // Ensure slider has proper height
      setTimeout(() => {
        slider.style.height = `${
          visibleTestimonials[0]?.offsetHeight || 300
        }px`;
      }, 100);
    });
  });
}

function updateNavigationDots(visibleTestimonials) {
  const navDots = document.querySelectorAll(".nav-dot");
  if (!navDots.length) return;

  // Show/hide dots based on visible testimonials
  navDots.forEach((dot, i) => {
    dot.style.display = i < visibleTestimonials.length ? "block" : "none";
    dot.classList.toggle("active", i === 0);
  });
}

// Make sure the function is called when the DOM is fully loaded
document.addEventListener("DOMContentLoaded", function () {
  initTestimonialSlider();
});
