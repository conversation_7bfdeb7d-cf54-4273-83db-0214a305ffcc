/* Testimonials Section Styling */

/* Custom properties needed for testimonials component */
:root {
  --bg-dark: #1e2024;
  --text-muted: #6c757d;
  --border-color: #e0e0e0;
  --border-color-dark: #444;
  --primary-rgb: 67, 97, 238;
  --bg-card-dark: #2a2d34;

  /* Missing variables that are used in the component */
  --bg-white: #ffffff;
  --primary: #4361ee;
  --primary-light: #6885ff;
  --primary-dark: #2545db;
  --text-light: #6c757d;
  --text-dark: #333333;
  --text-white: #ffffff;
  --card-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  --transition: all 0.3s ease;
  
  /* Default value for star animation delays (will be overridden by JS) */
  --i: 0;
}

/* Testimonials Section Styles */

.testimonials-section {
  padding: 100px 0;
  background-color: var(--bg-white);
  position: relative;
  overflow: hidden;
  perspective: 1000px;
}

.testimonials-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3z' fill='%234361ee' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 0.7;
  z-index: 0;
}

.testimonials-header {
  text-align: center;
  position: relative;
  z-index: 1;
  margin-bottom: 50px;
}

.testimonials-section .section-title {
  position: relative;
  display: inline-block;
  margin-bottom: 20px;
}

.testimonials-section .section-title::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 70px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-light), var(--primary));
  border-radius: 2px;
}

.testimonials-intro {
  max-width: 700px;
  margin: 0 auto 40px;
  color: var(--text-light);
  font-size: 1.1rem;
}

.testimonial-filters {
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
  margin-bottom: 50px;
  position: relative;
  z-index: 1;
  overflow-x: auto;
  padding-bottom: 5px;
  scrollbar-width: thin;
  scrollbar-color: var(--primary) transparent;
}

.testimonial-filters::-webkit-scrollbar {
  height: 3px;
}

.testimonial-filters::-webkit-scrollbar-thumb {
  background-color: var(--primary);
  border-radius: 2px;
}

.testimonial-filter {
  background: var(--bg-white);
  border: 1px solid rgba(0, 0, 0, 0.05);
  color: var(--text-light);
  padding: 10px 20px;
  border-radius: 30px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.testimonial-filter::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: rgba(67, 97, 238, 0.1);
  transition: transform 0.4s ease;
  z-index: -1;
}

.testimonial-filter:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.08);
  color: var(--primary);
}

.testimonial-filter:hover::before {
  transform: translateX(100%);
}

.testimonial-filter.active {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.25);
  transform: translateY(-3px);
}

.testimonials-slider {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
  min-height: 350px;
  perspective: 1000px;
}

.testimonial-item {
  margin: 20px 10px 50px;
  transform-style: preserve-3d;
  transition: transform 0.6s cubic-bezier(0.19, 1, 0.22, 1);
}

.testimonial-content {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  padding: 40px 30px;
  box-shadow: var(--card-shadow);
  position: relative;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid rgba(0, 0, 0, 0.03);
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

.testimonial-content::before {
  content: "";
  position: absolute;
  inset: 0;
  z-index: -1;
  border-radius: inherit;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(67, 97, 238, 0.05) 100%
  );
  opacity: 0;
  transition: opacity 0.4s ease;
}

.testimonial-content:hover {
  transform: translateY(-15px) rotateX(5deg);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.12);
}

.testimonial-content:hover::before {
  opacity: 1;
}

.quote {
  position: absolute;
  top: -20px;
  left: 30px;
  width: 45px;
  height: 45px;
  background: linear-gradient(135deg, var(--primary-light), var(--primary));
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  box-shadow: 0 8px 20px rgba(67, 97, 238, 0.25);
  transform: rotate(-5deg);
  transition: transform 0.3s ease;
}

.testimonial-content:hover .quote {
  transform: rotate(0deg) scale(1.1);
}

.rating {
  margin-bottom: 15px;
  color: #ffc107;
  display: flex;
  gap: 3px;
}

.rating i {
  filter: drop-shadow(0 2px 3px rgba(255, 193, 7, 0.2));
  transition: transform 0.3s ease;
}

.testimonial-content:hover .rating i {
  transform: scale(1.1);
  transition-delay: calc(0.05s * var(--i, 0)); /* Added fallback value */
}

.testimonial-content p {
  color: var(--text-light);
  font-size: 1.05rem;
  line-height: 1.7;
  margin-bottom: 25px;
  position: relative;
  font-style: italic;
}

.testimonial-content p::before,
.testimonial-content p::after {
  content: '"';
  color: var(--primary-light);
  font-size: 1.5em;
  line-height: 0;
  position: relative;
}

.testimonial-content p::before {
  vertical-align: super;
  margin-right: 2px;
}

.testimonial-content p::after {
  vertical-align: bottom;
  margin-left: 2px;
}

.testimonial-author {
  display: flex;
  align-items: center;
  margin-top: 25px;
  position: relative;
  transition: transform 0.3s ease;
}

.testimonial-content:hover .testimonial-author {
  transform: translateX(5px);
}

.author-img {
  width: 55px;
  height: 55px;
  border-radius: 12px;
  background: linear-gradient(
    135deg,
    var(--primary-light),
    var(--primary-dark)
  );
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 1.5rem;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.2);
  transform: rotate(-5deg);
  transition: transform 0.3s ease;
}

.testimonial-content:hover .author-img {
  transform: rotate(0deg);
}

.author-info {
  transition: transform 0.3s ease;
}

.author-info h4 {
  margin: 0;
  font-weight: 600;
  font-size: 1.1rem;
  color: var(--text-dark);
  margin-bottom: 5px;
}

.author-info span {
  font-size: 0.9rem;
  color: var(--text-light);
  display: inline-block;
  padding: 2px 10px;
  background: rgba(67, 97, 238, 0.08);
  border-radius: 20px;
  transform: translateX(-5px);
  opacity: 0.9;
  transition: all 0.3s ease;
}

.testimonial-content:hover .author-info span {
  transform: translateX(0);
  opacity: 1;
  background: rgba(67, 97, 238, 0.12);
}

.testimonial-navigation {
  text-align: center;
  position: relative;
  z-index: 1;
  margin-top: 20px;
}

.testimonial-arrows {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 20px;
}

.arrow-btn {
  width: 45px;
  height: 45px;
  border-radius: 12px;
  background: var(--bg-white);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  color: var(--text-dark);
  position: relative;
  overflow: hidden;
}

.arrow-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-light), var(--primary));
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.arrow-btn:hover {
  color: white;
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(67, 97, 238, 0.2);
}

.arrow-btn:hover::before {
  opacity: 1;
}

.testimonial-nav {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 10px;
}

.nav-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: rgba(67, 97, 238, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.nav-dot::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0);
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background: var(--primary);
  transition: transform 0.3s ease;
}

.nav-dot:hover {
  background: rgba(67, 97, 238, 0.4);
}

.nav-dot.active {
  width: 25px;
  border-radius: 5px;
  background: var(--primary);
}

.testimonial-cta {
  margin-top: 60px;
  text-align: center;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.5),
    rgba(255, 255, 255, 0.2)
  );
  backdrop-filter: blur(10px);
  padding: 30px;
  border-radius: var(--border-radius);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transform: translateY(0);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.testimonial-cta:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.testimonial-cta:hover::before {
  left: 100%;
}

.testimonial-cta p {
  margin-bottom: 20px;
  color: var(--text-dark);
  font-weight: 500;
  font-size: 1.1rem;
}

.btn-sm {
  padding: 10px 20px;
  font-size: 0.95rem;
  border-radius: 30px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.btn-outline::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--primary);
  transition: transform 0.4s ease;
  z-index: -1;
}

.btn-outline:hover {
  color: white;
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(67, 97, 238, 0.2);
}

.btn-outline:hover::before {
  transform: translateX(100%);
}

body.dark-mode .testimonial-cta {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.1);
}

body.dark-mode .testimonial-cta p {
  color: var(--text-white);
}

body.dark-mode .btn-outline {
  border-color: var(--primary-light);
  color: var(--primary-light);
  background: rgba(67, 97, 238, 0.15);
}

body.dark-mode .btn-outline:hover {
  background: var(--primary);
  color: white;
}

/* Animation classes for testimonial items */
.testimonial-item.active {
  opacity: 1;
  z-index: 2;
  transform: translateX(0) scale(1) rotateY(0);
}

.testimonial-item.prev {
  opacity: 0.4;
  transform: translateX(-50px) scale(0.85) rotateY(-5deg);
  z-index: 1;
  filter: blur(1px);
}

.testimonial-item.next {
  opacity: 0.4;
  transform: translateX(50px) scale(0.85) rotateY(5deg);
  z-index: 1;
  filter: blur(1px);
}

.testimonial-item.bouncing {
  animation: testimonial-bounce 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes testimonial-bounce {
  0% {
    transform: scale(0.95) translateY(10px);
  }
  50% {
    transform: scale(1.02) translateY(-5px);
  }
  100% {
    transform: scale(1) translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .testimonials-section {
    padding: 70px 0;
  }

  .testimonial-filters {
    padding-bottom: 10px;
    margin-bottom: 30px;
    overflow-x: auto;
    justify-content: flex-start;
    padding-left: 10px;
  }

  .testimonial-filter {
    padding: 8px 16px;
    font-size: 0.9rem;
  }

  .testimonial-content {
    padding: 30px 25px;
  }

  .testimonial-content p {
    font-size: 0.95rem;
  }

  .testimonial-navigation {
    margin-top: 20px;
  }

  .testimonial-cta {
    padding: 20px;
    margin-top: 40px;
  }
}

@media (max-width: 576px) {
  .testimonials-section {
    padding: 60px 0;
  }

  .testimonial-content {
    padding: 25px 20px;
  }

  .quote {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .author-img {
    width: 45px;
    height: 45px;
  }

  .arrow-btn {
    width: 40px;
    height: 40px;
  }
}
