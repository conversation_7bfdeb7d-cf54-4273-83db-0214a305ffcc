{"id": "ali-gad-ali-gad", "name": "<PERSON>", "role": "Mobile Developer", "team": "mobile", "contact": {"phone": "+201061121037", "email": "<EMAIL>"}, "bio": "<PERSON> is a mobile developer with expertise in cross-platform application development. He specializes in creating intuitive and performant mobile experiences.", "education": ["B.S. Software Engineering, Ain Shams University, 2022", "Mobile App Development Certification, 2021"], "skills": [{"name": "Flutter", "level": 90}, {"name": "Dart", "level": 85}, {"name": "React Native", "level": 80}, {"name": "Mobile UI Design", "level": 85}, {"name": "App Performance Optimization", "level": 75}], "projects": [{"name": "Cross-platform Mobile App", "description": "Developed a feature-rich cross-platform mobile application with offline capabilities and synchronization."}, {"name": "UI Animation Framework", "description": "Created a custom animation framework for mobile interfaces that improved user engagement metrics by 25%."}], "social": {"github": "https://github.com/AliGad17611", "linkedin": "https://www.linkedin.com/in/aligadali/", "twitter": "", "instagram": "", "facebook": ""}, "tasks": [{"title": "Implement Biometric Authentication", "description": "Add fingerprint and face recognition authentication options to enhance app security while maintaining a seamless user experience.", "status": "Completed", "startDate": "02/10/2025", "endDate": "02/20/2025", "hours": 25, "category": "Security", "icon": "fingerprint", "complexity": "Medium"}, {"title": "Optimize App Startup Time", "description": "Analyze and improve the app's cold start and warm start performance, reducing launch time by at least 30%.", "status": "In Progress", "startDate": "03/01/2025", "endDate": "03/10/2025", "hours": 20, "category": "Performance", "icon": "tachometer-alt", "complexity": "High"}, {"title": "Implement Offline-First Architecture", "description": "Design and implement a robust offline-first data synchronization system that allows users to work seamlessly without an internet connection.", "status": "In Progress", "startDate": "03/12/2025", "endDate": "03/25/2025", "hours": 35, "category": "Architecture", "icon": "cloud-download-alt", "complexity": "High"}, {"title": "Create Custom Animation Library", "description": "Develop a reusable animation library for common mobile UI interactions to ensure consistent and performant animations throughout the app.", "status": "Pending", "startDate": "03/28/2025", "endDate": "04/08/2025", "hours": 30, "category": "UI/UX", "icon": "film", "complexity": "Medium"}]}