// Completely rewritten Table of Contents functionality with enhanced mini-mode
function initTableOfContents() {
    console.log("Initializing Table of Contents...");
    const tocList = document.getElementById("tocList");
    const tocToggle = document.getElementById("tocToggle");
    const tocContainer = document.getElementById("tableOfContents");

    if (!tocList || !tocContainer) {
        console.error("TOC elements not found");
        return;
    }

    // Get all section headings
    const sections = document.querySelectorAll("section[id]");

    if (sections.length === 0) {
        console.warn("No sections found for TOC generation");
        tocContainer.style.display = "none";
        return;
    }

    // Check if mini-mode-toggle already exists
    const tocHeader = tocContainer.querySelector(".toc-header");
    let miniModeToggle = tocContainer.querySelector(".mini-mode-toggle");

    // Create mini-mode-toggle button only if it doesn't exist
    if (!miniModeToggle && tocHeader) {
        miniModeToggle = document.createElement("button");
        miniModeToggle.className = "mini-mode-toggle";
        miniModeToggle.innerHTML = '<i class="fas fa-compress-alt"></i>';
        miniModeToggle.setAttribute("aria-label", "Toggle compact TOC view");
        tocHeader.appendChild(miniModeToggle);
    }

    // Function to toggle mini mode with enhanced animation
    function toggleMiniMode(enableMiniMode = null) {
        // If enableMiniMode is null, toggle the current state
        // Otherwise, set to the specified state
        const shouldEnableMiniMode =
            enableMiniMode !== null
                ? enableMiniMode
                : !tocContainer.classList.contains("mini-mode");

        if (shouldEnableMiniMode) {
            // Transition to mini-mode
            tocContainer.classList.add("mini-mode");
            if (tocToggle) tocToggle.classList.add("collapsed");
            if (tocList) tocList.classList.add("collapsed");
        } else {
            // Transition to full mode
            tocContainer.classList.remove("mini-mode");
            if (tocToggle) tocToggle.classList.remove("collapsed");
            if (tocList) tocList.classList.remove("collapsed");
        }

        // Save preference
        localStorage.setItem(
            "toc-mini-mode",
            shouldEnableMiniMode ? "true" : "false"
        );
    }

    // Mini-mode toggle button handler
    if (miniModeToggle) {
        // Remove any existing event listeners to prevent duplicates
        const newMiniModeToggle = miniModeToggle.cloneNode(true);
        if (miniModeToggle.parentNode) {
            miniModeToggle.parentNode.replaceChild(newMiniModeToggle, miniModeToggle);
        }
        miniModeToggle = newMiniModeToggle;

        miniModeToggle.addEventListener("click", (e) => {
            e.stopPropagation();
            toggleMiniMode();
        });
    }

    // IMPROVED: Stronger click handling for mini-mode
    // Add click handler directly to the container element
    tocContainer.addEventListener("click", function (e) {
        if (this.classList.contains("mini-mode")) {
            // When in mini-mode, expand on any click except buttons inside
            if (!e.target.closest("button")) {
                toggleMiniMode(false); // Expand
                e.stopPropagation(); // Prevent other handlers
                e.preventDefault(); // Prevent default behavior
            }
        }
    });

    // Toggle list visibility when in normal mode
    if (tocToggle) {
        tocToggle.addEventListener("click", (e) => {
            e.stopPropagation();
            if (!tocContainer.classList.contains("mini-mode")) {
                tocToggle.classList.toggle("collapsed");
                tocList.classList.toggle("collapsed");
            }
        });
    }

    // Generate TOC items
    generateTocItems(sections, tocList);

    // Restore saved mini-mode state
    if (localStorage.getItem("toc-mini-mode") === "true") {
        toggleMiniMode(true);
    }

    // Auto-mini mode on mobile
    if (window.innerWidth < 768) {
        toggleMiniMode(true);
    }

    // Update active section on scroll
    initActiveSectionTracking();

    // Highlight section on initial load
    setTimeout(highlightActiveSectionInToc, 500);

    // Make TOC draggable for better UX on smaller screens
    makeTocDraggable();

    // Make TOC visible after initialization
    tocContainer.style.opacity = "1";
    console.log("TOC initialization complete");
}

// Initialize active section tracking
function initActiveSectionTracking() {
    // Use debounce if available, otherwise basic timeout-based approach
    if (typeof debounce === "function") {
        window.addEventListener(
            "scroll",
            debounce(highlightActiveSectionInToc, 100)
        );
    } else {
        let scrollTimeout;
        window.addEventListener("scroll", function () {
            if (scrollTimeout) {
                clearTimeout(scrollTimeout);
            }
            scrollTimeout = setTimeout(highlightActiveSectionInToc, 100);
        });
    }
}

// Generate TOC items with enhanced icons
function generateTocItems(sections, tocList) {
    // Clear existing items
    tocList.innerHTML = "";

    // Section icons mapping - enhanced with more specific icons
    const sectionIcons = {
        overview: "fa-info-circle",
        team: "fa-users",
        sprints: "fa-tasks",
        documentation: "fa-file-alt",
        gallery: "fa-images",
        testimonials: "fa-comment-alt",
        contact: "fa-envelope",
        progress: "fa-chart-line",
        roadmap: "fa-map-marked-alt",
        "tech-stack": "fa-layer-group",
        "sprint-progress": "fa-tasks",
    };

    sections.forEach((section) => {
        const id = section.getAttribute("id");
        if (!id) return; // Skip sections without ID

        const titleElement =
            section.querySelector("h2") || section.querySelector("h3");

        if (!titleElement) return;

        const title = titleElement.textContent;
        const icon = sectionIcons[id] || "fa-angle-right";

        const listItem = document.createElement("li");
        listItem.classList.add("toc-item");
        listItem.setAttribute("data-target", id);
        listItem.innerHTML = `
            <i class="fas ${icon}"></i>
            <span class="toc-text">${title}</span>
        `;

        listItem.addEventListener("click", (e) => {
            e.preventDefault();
            scrollToSection(id);
        });

        tocList.appendChild(listItem);
    });
}

// Make the TOC draggable for better mobile experience
function makeTocDraggable() {
    const tocContainer = document.getElementById("tableOfContents");
    if (!tocContainer || window.innerWidth > 992) return;

    let isDragging = false;
    let startX, startY, initialX, initialY;
    let offsetX = parseInt(localStorage.getItem("toc-offset-x")) || 0;
    let offsetY = parseInt(localStorage.getItem("toc-offset-y")) || 0;

    // Set initial position if saved before
    if (offsetX || offsetY) {
        tocContainer.style.transform = `translate(${offsetX}px, ${offsetY}px)`;
    }

    // Get maximum allowed dragX and dragY (to keep the TOC visible)
    const maxDragX = window.innerWidth - 70;
    const maxDragY = window.innerHeight - 70;

    const dragStart = (e) => {
        // Only allow dragging when in mini-mode
        if (!tocContainer.classList.contains("mini-mode")) return;

        // Get starting position
        if (e.type === "touchstart") {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        } else {
            startX = e.clientX;
            startY = e.clientY;
        }

        initialX = offsetX;
        initialY = offsetY;
        isDragging = true;

        // Add active class
        tocContainer.classList.add("dragging");
    };

    const drag = (e) => {
        if (!isDragging) return;
        e.preventDefault();

        let currentX, currentY;
        if (e.type === "touchmove") {
            currentX = e.touches[0].clientX;
            currentY = e.touches[0].clientY;
        } else {
            currentX = e.clientX;
            currentY = e.clientY;
        }

        offsetX = Math.min(
            Math.max(initialX + currentX - startX, -maxDragX),
            maxDragX
        );
        offsetY = Math.min(
            Math.max(initialY + currentY - startY, -maxDragY),
            maxDragY
        );

        tocContainer.style.transform = `translate(${offsetX}px, ${offsetY}px)`;
    };

    const dragEnd = () => {
        if (!isDragging) return;
        initialX = offsetX;
        initialY = offsetY;
        isDragging = false;

        // Remove active class
        tocContainer.classList.remove("dragging");

        // Save position
        localStorage.setItem("toc-offset-x", offsetX);
        localStorage.setItem("toc-offset-y", offsetY);
    };

    // Add listeners
    tocContainer.addEventListener("mousedown", dragStart);
    tocContainer.addEventListener("touchstart", dragStart, {passive: true});
    document.addEventListener("mousemove", drag);
    document.addEventListener("touchmove", drag, {passive: false});
    document.addEventListener("mouseup", dragEnd);
    document.addEventListener("touchend", dragEnd);
}

// Highlight active section in TOC with improved logic
function highlightActiveSectionInToc() {
    const tocItems = document.querySelectorAll(".toc-item");
    if (!tocItems.length) return;

    const scrollPosition = window.scrollY + 250;
    let currentSection = "";

    // Find current section
    document.querySelectorAll("section[id]").forEach((section) => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.clientHeight;

        if (
            scrollPosition >= sectionTop &&
            scrollPosition <= sectionTop + sectionHeight
        ) {
            currentSection = section.getAttribute("id");
        }
    });

    // If we're at the bottom of the page, use the last section
    if (
        !currentSection &&
        scrollPosition >=
        document.documentElement.scrollHeight - window.innerHeight - 10
    ) {
        const sections = document.querySelectorAll("section[id]");
        if (sections.length) {
            currentSection = sections[sections.length - 1].getAttribute("id");
        }
    }

    // Update active class
    document.querySelectorAll(".toc-item").forEach((item) => {
        item.classList.remove("active");
        if (item.getAttribute("data-target") === currentSection) {
            item.classList.add("active");

            // Ensure the active item is visible in the list
            const tocList = document.getElementById("tocList");
            if (tocList && !tocList.classList.contains("collapsed")) {
                // Only scroll into view if the list isn't hidden
                item.scrollIntoView({behavior: "smooth", block: "nearest"});
            }
        }
    });
}

// Enhanced scroll to section function
function scrollToSection(id) {
    const targetSection = document.getElementById(id);
    const tocContainer = document.getElementById("tableOfContents");

    if (!targetSection) return;

    // If in mini-mode, expand first
    if (tocContainer && tocContainer.classList.contains("mini-mode")) {
        tocContainer.classList.remove("mini-mode");

        // Small delay to allow animation
        setTimeout(() => {
            performScroll();
        }, 300);
    } else {
        performScroll();
    }

    function performScroll() {
        // Get header height for offset
        const header = document.querySelector("header");
        const headerHeight = header ? header.offsetHeight : 0;
        const topOffset = targetSection.offsetTop - headerHeight - 20;

        window.scrollTo({
            top: topOffset,
            behavior: "smooth",
        });

        // Highlight effect
        targetSection.classList.add("highlight-section");
        setTimeout(() => {
            targetSection.classList.remove("highlight-section");
        }, 1500);
    }

    // Add highlight effect styles
    if (!document.querySelector('style[data-id="highlight-styles"]')) {
        const style = document.createElement("style");
        style.setAttribute("data-id", "highlight-styles");
        style.textContent = `
        .highlight-section {
            animation: highlight-pulse 1.5s ease-out;
        }
        
        @keyframes highlight-pulse {
            0% { box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.4); }
            70% { box-shadow: 0 0 0 15px rgba(67, 97, 238, 0); }
            100% { box-shadow: 0 0 0 0 rgba(67, 97, 238, 0); }
        }
        
        .toc-container.dragging {
            opacity: 0.8;
            cursor: grabbing !important;
        }
        `;
        document.head.appendChild(style);
    }
}

// Auto-initialize TOC on page load
document.addEventListener("DOMContentLoaded", function () {
    // Check if we should initialize TOC (element exists)
    if (document.getElementById("tableOfContents")) {
        setTimeout(initTableOfContents, 100);
    }
});
