// roadmap.js - Include this at the bottom of your HTML or as a separate file
document.addEventListener('DOMContentLoaded', function () {
    initRoadmap();
});

function initRoadmap() {
    // Add animation for roadmap items
    const roadmapItems = document.querySelectorAll(".roadmap-item");
    const observer = new IntersectionObserver(
        (entries) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = 1;
                    entry.target.style.transform = "translateY(0)";
                    observer.unobserve(entry.target);
                }
            });
        },
        {
            threshold: 0.1,
        }
    );

    roadmapItems.forEach((item, index) => {
        item.style.opacity = 0;
        item.style.transform = "translateY(30px)";
        item.style.transition = `opacity 0.6s ease, transform 0.6s cubic-bezier(0.34, 1.56, 0.64, 1)`;
        item.style.transitionDelay = `${index * 0.15}s`;
        observer.observe(item);

        // Add hover effects to markers
        const marker = item.querySelector(".roadmap-marker");
        if (marker) {
            marker.addEventListener("mouseover", () => {
                marker.style.transform = "translateX(-50%) scale(1.2)";
            });
            marker.addEventListener("mouseout", () => {
                marker.style.transform = "translateX(-50%) scale(1)";
            });
        }
    });

    // Add progress line animation when in view
    const roadmapSection = document.getElementById("roadmap");
    const progressLine = document.querySelector(".roadmap-progress-line");
    const progressFill = document.querySelector(".progress-fill");
    if (progressLine && progressFill) {
        progressLine.style.height = "0%";
        progressFill.style.width = "0%";

        const progressObserver = new IntersectionObserver(
            (entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        setTimeout(() => {
                            progressLine.style.height = "67%";
                            progressFill.style.width = "67%";
                        }, 500);
                        progressObserver.unobserve(entry.target);
                    }
                });
            },
            {threshold: 0.2}
        );

        progressObserver.observe(roadmapSection);
    }

    // Add filter functionality
    const filterButtons = document.querySelectorAll(".roadmap-filter");
    filterButtons.forEach((button) => {
        button.addEventListener("click", () => {
            // Remove active class from all buttons
            filterButtons.forEach((btn) => btn.classList.remove("active"));

            // Add active class to clicked button
            button.classList.add("active");

            const filter = button.getAttribute("data-filter");

            // Filter roadmap items
            roadmapItems.forEach((item) => {
                if (filter === "all" || item.getAttribute("data-phase") === filter) {
                    item.style.display = "flex";
                    setTimeout(() => {
                        item.style.opacity = 1;
                        item.style.transform = "translateY(0)";
                    }, 100);
                } else {
                    item.style.opacity = 0;
                    item.style.transform = "translateY(20px)";
                    setTimeout(() => {
                        item.style.display = "none";
                    }, 500);
                }
            });
        });
    });

    // Initialize mini-progress bars
    const miniBars = document.querySelectorAll('.mini-bar');
    miniBars.forEach(bar => {
        // Get the initial width from the inline style and store it
        const targetWidth = bar.style.width;
        // Set to 0 first
        bar.style.width = '0';
        bar.style.transform = 'scaleX(0)';

        // Create an observer for each mini-bar
        const barObserver = new IntersectionObserver(
            (entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        // Delay slightly for effect
                        setTimeout(() => {
                            bar.style.width = targetWidth;
                            bar.style.transform = 'scaleX(1)';
                        }, 700);
                        barObserver.unobserve(entry.target);
                    }
                });
            },
            {threshold: 0.1}
        );

        barObserver.observe(bar);
    });
}