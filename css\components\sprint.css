/* Renamed from sprint-styles.css to match naming convention */
/* Sprint Progress Styles */
.sprint-progress-section {
    padding: 100px 0;
    background: var(--bg-white);
    position: relative;
    overflow: hidden;
}

.section-background-wave {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%234361ee10' fill-opacity='1' d='M0,288L48,272C96,256,192,224,288,213.3C384,203,480,213,576,229.3C672,245,768,267,864,261.3C960,256,1056,224,1152,208C1248,192,1344,192,1392,192L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C");
    background-repeat: no-repeat;
    background-size: cover;
    opacity: 0.6;
    z-index: 0;
    pointer-events: none;
}

.sprint-overview {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-bottom: 60px;
    justify-content: center;
}

.sprint-chart-container {
    flex: 1;
    min-width: 300px;
    max-width: 600px;
    height: 350px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: 25px;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.sprint-chart-container:hover {
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.chart-header h3 {
    margin: 0;
    font-size: 1.2rem;
    color: var(--text-dark);
}

.chart-controls select {
    padding: 5px 10px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background: white;
    font-size: 0.9rem;
}

.sprint-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 20px;
    flex: 1;
    min-width: 300px;
}

.metric-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: 25px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.metric-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary);
    opacity: 0.7;
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.highlight-card {
    background: linear-gradient(
            135deg,
            var(--primary-light) 0%,
            var(--primary) 100%
    );
    color: white;
}

.metric-card h3 {
    margin: 0 0 10px;
    font-size: 1rem;
    color: var(--text-light);
    font-weight: 500;
}

h3.highlight-title,
.highlight-card .metric-value {
    color: white;
}

.highlight-card::before {
    background: white;
}

.metric-icon {
    width: 50px;
    height: 50px;
    margin: 0 auto 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(67, 97, 238, 0.1);
    color: var(--primary);
    font-size: 1.2rem;
}

.highlight-card .metric-icon {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.pulse {
    animation: pulse 2s infinite;
}

.metric-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 15px;
    line-height: 1;
}

.metric-progress {
    margin-top: 10px;
}

.metric-progress-bar {
    height: 6px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 5px;
}

.metric-progress-fill {
    height: 100%;
    background: var(--primary);
    border-radius: 3px;
    transition: width 1.5s ease;
}

.highlight-card .metric-progress-fill {
    background: white;
}

.metric-progress-text {
    font-size: 0.85rem;
    color: var(--text-light);
}

.highlight-card .metric-progress-text {
    color: rgba(255, 255, 255, 0.9);
}

.velocity-trend {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 5px;
    font-size: 0.9rem;
}

.trend-icon {
    margin-right: 5px;
}

.trend-icon.positive {
    color: var(--success);
}

.trend-icon.negative {
    color: var(--danger);
}

.highlight-card .trend-icon.positive {
    color: rgba(255, 255, 255, 0.9);
}

.highlight-card .trend-value {
    font-weight: 600;
}

.sprint-details-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
    max-width: 1100px;
    margin: 0 auto;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.sprint-tabs {
    display: flex;
    overflow-x: auto;
    background: var(--bg-light);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    scrollbar-width: thin;
    scrollbar-color: var(--primary) transparent;
}

.sprint-tabs::-webkit-scrollbar {
    height: 3px;
}

.sprint-tabs::-webkit-scrollbar-thumb {
    background-color: var(--primary);
    border-radius: 2px;
}

.sprint-tab {
    padding: 15px 25px;
    background: transparent;
    border: none;
    cursor: pointer;
    font-weight: 500;
    color: var(--text-light);
    transition: all 0.3s ease;
    white-space: nowrap;
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
}

.tab-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.tab-status.completed {
    background: var(--success);
}

.tab-status.in-progress {
    background: var(--warning);
}

.tab-status.upcoming {
    background: var(--text-light);
}

.sprint-tab.active {
    background: white;
    color: var(--primary);
    font-weight: 600;
}

.sprint-tab.active::after {
    content: "";
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--primary);
    animation: slideIn 0.3s ease forwards;
}

@keyframes slideIn {
    from {
        transform: scaleX(0);
    }
    to {
        transform: scaleX(1);
    }
}

.sprint-tab:hover:not(.disabled) {
    background: rgba(255, 255, 255, 0.5);
    color: var(--primary-dark);
}

.sprint-tab.disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.sprint-detail {
    display: none;
    padding: 30px;
}

.sprint-detail.active {
    display: block;
    animation: fadeIn 0.5s ease;
}

.sprint-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding-bottom: 20px;
    margin-bottom: 25px;
}

.sprint-title-section {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-bottom: 15px;
    gap: 15px;
}

.sprint-title-section h3 {
    margin: 0;
    flex: 1;
    color: var(--text-dark);
    font-size: 1.5rem;
}

.sprint-badges {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.sprint-status {
    padding: 6px 14px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
}

.sprint-status.completed {
    background: rgba(76, 201, 240, 0.15);
    color: var(--success);
}

.sprint-status.in-progress {
    background: rgba(249, 199, 79, 0.15);
    color: var(--warning);
}

.sprint-timeframe {
    color: var(--text-light);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

.sprint-summary {
    color: var(--text-light);
    font-size: 1rem;
    line-height: 1.5;
    margin-top: 5px;
}

.sprint-stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.sprint-stat {
    background: var(--bg-light);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.03);
}

.sprint-stat:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

.highlight-stat {
    background: linear-gradient(
            135deg,
            rgba(76, 201, 240, 0.15) 0%,
            rgba(67, 97, 238, 0.15) 100%
    );
    border: 1px solid rgba(67, 97, 238, 0.1);
}

.sprint-stat .stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 8px;
    -webkit-text-fill-color: #4688ee;
}

.sprint-stat .stat-label {
    font-size: 0.9rem;
    color: var(--text-light);
    font-weight: 500;
}

.sprint-content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.task-distribution,
.key-achievements {
    background: var(--bg-light);
    border-radius: var(--border-radius);
    padding: 25px;
    border: 1px solid rgba(0, 0, 0, 0.03);
}

.task-distribution h4,
.key-achievements h4,
.sprint-comparison h4 {
    margin-top: 0;
    margin-bottom: 20px;
    color: var(--text-dark);
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.distribution-chart-container {
    height: 240px;
    margin-top: 15px;
}

.achievement-list {
    padding-left: 0;
    list-style: none;
}

.achievement-item {
    padding: 10px 0;
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--text-light);
    border-bottom: 1px dashed rgba(0, 0, 0, 0.05);
}

.achievement-item:last-child {
    border-bottom: none;
}

.sprint-documents {
    margin-top: 25px;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.document-link {
    background: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    color: var(--primary);
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.document-link:hover {
    background: var(--primary);
    color: white;
    transform: translateY(-2px);
}

.sprint-comparison {
    margin-top: 30px;
    background: var(--bg-light);
    border-radius: var(--border-radius);
    padding: 25px;
    border: 1px solid rgba(0, 0, 0, 0.03);
}

.comparison-chart {
    height: 300px;
}

/* Animation classes */
.animate-in {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.animate-in.visible {
    opacity: 1;
    transform: translateY(0);
}

.animate-delay-1 {
    transition-delay: 0.2s;
}

.animate-delay-2 {
    transition-delay: 0.4s;
}

.animate-delay-3 {
    transition-delay: 0.6s;
}

/* Sprint Health Dashboard */
.sprint-health {
    margin-top: 30px;
    background: var(--bg-light);
    border-radius: var(--border-radius);
    padding: 25px;
    border: 1px solid rgba(0, 0, 0, 0.03);
}

.health-metrics-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    gap: 15px;
    margin-top: 20px;
    margin-bottom: 30px;
}

.health-metric {
    min-width: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: transform 0.3s ease;
}

.health-metric:hover {
    transform: translateY(-5px);
}

.metric-gauge {
    width: 120px;
    max-width: 100%;
    position: relative;
    margin-bottom: 10px;
}

.gauge {
    overflow: visible;
}

.gauge-bg {
    fill: none;
    stroke: rgba(0, 0, 0, 0.05);
    stroke-width: 10;
}

.gauge-fill {
    fill: none;
    stroke: var(--primary);
    stroke-width: 10;
    stroke-linecap: round;
    transform-origin: center;
    transform: rotate(-90deg);
    stroke-dasharray: 314;
    transition: stroke-dashoffset 1s ease;
}

.gauge-fill.quality {
    stroke: #4cc9f0;
}

.gauge-fill.velocity {
    stroke: #f25c54;
}

.gauge-fill.satisfaction {
    stroke: #72c497;
}

.gauge-value {
    font-size: 18px;
    font-weight: 700;
    fill: var(--text-dark);
}

.gauge-label {
    text-align: center;
    font-size: 0.9rem;
    color: var(--text-light);
    font-weight: 500;
}

.sprint-insights {
    background: white;
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--card-shadow);
    margin-top: 20px;
}

.insight-title {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 15px;
    font-size: 1rem;
}

.insights-list {
    padding-left: 0;
    list-style: none;
    margin: 0;
}

.insights-list li {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 0;
    color: var(--text-light);
    border-bottom: 1px dashed rgba(0, 0, 0, 0.05);
}

.insights-list li:last-child {
    border-bottom: none;
}

.insight-badge {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
}

.insight-badge.positive {
    background-color: rgba(114, 196, 151, 0.15);
    color: #72c497;
}

.insight-badge.neutral {
    background-color: rgba(76, 201, 240, 0.15);
    color: #4cc9f0;
}

.insight-badge.negative {
    background-color: rgba(242, 92, 84, 0.15);
    color: #f25c54;
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .sprint-content-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .sprint-stats-row {
        gap: 15px;
    }

    .sprint-stat {
        padding: 15px;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .sprint-title-section {
        flex-direction: column;
        align-items: flex-start;
    }

    .sprint-badges {
        width: 100%;
    }

    .sprint-detail {
        padding: 20px 15px;
    }

    .health-metrics-container {
        flex-direction: column;
        align-items: center;
    }

    .health-metric {
        width: 100%;
        max-width: 200px;
    }
}

@media (max-width: 576px) {
    .sprint-progress-section {
        padding: 60px 0;
    }

    .sprint-intro {
        margin-bottom: 30px;
    }

    .sprint-overview {
        margin-bottom: 40px;
    }

    .metric-card {
        padding: 20px;
    }
}
