{"id": "eh<PERSON>-mag<PERSON>-a<PERSON><PERSON><PERSON>-a<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON>", "role": "Embedded Systems Developer", "team": "embedded", "contact": {"phone": "+201006085975", "email": "ehab<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com"}, "bio": "<PERSON><PERSON> is an embedded systems developer with expertise in firmware development and hardware integration. He specializes in creating efficient solutions for resource-constrained environments.", "education": ["B.S. Electronics Engineering, Helwan University, 2022", "Embedded Systems Design Certification, 2021"], "skills": [{"name": "Embedded C", "level": 90}, {"name": "ARM Architecture", "level": 85}, {"name": "RTOS", "level": 80}, {"name": "Circuit Design", "level": 75}, {"name": "Debugging Tools", "level": 85}], "projects": [{"name": "Smart Home Control System", "description": "Developed a comprehensive smart home control system with multiple sensor integrations and remote management capabilities."}, {"name": "Low-Power Firmware", "description": "Created optimized low-power firmware for battery-operated IoT devices, extending battery life by 35%."}], "social": {"github": "https://github.com/EhabMagdyy/", "linkedin": "https://www.linkedin.com/in/ehabmagdyy/", "twitter": "", "instagram": "", "facebook": ""}}