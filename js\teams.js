/**
 * Team tabs functionality - Enhanced with modern UI interactions
 * Updated to load team members dynamically from JSON files
 */

document.addEventListener("DOMContentLoaded", async function () {
  const teamTabs = document.querySelectorAll(".team-tab");
  const teamContents = document.querySelectorAll(".team-content");
  const allTeamContent = document.getElementById("all-team");
  const allMembersContainer = document.querySelector(".all-members");

  if (!allMembersContainer) {
    console.error("All members container not found");
    return;
  }

  // Add background decoration elements
  addBackgroundElements();

  // Load team data from JSON files
  await loadTeamData();

  // Clone all team members to "All Members" tab
  function populateAllMembers() {
    // Clear previous content
    allMembersContainer.innerHTML = "";

    // Get all team members except those in the "all-team" section
    const memberCards = document.querySelectorAll(
      ".team-content:not(#all-team) .member-card"
    );

    // Clone each member card and add to the all members section
    memberCards.forEach((card, index) => {
      const clone = card.cloneNode(true);

      // Make sure click events work on clones by adding data attribute
      const parentId = card.closest(".team-content").id;
      clone.setAttribute("data-original-team", parentId);

      // Set animation delay based on index for staggered appearance
      clone.style.setProperty("--card-index", index);

      allMembersContainer.appendChild(clone);
    });
  }

  // Load team data from JSON files
  async function loadTeamData() {
    try {
      // Fetch the team index file
      const response = await fetch("/data/team/team-index.json");
      if (!response.ok) {
        throw new Error("Failed to load team index data");
      }

      const teamIndex = await response.json();

      // Process each team
      for (const team of teamIndex.teams) {
        // Find the team content container
        const teamContainer = document.getElementById(`${team.id}-team`);
        if (!teamContainer) {
          console.warn(`Team container for ${team.id} not found`);
          continue;
        }

        // Get the team members container
        const teamMembersContainer =
          teamContainer.querySelector(".team-members");
        if (!teamMembersContainer) {
          console.warn(`Team members container for ${team.id} not found`);
          continue;
        }

        // Clear existing content
        teamMembersContainer.innerHTML = "";

        // Load each team member
        for (const memberId of team.members) {
          try {
            const memberResponse = await fetch(
              `/data/team/${team.id}/${memberId}.json`
            );
            if (!memberResponse.ok) {
              console.warn(`Failed to load member data for ${memberId}`);
              continue;
            }

            const memberData = await memberResponse.json();

            // Create member card
            const memberCard = createMemberCard(memberData);
            teamMembersContainer.appendChild(memberCard);
          } catch (memberError) {
            console.error(`Error loading member ${memberId}:`, memberError);
          }
        }
      }

      // After all teams are loaded, populate the All Members tab
      populateAllMembers();
    } catch (error) {
      console.error("Error loading team data:", error);
    }
  }

  // Create a member card element from member data
  function createMemberCard(memberData) {
    const card = document.createElement("div");
    card.className = "member-card";
    card.setAttribute("data-member-id", memberData.id);

    card.innerHTML = `
      <div class="member-img-container">
        <div class="member-img"><i class="fas fa-user"></i></div>
        <div class="view-profile">
          <i class="fas fa-id-card"></i> View Profile
        </div>
      </div>
      <div class="member-info">
        <h3 class="member-name">${memberData.name}</h3>
        <div class="member-role">${memberData.role}</div>
        <div class="member-social">
          <a href="${memberData.social.github}" class="social-icon" target="_blank">
            <i class="fab fa-github"></i>
          </a>
          <a href="${memberData.social.linkedin}" class="social-icon" target="_blank">
            <i class="fab fa-linkedin"></i>
          </a>
          <a href="mailto:${memberData.contact.email}" class="social-icon">
            <i class="fas fa-envelope"></i>
          </a>
        </div>
      </div>
    `;

    return card;
  }

  // Add search functionality
  addTeamSearch();

  // Add member filtering with enhanced UI
  addMemberFiltering();

  // Enhance team filters with animations and counters
  enhanceTeamFilters();

  // Add background decoration elements
  function addBackgroundElements() {
    const teamsContainer = document.querySelector(".teams-container");

    if (!teamsContainer) return;

    // Create and append background circles
    const bgCircle1 = document.createElement("div");
    bgCircle1.className = "team-bg-circle team-bg-circle-1";

    const bgCircle2 = document.createElement("div");
    bgCircle2.className = "team-bg-circle team-bg-circle-2";

    teamsContainer.appendChild(bgCircle1);
    teamsContainer.appendChild(bgCircle2);
  }

  // Add search functionality for team members
  function addTeamSearch() {
    // Create search input
    const searchContainer = document.createElement("div");
    searchContainer.className = "team-search";
    searchContainer.innerHTML = `
            <i class="fas fa-search"></i>
            <input type="text" placeholder="Search team members..." aria-label="Search team members">
        `;

    // Add it before the filters in all-team
    const allTeamIntro = document.querySelector("#all-team .team-intro");
    if (allTeamIntro) {
      allTeamIntro.appendChild(searchContainer);

      // Add search functionality
      const searchInput = searchContainer.querySelector("input");
      searchInput.addEventListener(
        "input",
        debounceSearch(function () {
          const searchTerm = this.value.toLowerCase();
          const members = document.querySelectorAll("#all-team .member-card");

          members.forEach((member) => {
            const name = member
              .querySelector(".member-name")
              .textContent.toLowerCase();
            const role = member
              .querySelector(".member-role")
              .textContent.toLowerCase();

            if (name.includes(searchTerm) || role.includes(searchTerm)) {
              member.style.display = "block";
              setTimeout(() => {
                member.style.opacity = "1";
                member.style.transform = "translateY(0)";
              }, 10);
            } else {
              member.style.opacity = "0";
              member.style.transform = "translateY(20px)";
              setTimeout(() => {
                member.style.display = "none";
              }, 300);
            }
          });
        }, 300)
      );
    }
  }

  // Simple debounce function for search
  function debounceSearch(func, delay) {
    let timer;
    return function () {
      const context = this;
      const args = arguments;
      clearTimeout(timer);
      timer = setTimeout(() => {
        func.apply(context, args);
      }, delay);
    };
  }

  // Add enhanced member filtering
  function addMemberFiltering() {
    // Create filter UI
    const filterContainer = document.createElement("div");
    filterContainer.className = "member-filters";
    filterContainer.innerHTML = `
            <div class="filter-label">Filter by role</div>
            <div class="filter-buttons">
                <button class="filter-btn active" data-filter="all">All Roles</button>
                <button class="filter-btn" data-filter="lead">Team Leads</button>
                <button class="filter-btn" data-filter="developer">Developers</button>
                <button class="filter-btn" data-filter="designer">Designers</button>
                <button class="filter-btn" data-filter="engineer">Engineers</button>
            </div>
        `;

    // Add to the DOM
    const allTeamIntro = document.querySelector("#all-team .team-intro");
    if (allTeamIntro) {
      allTeamIntro.appendChild(filterContainer);

      // Add filter functionality with animations
      const filterButtons = document.querySelectorAll(".filter-btn");
      filterButtons.forEach((btn) => {
        btn.addEventListener("click", () => {
          // Toggle active state with animation
          filterButtons.forEach((b) => b.classList.remove("active"));
          btn.classList.add("active");

          // Animate the active button
          btn.animate(
            [
              { transform: "scale(0.95)" },
              { transform: "scale(1.05)" },
              { transform: "scale(1)" },
            ],
            {
              duration: 400,
              easing: "cubic-bezier(0.34, 1.56, 0.64, 1)",
            }
          );

          const filter = btn.getAttribute("data-filter");
          const members = document.querySelectorAll("#all-team .member-card");

          members.forEach((member, index) => {
            const role = member
              .querySelector(".member-role")
              .textContent.toLowerCase();

            if (filter === "all" || role.includes(filter)) {
              member.style.display = "block";
              setTimeout(() => {
                member.style.setProperty("--card-index", index);
                member.style.opacity = "1";
                member.style.transform = "translateY(0)";
              }, 10 * index); // Staggered appearance
            } else {
              member.style.opacity = "0";
              member.style.transform = "translateY(20px)";
              setTimeout(() => {
                member.style.display = "none";
              }, 300);
            }
          });
        });
      });
    }
  }

  // Enhance team filters with counters and improved interactions
  function enhanceTeamFilters() {
    // Add skills filtering
    const filterContainer = document.querySelector(".member-filters");
    if (!filterContainer) return;

    // Add a secondary filter row for skills
    const skillsFilter = document.createElement("div");
    skillsFilter.className = "skills-filter";
    skillsFilter.innerHTML = `
            <div class="filter-label">Filter by skills</div>
            <div class="filter-buttons skills-buttons">
                <button class="filter-btn active" data-skill="all">All Skills</button>
                <button class="filter-btn" data-skill="frontend">Frontend</button>
                <button class="filter-btn" data-skill="backend">Backend</button>
                <button class="filter-btn" data-skill="mobile">Mobile</button>
                <button class="filter-btn" data-skill="design">Design</button>
                <button class="filter-btn" data-skill="ai">AI/ML</button>
                <button class="filter-btn" data-skill="hardware">Hardware</button>
            </div>
        `;

    filterContainer.appendChild(skillsFilter);

    // Enhanced filter functionality with multiple filters
    const roleFilters = document.querySelectorAll(
      ".filter-buttons:not(.skills-buttons) .filter-btn"
    );
    const skillFilters = document.querySelectorAll(
      ".skills-buttons .filter-btn"
    );
    let currentRoleFilter = "all";
    let currentSkillFilter = "all";

    function updateMembersVisibility() {
      const members = document.querySelectorAll("#all-team .member-card");

      members.forEach((member, index) => {
        const role = member
          .querySelector(".member-role")
          .textContent.toLowerCase();

        // Infer skills from role and team
        const originalTeam = member.getAttribute("data-original-team");
        let memberSkills = [];

        if (originalTeam === "web-team") {
          memberSkills.push("frontend", "backend");
          if (role.includes("front")) memberSkills.push("frontend");
          if (role.includes("back")) memberSkills.push("backend");
          if (role.includes("design") || role.includes("ui"))
            memberSkills.push("design");
        } else if (originalTeam === "mobile-team") {
          memberSkills.push("mobile");
          if (role.includes("ios") || role.includes("android"))
            memberSkills.push("mobile");
          if (role.includes("design") || role.includes("ux"))
            memberSkills.push("design");
        } else if (originalTeam === "ai-team") {
          memberSkills.push("ai");
        } else if (originalTeam === "embedded-team") {
          memberSkills.push("hardware");
        }

        const roleMatch =
          currentRoleFilter === "all" || role.includes(currentRoleFilter);
        const skillMatch =
          currentSkillFilter === "all" ||
          memberSkills.includes(currentSkillFilter);

        if (roleMatch && skillMatch) {
          member.style.display = "block";
          setTimeout(() => {
            member.style.opacity = "1";
            member.style.transform = "translateY(0)";
          }, 10 * index); // Staggered animation
        } else {
          member.style.opacity = "0";
          member.style.transform = "translateY(20px)";
          setTimeout(() => {
            member.style.display = "none";
          }, 300);
        }
      });
    }

    roleFilters.forEach((btn) => {
      btn.addEventListener("click", () => {
        roleFilters.forEach((b) => b.classList.remove("active"));
        btn.classList.add("active");

        // Animate button
        btn.animate(
          [
            { transform: "scale(0.95)" },
            { transform: "scale(1.05)" },
            { transform: "scale(1)" },
          ],
          {
            duration: 400,
            easing: "cubic-bezier(0.34, 1.56, 0.64, 1)",
          }
        );

        currentRoleFilter = btn.getAttribute("data-filter");
        updateMembersVisibility();
      });
    });

    skillFilters.forEach((btn) => {
      btn.addEventListener("click", () => {
        skillFilters.forEach((b) => b.classList.remove("active"));
        btn.classList.add("active");

        // Animate button
        btn.animate(
          [
            { transform: "scale(0.95)" },
            { transform: "scale(1.05)" },
            { transform: "scale(1)" },
          ],
          {
            duration: 400,
            easing: "cubic-bezier(0.34, 1.56, 0.64, 1)",
          }
        );

        currentSkillFilter = btn.getAttribute("data-skill");
        updateMembersVisibility();
      });
    });

    // Add visual counters to show how many team members match each filter
    function updateFilterCounters() {
      const members = document.querySelectorAll("#all-team .member-card");
      const counts = {
        roles: {
          all: members.length,
          lead: 0,
          developer: 0,
          designer: 0,
          engineer: 0,
        },
        skills: {
          all: members.length,
          frontend: 0,
          backend: 0,
          mobile: 0,
          design: 0,
          ai: 0,
          hardware: 0,
        },
      };

      members.forEach((member) => {
        const role = member
          .querySelector(".member-role")
          .textContent.toLowerCase();
        const originalTeam = member.getAttribute("data-original-team");

        // Count roles
        if (role.includes("lead")) counts.roles.lead++;
        if (role.includes("develop")) counts.roles.developer++;
        if (role.includes("design") || role.includes("ui"))
          counts.roles.designer++;
        if (role.includes("engineer")) counts.roles.engineer++;

        // Count skills based on team and role
        if (originalTeam === "web-team") {
          if (role.includes("front")) counts.skills.frontend++;
          else if (role.includes("back")) counts.skills.backend++;
          else if (role.includes("full")) {
            counts.skills.frontend++;
            counts.skills.backend++;
          } else {
            counts.skills.frontend++;
          }

          if (role.includes("design") || role.includes("ui"))
            counts.skills.design++;
        } else if (originalTeam === "mobile-team") {
          counts.skills.mobile++;
        } else if (originalTeam === "ai-team") {
          counts.skills.ai++;
        } else if (originalTeam === "embedded-team") {
          counts.skills.hardware++;
        }
      });

      // Update role filter buttons with counters
      roleFilters.forEach((btn) => {
        const filter = btn.getAttribute("data-filter");
        if (counts.roles[filter] !== undefined) {
          const counter = document.createElement("span");
          counter.className = "filter-counter";
          counter.textContent = counts.roles[filter];

          // Remove existing counter if any
          const existingCounter = btn.querySelector(".filter-counter");
          if (existingCounter) btn.removeChild(existingCounter);

          btn.appendChild(counter);
        }
      });

      // Update skill filter buttons with counters
      skillFilters.forEach((btn) => {
        const skill = btn.getAttribute("data-skill");
        if (counts.skills[skill] !== undefined) {
          const counter = document.createElement("span");
          counter.className = "filter-counter";
          counter.textContent = counts.skills[skill];

          // Remove existing counter if any
          const existingCounter = btn.querySelector(".filter-counter");
          if (existingCounter) btn.removeChild(existingCounter);

          btn.appendChild(counter);
        }
      });
    }

    // Initialize counters
    updateFilterCounters();
  }

  // Add mousemove parallax effect to team cards
  function addCardParallaxEffect() {
    const cards = document.querySelectorAll(".member-card");

    cards.forEach((card) => {
      card.addEventListener("mousemove", (e) => {
        const rect = card.getBoundingClientRect();
        const x = e.clientX - rect.left; // x position within the element
        const y = e.clientY - rect.top; // y position within the element

        const centerX = rect.width / 2;
        const centerY = rect.height / 2;

        const rotateY = ((x - centerX) / centerX) * 5; // -5 to 5 degrees
        const rotateX = ((centerY - y) / centerY) * 5; // -5 to 5 degrees

        card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
      });

      card.addEventListener("mouseleave", () => {
        card.style.transform = "";
      });
    });
  }

  // Initialize parallax effect
  setTimeout(addCardParallaxEffect, 1000);

  // Tab switching functionality with enhanced animations
  teamTabs.forEach((tab) => {
    tab.addEventListener("click", () => {
      // Remove active class from all tabs
      teamTabs.forEach((t) => t.classList.remove("active"));

      // Add active class to clicked tab with ripple effect
      tab.classList.add("active");

      // Create ripple effect
      const ripple = document.createElement("span");
      ripple.className = "tab-ripple";
      ripple.style.position = "absolute";
      ripple.style.top = "0";
      ripple.style.left = "0";
      ripple.style.width = "100%";
      ripple.style.height = "100%";
      ripple.style.background =
        "radial-gradient(circle, rgba(255,255,255,0.7) 0%, rgba(255,255,255,0) 70%)";
      ripple.style.borderRadius = "inherit";
      ripple.style.transform = "scale(0)";
      ripple.style.opacity = "1";
      ripple.style.pointerEvents = "none";
      ripple.style.transition =
        "transform 0.5s ease-out, opacity 0.5s ease-out";

      tab.appendChild(ripple);

      setTimeout(() => {
        ripple.style.transform = "scale(3)";
        ripple.style.opacity = "0";
      }, 10);

      setTimeout(() => {
        ripple.remove();
      }, 500);

      // Hide all team content sections with fade out
      teamContents.forEach((content) => {
        content.style.opacity = "0";
        content.style.transform = "translateY(20px)";
        setTimeout(() => {
          content.classList.remove("active");
          content.style.opacity = "";
          content.style.transform = "";
        }, 300);
      });

      // Show the selected team content with fade in
      const teamName = tab.dataset.team;
      const targetContent = document.getElementById(teamName + "-team");
      if (targetContent) {
        setTimeout(() => {
          targetContent.classList.add("active");
        }, 310);
      }
    });
  });

  // Make member cards in the "All" tab clickable with enhanced feedback
  document.addEventListener("click", function (e) {
    const card = e.target.closest(".member-card");
    if (!card) return;

    // Add click feedback animation
    card.animate([{ transform: "scale(0.95)" }, { transform: "scale(1)" }], {
      duration: 300,
      easing: "cubic-bezier(0.34, 1.56, 0.64, 1)",
    });

    // Get member information
    const memberName = card.querySelector(".member-name").textContent;
    const memberRole = card.querySelector(".member-role").textContent;

    // Generate member ID from name (for URL)
    const memberId = memberName.toLowerCase().replace(/\s+/g, "-");

    // Get original team or current team context
    const originalTeam =
      card.getAttribute("data-original-team") ||
      card.closest(".team-content")?.id ||
      "team-member";

    // Create the member profile page URL
    window.location.href = `memberProfile.html?id=${memberId}&name=${encodeURIComponent(
      memberName
    )}&role=${encodeURIComponent(memberRole)}&team=${originalTeam}`;
  });
});
