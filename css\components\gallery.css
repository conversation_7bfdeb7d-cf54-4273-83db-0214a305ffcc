/* Gallery Section Styles */

.gallery-section {
  padding: 60px 0;
  background-color: var(--bg-light);
}

.gallery-intro {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 30px;
  color: var(--text-light);
}

.gallery-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
  gap: 10px;
  flex-wrap: wrap;
}

.gallery-tab {
  padding: 8px 20px;
  background-color: var(--bg-white);
  border-radius: 30px;
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.95rem;
  font-weight: 500;
  color: var(--text-light);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.gallery-tab:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  color: var(--primary);
}

.gallery-tab.active {
  background: var(--primary);
  color: white;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.2);
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 25px;
  margin-top: 20px;
}

.gallery-item {
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--card-shadow);
  background-color: var(--bg-white);
  transition: var(--transition);
  transform: translateY(0);
  position: relative;
}

.gallery-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.gallery-img-container {
  height: 0;
  padding-bottom: 66.67%; /* 3:2 aspect ratio */
  position: relative;
  overflow: hidden;
  background-color: #f5f5f5;
}

.gallery-img-container img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.gallery-item:hover .gallery-img-container img {
  transform: scale(1.1);
}

.gallery-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7) 0%, transparent 100%);
  opacity: 0;
  transition: var(--transition);
  display: flex;
  align-items: flex-end;
  padding: 20px;
}

.gallery-item:hover .gallery-overlay {
  opacity: 1;
}

.gallery-actions {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.gallery-zoom {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  backdrop-filter: blur(2px);
  transition: var(--transition);
}

.gallery-zoom:hover {
  background: var(--primary);
  transform: scale(1.1);
}

.gallery-title {
  color: white;
  font-weight: 500;
  font-size: 1.1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Loading and error states */
.gallery-img-container.loading::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 30px;
  height: 30px;
  margin: -15px 0 0 -15px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top-color: var(--primary);
  border-radius: 50%;
  animation: spin 1s ease-in-out infinite;
  z-index: 2;
}

.gallery-img-container.error::after {
  content: "\f071";
  font-family: "Font Awesome 5 Free", serif;
  font-weight: 900;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 2rem;
  color: var(--danger);
  z-index: 2;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
  }

  .gallery-overlay {
    opacity: 1;
    background: linear-gradient(
      to top,
      rgba(0, 0, 0, 0.6) 0%,
      transparent 100%
    );
  }

  .gallery-zoom {
    width: 35px;
    height: 35px;
    font-size: 0.9rem;
  }

  .gallery-title {
    font-size: 1rem;
  }
}
