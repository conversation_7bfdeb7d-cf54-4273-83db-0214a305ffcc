// sprint-progress.js
function enhanceSprintProgress() {
    // Get the sprint progress section
    const sprintProgressSection = document.getElementById("sprint-progress");

    if (!sprintProgressSection) {
        console.error("Sprint progress section not found");
        return;
    }

    // Position handling - keep the existing positioning logic
    const roadmapSection = document.getElementById("roadmap");
    if (
        roadmapSection &&
        sprintProgressSection.parentNode !== roadmapSection.parentNode
    ) {
        roadmapSection.parentNode.insertBefore(
            sprintProgressSection,
            roadmapSection.nextSibling
        );
    } else {
        const sprintsSection = document.getElementById("sprints");
        if (
            sprintsSection &&
            sprintProgressSection.parentNode !== sprintsSection.parentNode
        ) {
            sprintsSection.parentNode.insertBefore(
                sprintProgressSection,
                sprintsSection.nextSibling
            );
        }
    }

    // Initialize tab functionality with enhanced transitions
    const sprintTabs = document.querySelectorAll(".sprint-tab:not(.disabled)");
    const sprintDetails = document.querySelectorAll(".sprint-detail");

    sprintTabs.forEach((tab) => {
        tab.addEventListener("click", () => {
            // Remove active class from all tabs and details
            sprintTabs.forEach((t) => t.classList.remove("active"));
            sprintDetails.forEach((d) => d.classList.remove("active"));

            // Add active class to clicked tab and corresponding detail
            tab.classList.add("active");
            const sprintId = tab.getAttribute("data-sprint");
            const detailElement = document.getElementById(`sprint-${sprintId}`);

            // Add fade-in animation
            detailElement.style.opacity = "0";
            detailElement.classList.add("active");

            setTimeout(() => {
                detailElement.style.opacity = "1";
            }, 50);
        });
    });

    // Initialize animations
    initializeAnimations();

    // Initialize counters
    initializeCounters();

    // Initialize tilt effect for cards
    initializeTilt();

    // Initialize charts
    initializeCharts();

    // Initialize sprint health gauges
    initializeSprintHealthGauges();

    // Add chart type toggle functionality
    const chartTypeSelector = document.getElementById("burndownChartType");
    if (chartTypeSelector) {
        chartTypeSelector.addEventListener("change", function () {
            updateBurndownChart(this.value);
        });
    }
}

function initializeAnimations() {
    // Intersection Observer for animations
    const animatedElements = document.querySelectorAll(".animate-in");

    const observer = new IntersectionObserver(
        (entries) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    entry.target.classList.add("visible");
                    observer.unobserve(entry.target);
                }
            });
        },
        {
            root: null,
            threshold: 0.1,
            rootMargin: "0px 0px -100px 0px",
        }
    );

    animatedElements.forEach((element) => {
        observer.observe(element);
    });
}

function initializeCounters() {
    const counters = document.querySelectorAll(".counter");

    counters.forEach((counter) => {
        const target = parseInt(counter.getAttribute("data-target"));
        const increment = target / 20; // Adjust speed by changing divider
        let current = 0;

        const updateCounter = () => {
            if (current < target) {
                current += increment;
                if (current > target) current = target;

                // Handle special cases like percentages or fractions
                if (counter.innerText.includes("/")) {
                    // For formats like "12/12"
                    const parts = counter.innerText.split("/");
                    counter.innerText = `${Math.ceil(current)}/${parts[1]}`;
                } else if (counter.innerText.includes("%")) {
                    // For percentage values
                    counter.innerText = `${Math.ceil(current)}%`;
                } else {
                    // For regular numbers
                    counter.innerText = Math.ceil(current);
                }

                setTimeout(updateCounter, 50);
            }
        };

        // Start counting when the element is in view
        const observer = new IntersectionObserver(
            (entries) => {
                if (entries[0].isIntersecting) {
                    updateCounter();
                    observer.unobserve(entries[0].target);
                }
            },
            {threshold: 0.5}
        );

        observer.observe(counter);
    });
}

function initializeTilt() {
    // Load vanilla-tilt.js if not already loaded
    if (typeof VanillaTilt === "undefined") {
        const script = document.createElement("script");
        script.src =
            "https://cdnjs.cloudflare.com/ajax/libs/vanilla-tilt/1.7.2/vanilla-tilt.min.js";
        script.onload = () => {
            applyTiltEffect();
        };
        document.head.appendChild(script);
    } else {
        applyTiltEffect();
    }
}

function applyTiltEffect() {
    if (typeof VanillaTilt !== "undefined") {
        VanillaTilt.init(document.querySelectorAll("[data-tilt]"), {
            max: 5,
            speed: 400,
            glare: false,
        });
    }
}

function initializeCharts() {
    // Load Chart.js if needed
    if (typeof Chart === "undefined") {
        loadChartJS().then(() => {
            createBurndownChart();
            createTaskDistributionCharts();
            // Removed createComparisonCharts() call
        });
    } else {
        createBurndownChart();
        createTaskDistributionCharts();
        // Removed createComparisonCharts() call
    }
}

function loadChartJS() {
    return new Promise((resolve, reject) => {
        if (typeof Chart !== "undefined") {
            resolve();
            return;
        }

        const script = document.createElement("script");
        script.src =
            "https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js";
        script.integrity =
            "sha512-ElRFoEQdI5Ht6kZvyzXhcG0QP9gCfwv8rhQvx3Lngr+LHpUwB0XgvhEHJ8kT/+6lCf5V4VQfTRGWP2EQj+1IQ==";
        script.crossOrigin = "anonymous";
        script.referrerPolicy = "no-referrer";

        script.onload = () => resolve();
        script.onerror = () => reject(new Error("Failed to load Chart.js"));

        document.head.appendChild(script);
    });
}

// Store chart references to allow updates
let burndownChart = null;

function createBurndownChart() {
    const ctx = document.getElementById("sprintBurndownChart");
    if (!ctx) return;

    // Enhanced data for burndown chart
    const labels = [
        "Day 1",
        "Day 2",
        "Day 3",
        "Day 4",
        "Day 5",
        "Day 6",
        "Day 7",
        "Day 8",
        "Day 9",
        "Day 10",
    ];
    const idealData = [80, 72, 64, 56, 48, 40, 32, 24, 16, 8, 0];
    const actualData = [80, 76, 72, 65, 58, 52, 48, 40, 30, 15, 0];
    const projectedData = [null, null, null, null, 58, 51, 43, 34, 24, 12, 0];

    // Set gradient background
    const gradient = ctx.getContext("2d").createLinearGradient(0, 0, 0, 300);
    gradient.addColorStop(0, "rgba(67, 97, 238, 0.3)");
    gradient.addColorStop(1, "rgba(67, 97, 238, 0.0)");

    burndownChart = new Chart(ctx, {
        type: "line",
        data: {
            labels: labels,
            datasets: [
                {
                    label: "Ideal Burndown",
                    data: idealData,
                    borderColor: "rgba(0, 0, 0, 0.2)",
                    borderDash: [5, 5],
                    borderWidth: 2,
                    pointRadius: 0,
                    fill: false,
                    tension: 0.1,
                },
                {
                    label: "Actual Burndown",
                    data: actualData,
                    borderColor: "var(--primary)",
                    backgroundColor: gradient,
                    borderWidth: 2,
                    pointBackgroundColor: "var(--primary)",
                    pointBorderColor: "#fff",
                    pointRadius: 4,
                    pointHoverRadius: 6,
                    tension: 0.3,
                    fill: true,
                },
                {
                    label: "Projected",
                    data: projectedData,
                    borderColor: "rgba(67, 97, 238, 0.5)",
                    borderDash: [3, 3],
                    borderWidth: 2,
                    pointRadius: 2,
                    fill: false,
                    tension: 0.3,
                },
            ],
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: "index",
                intersect: false,
            },
            plugins: {
                title: {
                    display: false,
                },
                legend: {
                    position: "bottom",
                    labels: {
                        usePointStyle: true,
                        padding: 15,
                    },
                },
                tooltip: {
                    backgroundColor: "rgba(255, 255, 255, 0.9)",
                    titleColor: "#333",
                    bodyColor: "#666",
                    borderColor: "rgba(0, 0, 0, 0.1)",
                    borderWidth: 1,
                    padding: 10,
                    boxPadding: 5,
                    usePointStyle: true,
                    callbacks: {
                        title: function (tooltipItems) {
                            return tooltipItems[0].label;
                        },
                        label: function (context) {
                            let label = context.dataset.label || "";
                            if (label) {
                                label += ": ";
                            }
                            if (context.parsed.y !== null) {
                                label += context.parsed.y + " points";
                            }
                            return label;
                        },
                    },
                },
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: "Remaining Story Points",
                    },
                    grid: {
                        color: "rgba(0, 0, 0, 0.05)",
                    },
                },
                x: {
                    title: {
                        display: true,
                        text: "Sprint Days",
                    },
                    grid: {
                        color: "rgba(0, 0, 0, 0.05)",
                        display: false,
                    },
                },
            },
        },
    });
}

function updateBurndownChart(chartType) {
    if (!burndownChart) return;

    if (chartType === "velocity") {
        // Velocity data
        const velocityData = {
            labels: ["Sprint 1", "Sprint 2", "Sprint 3", "Sprint 4"],
            datasets: [
                {
                    label: "Team Velocity",
                    data: [32, 38, 35, 42],
                    borderColor: "var(--primary)",
                    backgroundColor: "rgba(67, 97, 238, 0.2)",
                    borderWidth: 2,
                    pointBackgroundColor: "var(--primary)",
                    pointBorderColor: "#fff",
                    fill: true,
                    tension: 0.3,
                },
                {
                    label: "Planned Capacity",
                    data: [30, 35, 40, 40],
                    borderColor: "rgba(0, 0, 0, 0.2)",
                    borderDash: [5, 5],
                    pointRadius: 0,
                    fill: false,
                },
            ],
        };

        burndownChart.data = velocityData;
        burndownChart.options.scales.y.title.text = "Story Points";
        burndownChart.options.scales.x.title.text = "Sprints";
    } else {
        // Return to original burndown data
        const labels = [
            "Day 1",
            "Day 2",
            "Day 3",
            "Day 4",
            "Day 5",
            "Day 6",
            "Day 7",
            "Day 8",
            "Day 9",
            "Day 10",
        ];
        const idealData = [80, 72, 64, 56, 48, 40, 32, 24, 16, 8, 0];
        const actualData = [80, 76, 72, 65, 58, 52, 48, 40, 30, 15, 0];
        const projectedData = [null, null, null, null, 58, 51, 43, 34, 24, 12, 0];

        burndownChart.data.labels = labels;
        burndownChart.data.datasets = [
            {
                label: "Ideal Burndown",
                data: idealData,
                borderColor: "rgba(0, 0, 0, 0.2)",
                borderDash: [5, 5],
                borderWidth: 2,
                pointRadius: 0,
                fill: false,
                tension: 0.1,
            },
            {
                label: "Actual Burndown",
                data: actualData,
                borderColor: "var(--primary)",
                backgroundColor: burndownChart.data.datasets[1].backgroundColor,
                borderWidth: 2,
                pointBackgroundColor: "var(--primary)",
                pointBorderColor: "#fff",
                pointRadius: 4,
                pointHoverRadius: 6,
                tension: 0.3,
                fill: true,
            },
            {
                label: "Projected",
                data: projectedData,
                borderColor: "rgba(67, 97, 238, 0.5)",
                borderDash: [3, 3],
                borderWidth: 2,
                pointRadius: 2,
                fill: false,
                tension: 0.3,
            },
        ];

        burndownChart.options.scales.y.title.text = "Remaining Story Points";
        burndownChart.options.scales.x.title.text = "Sprint Days";
    }

    burndownChart.update();
}

function createTaskDistributionCharts() {
    // Create task distribution charts for each sprint
    for (let i = 1; i <= 4; i++) {
        const ctx = document.getElementById(`taskDistChart${i}`);
        if (!ctx) continue;

        // Customize data for each sprint
        const data = {
            labels: ["UI/UX", "Backend", "Testing", "DevOps", "Documentation"],
            datasets: [
                {
                    data:
                        i === 1
                            ? [35, 25, 20, 10, 10]
                            : i === 2
                                ? [30, 30, 25, 8, 7]
                                : i === 3
                                    ? [25, 35, 25, 10, 5]
                                    : [20, 30, 30, 15, 5],
                    backgroundColor: [
                        "rgba(67, 97, 238, 0.8)",
                        "rgba(76, 201, 240, 0.8)",
                        "rgba(242, 92, 84, 0.8)",
                        "rgba(249, 199, 79, 0.8)",
                        "rgba(114, 196, 151, 0.8)",
                    ],
                    borderColor: "#fff",
                    borderWidth: 2,
                },
            ],
        };

        new Chart(ctx, {
            type: "doughnut",
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: "65%",
                plugins: {
                    legend: {
                        position: "right",
                        labels: {
                            usePointStyle: true,
                            padding: 15,
                            font: {
                                size: 11,
                            },
                        },
                    },
                    tooltip: {
                        backgroundColor: "rgba(255, 255, 255, 0.9)",
                        titleColor: "#333",
                        bodyColor: "#666",
                        borderColor: "rgba(0, 0, 0, 0.1)",
                        borderWidth: 1,
                        padding: 10,
                        boxPadding: 5,
                        usePointStyle: true,
                        callbacks: {
                            label: function (context) {
                                const label = context.label || "";
                                const value = context.parsed || 0;
                                return `${label}: ${value}%`;
                            },
                        },
                    },
                },
                animation: {
                    animateScale: true,
                    animateRotate: true,
                },
            },
        });
    }
}

function initializeSprintHealthGauges() {
    // Find all gauge elements
    const gauges = document.querySelectorAll(".gauge-fill");

    // Set a timeout to animate the gauges after page load
    setTimeout(() => {
        gauges.forEach((gauge) => {
            // Get current stroke-dashoffset from style attribute
            const currentOffset = parseFloat(gauge.style.strokeDashoffset || "0");

            // Create an animation to smoothly transition from full circle to target value
            gauge.style.strokeDashoffset = "314"; // Start with full circle (no progress)

            // Trigger browser reflow
            gauge.getBoundingClientRect();

            // Animate to the target value
            gauge.style.strokeDashoffset = currentOffset.toString();
        });
    }, 500);
}

// Run the function when the DOM is fully loaded
document.addEventListener("DOMContentLoaded", function () {
    enhanceSprintProgress();
});
