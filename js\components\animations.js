/**
 * Animation Handler
 * Adds 'visible' class to elements with 'animate-in' class when they enter the viewport
 */

document.addEventListener("DOMContentLoaded", function () {
  // Function to check if element is in viewport
  function isInViewport(element) {
    const rect = element.getBoundingClientRect();
    return (
      rect.top <=
        (window.innerHeight || document.documentElement.clientHeight) * 0.85 &&
      rect.bottom >= 0
    );
  }

  // Get all elements with 'animate-in' class
  const animatedElements = document.querySelectorAll(".animate-in");

  // Function to check elements and add 'visible' class
  function checkElements() {
    animatedElements.forEach((element) => {
      if (isInViewport(element)) {
        element.classList.add("visible");
        element.style.animationPlayState = "running";
      }
    });
  }

  // Run on load
  checkElements();

  // Run on scroll
  window.addEventListener("scroll", checkElements);

  // Run on resize
  window.addEventListener("resize", checkElements);

  // Force check after page is fully loaded (for elements with images)
  window.addEventListener("load", checkElements);
});
