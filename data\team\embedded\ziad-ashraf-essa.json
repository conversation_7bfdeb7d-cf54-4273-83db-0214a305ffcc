{"id": "ziad-ash<PERSON><PERSON>-essa", "name": "<PERSON><PERSON><PERSON>", "role": "Embedded Systems Engineer", "team": "embedded", "contact": {"phone": "+201094439340", "email": "<EMAIL>"}, "bio": "<PERSON><PERSON><PERSON> is an embedded systems engineer with a focus on IoT applications and sensor integration. He has experience in developing firmware for various microcontroller platforms.", "education": ["B.S. Electronics Engineering, Cairo University, 2022", "IoT Systems Design Certification, 2021"], "skills": [{"name": "Embedded C/C++", "level": 90}, {"name": "IoT Protocols", "level": 85}, {"name": "Sensor Integration", "level": 90}, {"name": "Wireless Communication", "level": 80}, {"name": "Firmware Development", "level": 85}], "projects": [{"name": "Wireless Sensor Network", "description": "Developed a comprehensive wireless sensor network for environmental monitoring with low power consumption."}, {"name": "Real-time Data Processing System", "description": "Created a real-time data processing system for sensor data with efficient memory management."}], "social": {"github": "https://github.com/ZiiaadAshraf", "linkedin": "https://www.linkedin.com/in/ziiaadashraf", "twitter": "", "instagram": "", "facebook": ""}, "tasks": [{"title": "Develop Low-Power Sensor Module", "description": "Design and implement an ultra-low-power sensor module for environmental monitoring with extended battery life of at least 1 year.", "status": "Completed", "startDate": "02/05/2025", "endDate": "02/25/2025", "hours": 50, "category": "Hardware", "icon": "microchip", "complexity": "High"}, {"title": "Implement Wireless Communication Protocol", "description": "Develop a custom wireless communication protocol optimized for low-power, long-range transmission of sensor data in challenging environments.", "status": "Completed", "startDate": "03/01/2025", "endDate": "03/15/2025", "hours": 35, "category": "Communication", "icon": "wifi", "complexity": "Medium"}, {"title": "Create Firmware Update System", "description": "Design and implement a secure over-the-air firmware update system for deployed IoT devices with failsafe recovery mechanisms.", "status": "In Progress", "startDate": "03/18/2025", "endDate": "03/30/2025", "hours": 30, "category": "Firmware", "icon": "code", "complexity": "High"}, {"title": "Optimize Power Management System", "description": "Analyze and optimize the power management system to extend battery life by implementing advanced sleep modes and intelligent wake-up strategies.", "status": "Pending", "startDate": "04/02/2025", "endDate": "04/15/2025", "hours": 25, "category": "Power Management", "icon": "battery-half", "complexity": "Medium"}]}