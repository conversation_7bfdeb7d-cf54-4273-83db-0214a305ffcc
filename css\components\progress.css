/* ====== BASE SECTION STYLES ====== */
.progress-section {
  padding: 80px 0;
  background-color: var(--bg-light);
  position: relative;
  overflow: hidden;
}

/* Background decorations */
.progress-bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  overflow: hidden;
}

.progress-shape {
  position: absolute;
  border-radius: 50%;
  opacity: 0.05;
  background: linear-gradient(135deg, var(--primary-light), var(--primary));
}

.shape-1 {
  width: 400px;
  height: 400px;
  top: -150px;
  left: -100px;
  animation: float 15s ease-in-out infinite;
}

.shape-2 {
  width: 300px;
  height: 300px;
  bottom: -100px;
  right: -50px;
  animation: float 18s ease-in-out infinite reverse;
}

.shape-3 {
  width: 200px;
  height: 200px;
  top: 40%;
  right: 15%;
  animation: float 12s ease-in-out infinite;
}

.progress-section .container {
  position: relative;
  z-index: 1;
}

.progress-intro {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 40px;
  color: var(--text-light);
  font-size: 1.1rem;
  line-height: 1.6;
}

.progress-dashboard {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* ====== OVERALL PROGRESS CARD ====== */
.overall-progress {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95),
    rgba(255, 255, 255, 0.85)
  );
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(67, 97, 238, 0.05);
  border-radius: 16px;
  position: relative;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.overall-progress:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6px;
  background: linear-gradient(
    to right,
    var(--primary-light),
    var(--primary),
    var(--secondary)
  );
  z-index: 2;
}

.overall-progress:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%234361ee' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 0.5;
  z-index: 0;
}

.overall-progress:hover {
  transform: translateY(-10px) scale(1.01);
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.12), 0 0 0 2px rgba(67, 97, 238, 0.08);
}

.progress-content-wrapper {
  display: flex;
  align-items: center;
  gap: 40px;
  flex-wrap: wrap;
  position: relative;
  z-index: 1;
  padding: 40px;
}

/* Progress Text Content */
.progress-text {
  flex: 1;
  min-width: 300px;
  position: relative;
}

.progress-text h3 {
  font-size: 1.8rem;
  margin-bottom: 20px;
  color: var(--text-dark);
  position: relative;
  display: inline-block;
  font-weight: 700;
  letter-spacing: -0.5px;
}

.progress-text h3:after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 80px;
  height: 4px;
  background: linear-gradient(
    to right,
    var(--primary),
    var(--primary-light),
    var(--secondary)
  );
  border-radius: 4px;
}

.progress-text h3 .highlight {
  color: var(--primary);
  position: relative;
  display: inline-block;
}

.progress-text h3 .highlight:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 8px;
  background: rgba(67, 97, 238, 0.1);
  z-index: -1;
  border-radius: 4px;
}

.progress-description {
  color: var(--text-light);
  margin-bottom: 25px;
  line-height: 1.7;
  font-size: 1.05rem;
  position: relative;
  padding: 5px 5px 5px 20px;
  border-left: 3px solid rgba(67, 97, 238, 0.2);
}

/* Progress Milestones */
.progress-milestones {
  display: flex;
  margin-top: 30px;
  position: relative;
  padding: 0;
  flex-wrap: wrap;
}

.progress-milestones:before {
  content: "";
  position: absolute;
  top: 10px;
  left: 15px;
  width: calc(100% - 30px);
  height: 2px;
  background: rgba(0, 0, 0, 0.05);
  z-index: 1;
}

.milestone {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  flex: 1;
  min-width: 80px;
  margin-bottom: 15px;
}

.milestone-dot {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #f0f0f0;
  border: 2px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 10px;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.milestone.completed .milestone-dot {
  background-color: var(--success);
  border-color: var(--success);
  box-shadow: 0 0 0 4px rgba(76, 201, 240, 0.2);
}

.milestone.active .milestone-dot {
  background-color: var(--primary);
  border-color: var(--primary);
  box-shadow: 0 0 0 4px rgba(67, 97, 238, 0.2);
}

.milestone.completed .milestone-dot:after {
  content: "\f00c";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  color: white;
  font-size: 10px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.milestone.active .milestone-dot:after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: white;
  animation: pulse 2s infinite;
}

.milestone-label {
  font-size: 0.9rem;
  color: var(--text-light);
  text-align: center;
  transition: all 0.3s ease;
  font-weight: 500;
  max-width: 100px;
}

.milestone.completed .milestone-label {
  color: var(--success);
}

.milestone.active .milestone-label {
  color: var(--primary);
  font-weight: 600;
}

/* Circular Progress Visual */
.progress-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  position: relative;
}

.progress-visual:before {
  content: "";
  position: absolute;
  width: 220px;
  height: 220px;
  background: radial-gradient(
    circle,
    rgba(67, 97, 238, 0.08) 0%,
    rgba(76, 201, 240, 0) 70%
  );
  border-radius: 50%;
  z-index: 0;
  animation: pulse-glow 3s ease-in-out infinite alternate;
}

.circular-progress {
  position: relative;
  width: 200px;
  height: 200px;
  z-index: 1;
}

.circular-progress:before,
.circular-progress:after {
  content: "";
  position: absolute;
  border-radius: 50%;
  z-index: -1;
}

.circular-progress:before {
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: linear-gradient(
    135deg,
    rgba(67, 97, 238, 0.05),
    rgba(76, 201, 240, 0.05)
  );
  animation: rotate 10s linear infinite;
}

.circular-progress:after {
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border: 2px dashed rgba(67, 97, 238, 0.15);
  animation: rotate-reverse 20s linear infinite;
}

.progress-ring-circle-bg {
  fill: transparent;
  stroke: rgba(0, 0, 0, 0.03);
  stroke-width: 15px;
}

.progress-ring-circle {
  fill: transparent;
  stroke: url(#progressGradient);
  stroke-width: 15px;
  stroke-linecap: round;
  transform: rotate(-90deg);
  transform-origin: 50% 50%;
  transition: stroke-dashoffset 2s cubic-bezier(0.34, 1.56, 0.64, 1);
  filter: drop-shadow(0 0 8px rgba(67, 97, 238, 0.6));
}

.progress-value {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-weight: 700;
  color: var(--text-dark);
}

.percentage-value {
  font-size: 3.2rem;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text; /* Standard property for modern browsers */
  color: transparent; /* Fallback */
  line-height: 1;
  margin-bottom: 5px;
  position: relative;
}

.percentage-value:after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 40px;
  height: 3px;
  background: linear-gradient(to right, var(--primary-light), transparent);
  transform: translateX(-50%);
  border-radius: 3px;
}

.percentage-symbol {
  font-size: 1.2rem;
  margin-left: 2px;
  color: var(--primary);
  vertical-align: super;
}

.progress-label {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-top: 5px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-weight: 500;
}

/* ====== PROGRESS CARDS ====== */
.progress-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

.progress-card {
  background: var(--bg-white);
  border-radius: var(--border-radius);
  padding: 30px;
  box-shadow: var(--card-shadow);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.progress-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.card-decoration {
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  overflow: hidden;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  top: -50px;
  right: -50px;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: linear-gradient(
    135deg,
    rgba(67, 97, 238, 0.1),
    rgba(76, 201, 240, 0.1)
  );
}

.progress-header {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 20px;
  gap: 15px;
}

.progress-icon {
  width: 45px;
  height: 45px;
  border-radius: 12px;
  background: linear-gradient(
    135deg,
    rgba(67, 97, 238, 0.1),
    rgba(76, 201, 240, 0.1)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary);
  font-size: 1.2rem;
  margin-right: 5px;
  transition: all 0.3s ease;
}

.progress-card:hover .progress-icon {
  transform: scale(1.1) rotate(5deg);
  background: linear-gradient(
    135deg,
    rgba(67, 97, 238, 0.2),
    rgba(76, 201, 240, 0.2)
  );
}

.progress-header h3 {
  font-size: 1.3rem;
  color: var(--text-dark);
  margin: 0;
  flex: 1;
}

.progress-badge {
  background: linear-gradient(135deg, var(--primary-light), var(--primary));
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  box-shadow: 0 4px 10px rgba(67, 97, 238, 0.3);
}

/* ====== PROGRESS BARS ====== */
.progress-chart {
  margin-bottom: 25px;
}

.chart-bar {
  height: 12px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 6px;
  overflow: hidden;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-fill {
  height: 100%;
  background: linear-gradient(to right, var(--primary-light), var(--primary));
  border-radius: 6px;
  width: 0; /* Will be set via inline style */
  transform: scaleX(1);
  transform-origin: left;
  transition: transform 1.5s cubic-bezier(0.25, 1, 0.5, 1);
  box-shadow: 0 0 10px rgba(67, 97, 238, 0.3);
}

/* ====== PROGRESS DETAILS ====== */
.progress-details {
  list-style: none;
  padding: 0;
  margin: 20px 0 0;
}

.progress-details li {
  padding: 10px 0;
  display: flex;
  align-items: center;
  color: var(--text-light);
  border-bottom: 1px dashed rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.progress-details li:last-child {
  border-bottom: none;
}

.progress-details li i {
  margin-right: 12px;
  width: 20px;
  text-align: center;
}

.progress-details li.completed {
  color: var(--text-dark);
}

.progress-details li.completed i {
  color: var(--success);
}

.progress-details li.in-progress i {
  color: var(--warning);
}

.progress-details li.pending i {
  color: var(--text-light);
}

.progress-details li:hover {
  transform: translateX(5px);
  color: var(--text-dark);
}

/* ====== HARDWARE PROGRESS SECTION ====== */
.hardware-progress {
  margin-top: 10px;
  grid-column: 1 / -1;
}

.hardware-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
  margin-top: 25px;
}

.hardware-item {
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
  padding: 15px;
  border-radius: 10px;
}

.hardware-item:hover {
  background: rgba(67, 97, 238, 0.05);
  transform: translateY(-3px);
}

.hardware-icon {
  width: 50px;
  height: 50px;
  background: rgba(67, 97, 238, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary);
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.hardware-item:hover .hardware-icon {
  background: rgba(67, 97, 238, 0.2);
  transform: rotate(10deg);
}

.hardware-info {
  flex: 1;
}

.hardware-info h4 {
  margin: 0 0 10px;
  font-size: 1.1rem;
  color: var(--text-dark);
}

.mini-progress {
  height: 8px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  overflow: hidden;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 8px;
}

.mini-bar {
  height: 100%;
  background: linear-gradient(to right, var(--primary-light), var(--primary));
  border-radius: 4px;
  width: 0; /* Will be set via inline style */
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 1.5s cubic-bezier(0.25, 1, 0.5, 1);
}

.completion-text {
  font-size: 0.85rem;
  color: var(--text-light);
}

/* ====== TEAM VELOCITY SECTION ====== */
.team-velocity {
  grid-column: 1 / -1;
}

.velocity-metrics {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 20px;
}

.velocity-metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  transition: all 0.3s ease;
}

.velocity-circle {
  position: relative;
  width: 120px;
  height: 120px;
  margin-bottom: 15px;
}

.velocity-ring-bg {
  fill: transparent;
  stroke: rgba(0, 0, 0, 0.05);
  stroke-width: 8px;
}

.velocity-ring-fill {
  fill: transparent;
  stroke-width: 8px;
  stroke-linecap: round;
  transform: rotate(-90deg);
  transform-origin: 50% 50%;
  transition: stroke-dashoffset 2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.velocity-ring-fill.sprint {
  stroke: #4361ee;
}

.velocity-ring-fill.quality {
  stroke: #4cc9f0;
}

.velocity-ring-fill.efficiency {
  stroke: #72c497;
}

.velocity-value {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-dark);
}

.velocity-label {
  font-size: 1rem;
  color: var(--text-light);
  font-weight: 500;
  text-align: center;
}

/* ====== ANIMATIONS ====== */
@keyframes float {
  0% {
    transform: translate(0, 0) rotate(0deg);
  }
  50% {
    transform: translate(20px, 20px) rotate(5deg);
  }
  100% {
    transform: translate(0, 0) rotate(0deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(0.95);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.3;
  }
}

@keyframes pulse-glow {
  0% {
    transform: scale(0.95);
    opacity: 0.7;
  }
  100% {
    transform: scale(1.05);
    opacity: 0.9;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes rotate-reverse {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}

/* ====== RESPONSIVE STYLES ====== */
@media (max-width: 992px) {
  .progress-content-wrapper {
    flex-direction: column;
  }

  .progress-visual {
    order: -1;
    margin-bottom: 20px;
  }

  .velocity-metrics {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .progress-section {
    padding: 60px 0;
  }

  .progress-content-wrapper {
    flex-direction: column;
    padding: 25px 20px;
    gap: 20px;
  }

  .progress-text {
    min-width: 100%;
    order: 1;
  }

  .progress-text h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
  }

  .progress-description {
    font-size: 0.95rem;
    line-height: 1.5;
    padding-left: 15px;
  }

  .progress-visual {
    order: 0;
    padding: 10px;
    width: 100%;
  }

  .circular-progress,
  .progress-ring {
    width: 180px;
    height: 180px;
    margin: 0 auto;
  }

  .percentage-value {
    font-size: 2.8rem;
  }

  .percentage-symbol {
    font-size: 1rem;
  }

  .progress-label {
    font-size: 0.8rem;
  }

  .progress-card {
    padding: 25px;
  }

  .progress-grid {
    grid-template-columns: 1fr;
  }

  .progress-badge {
    padding: 5px 10px;
    font-size: 0.8rem;
  }

  .progress-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .progress-milestones {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-top: 20px;
  }

  .progress-milestones:before {
    display: none;
  }

  .milestone {
    flex: none;
    margin-bottom: 0;
  }

  .milestone-dot {
    margin-bottom: 8px;
  }

  .milestone-label {
    font-size: 0.85rem;
  }

  .hardware-details {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 576px) {
  .progress-content-wrapper {
    padding: 20px 15px;
  }

  .progress-text h3 {
    font-size: 1.3rem;
  }

  .progress-description {
    font-size: 0.9rem;
    padding-left: 10px;
  }

  .circular-progress,
  .progress-ring,
  .progress-ring[width="200"][height="200"] {
    width: 150px;
    height: 150px;
  }

  .percentage-value {
    font-size: 2.4rem;
  }

  .percentage-symbol {
    font-size: 0.9rem;
  }

  .progress-label {
    font-size: 0.75rem;
  }
}

/* Animation classes for scroll animations */
[data-aos] {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

[data-aos].aos-animate {
  opacity: 1;
  transform: translateY(0);
}

[data-aos-delay="100"] {
  transition-delay: 0.1s;
}
[data-aos-delay="200"] {
  transition-delay: 0.2s;
}
[data-aos-delay="300"] {
  transition-delay: 0.3s;
}
[data-aos-delay="400"] {
  transition-delay: 0.4s;
}
[data-aos-delay="500"] {
  transition-delay: 0.5s;
}
[data-aos-delay="600"] {
  transition-delay: 0.6s;
}
[data-aos-delay="700"] {
  transition-delay: 0.7s;
}
[data-aos-delay="800"] {
  transition-delay: 0.8s;
}
