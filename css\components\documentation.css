/* Documentation Section Styles */

.documentation-section {
  padding: 70px 0;
  background-color: var(--bg-light);
}

.doc-intro {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 40px;
  color: var(--text-light);
}

.doc-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.doc-card {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  padding: 35px 25px;
  box-shadow: var(--card-shadow);
  transition: var(--transition);
  border: 1px solid rgba(0, 0, 0, 0.03);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.doc-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.doc-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, var(--primary-light), var(--primary));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.5s ease;
}

.doc-card:hover::before {
  transform: scaleX(1);
}

.doc-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-light), var(--primary));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  margin-bottom: 20px;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.2);
  transition: all 0.5s ease;
}

.doc-card:hover .doc-icon {
  transform: scale(1.1) rotate(10deg);
}

.doc-title {
  font-size: 1.3rem;
  color: var(--text-dark);
  margin-bottom: 15px;
  transition: var(--transition);
}

.doc-desc {
  color: var(--text-light);
  font-size: 0.95rem;
  line-height: 1.6;
  margin-bottom: 25px;
  flex-grow: 1;
}

.doc-link {
  display: inline-flex;
  align-items: center;
  color: var(--primary);
  font-weight: 500;
  font-size: 0.95rem;
  transition: var(--transition);
  text-decoration: none;
  margin-top: auto;
}

.doc-link i {
  margin-left: 5px;
  transition: transform 0.3s ease;
}

.doc-link:hover {
  color: var(--primary-dark);
}

.doc-link:hover i {
  transform: translateX(5px);
}

/* Document sections styling */
.doc-section {
  margin-bottom: 50px;
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  padding: 30px;
  box-shadow: var(--card-shadow);
}

.doc-section-title {
  font-size: 1.5rem;
  margin-bottom: 20px;
  color: var(--text-dark);
  position: relative;
  padding-bottom: 10px;
}

.doc-section-title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background: var(--primary);
}

.doc-content {
  color: var(--text-light);
  line-height: 1.7;
}

.doc-content h4 {
  margin-top: 25px;
  margin-bottom: 15px;
  color: var(--text-dark);
}

.doc-content ul {
  padding-left: 20px;
  margin-bottom: 20px;
}

.doc-content ul li {
  margin-bottom: 10px;
  position: relative;
}

.doc-content ul li::before {
  content: "•";
  color: var(--primary);
  font-weight: bold;
  display: inline-block;
  width: 1em;
  margin-left: -1em;
}

.doc-content code {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 0.9em;
  color: var(--primary-dark);
}

.doc-content pre {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 5px;
  overflow-x: auto;
  margin: 20px 0;
}

.doc-content pre code {
  background-color: transparent;
  padding: 0;
  color: inherit;
}

.doc-content blockquote {
  border-left: 4px solid var(--primary-light);
  padding-left: 15px;
  color: var(--text-light);
  font-style: italic;
  margin: 20px 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .documentation-section {
    padding: 50px 0;
  }

  .doc-cards {
    gap: 20px;
  }

  .doc-card {
    padding: 25px 20px;
  }

  .doc-icon {
    width: 50px;
    height: 50px;
    font-size: 1.3rem;
  }

  .doc-title {
    font-size: 1.2rem;
  }

  .doc-section {
    padding: 25px 20px;
  }
}
