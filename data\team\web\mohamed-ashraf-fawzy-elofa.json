{"id": "mohamed-ashraf-fawzy-elofa", "name": "<PERSON>", "role": "Backend Developer", "team": "web", "contact": {"phone": "+201151342667", "email": "<EMAIL>"}, "bio": "<PERSON> is a backend developer with expertise in server-side architecture and database management. He specializes in building scalable and secure web applications.", "education": ["B.S. Computer Science, Mansoura University, 2022", "Database Administration Certification, 2021"], "skills": [{"name": "Node.js", "level": 85}, {"name": "Express.js", "level": 90}, {"name": "MongoDB", "level": 85}, {"name": "SQL", "level": 80}, {"name": "API Design", "level": 90}], "projects": [{"name": "Authentication System", "description": "Designed and implemented a secure authentication system with multi-factor authentication and role-based access control."}, {"name": "Data Migration Tool", "description": "Developed a robust data migration tool that successfully transferred and transformed complex datasets with zero data loss."}], "social": {"github": "https://github.com/mohamedelofa", "linkedin": "https://www.linkedin.com/in/mohamedelofa", "twitter": "", "instagram": "", "facebook": ""}}