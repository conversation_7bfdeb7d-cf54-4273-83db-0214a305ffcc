/* Contact section styles */
.contact-section {
  padding: 60px 0;
  background-color: var(--bg-light);
}

.contact-intro {
  text-align: center;
  max-width: 700px;
  margin: 0 auto 40px;
  color: var(--text-light);
}

.contact-container {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 40px;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.contact-card {
  background: var(--bg-white);
  border-radius: var(--border-radius);
  padding: 25px;
  box-shadow: var(--card-shadow);
  transition: var(--transition);
}

.contact-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.contact-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--primary-light), var(--primary));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  color: white;
  font-size: 1.2rem;
}

.contact-card h3 {
  font-size: 1.2rem;
  margin-bottom: 10px;
  color: var(--text-dark);
}

.contact-card p {
  color: var(--text-light);
  line-height: 1.6;
  margin-bottom: 5px;
}

.contact-form-container {
  background: var(--bg-white);
  border-radius: var(--border-radius);
  padding: 30px;
  box-shadow: var(--card-shadow);
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-dark);
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--border-radius);
  background: var(--bg-light);
  color: var(--text-dark);
  transition: var(--transition);
}

.form-group input:focus,
.form-group textarea:focus {
  border-color: var(--primary-light);
  outline: none;
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

.submit-btn {
  background: linear-gradient(135deg, var(--primary-light), var(--primary));
  color: white;
  border: none;
  border-radius: 30px;
  padding: 12px 30px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: var(--transition);
}

.submit-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(67, 97, 238, 0.2);
}

/* Responsive adjustments for contact section */
@media (max-width: 992px) {
  .contact-container {
    grid-template-columns: 1fr;
  }
}
