{"id": "reda-elsayed-mohammed-ahmed", "name": "<PERSON><PERSON>", "role": "Backend Developer", "team": "web", "contact": {"phone": "+201065086328", "email": "<EMAIL>"}, "bio": "<PERSON><PERSON> is a backend developer with expertise in building scalable server-side .Net applications. He specializes in database design and API development.", "education": ["B.S. Computer Science, Alexandria University, 2022", "Web Development Bootcamp, 2021"], "skills": [{"name": "Asp.Net", "level": 90}, {"name": "MongoDB", "level": 80}, {"name": "SQL", "level": 85}, {"name": "RESTful APIs", "level": 90}], "projects": [{"name": "API Gateway Implementation", "description": "Designed and implemented a robust API gateway for microservices architecture with authentication and rate limiting."}, {"name": "Database Optimization", "description": "Optimized database queries and schema design, resulting in 50% improvement in query performance."}], "social": {"github": "https://github.com/redaelsayied", "linkedin": "https://www.linkedin.com/in/redaelsayed/", "twitter": "", "instagram": "", "facebook": ""}, "tasks": [{"title": "Implement Authentication Microservice", "description": "Design and develop a secure authentication microservice with JWT token support, role-based access control, and multi-factor authentication capabilities.", "status": "Completed", "startDate": "02/15/2025", "endDate": "03/01/2025", "hours": 40, "category": "Backend", "icon": "lock", "complexity": "High"}, {"title": "Optimize Database Queries", "description": "Analyze and optimize critical database queries to improve application performance, implementing indexing strategies and query refactoring.", "status": "Completed", "startDate": "03/05/2025", "endDate": "03/12/2025", "hours": 25, "category": "Database", "icon": "database", "complexity": "Medium"}, {"title": "Implement Real-time Notification System", "description": "Create a scalable real-time notification system using WebSockets to deliver instant updates to connected clients.", "status": "In Progress", "startDate": "03/15/2025", "endDate": "03/25/2025", "hours": 30, "category": "Backend", "icon": "bell", "complexity": "Medium"}, {"title": "Design Caching Strategy", "description": "Develop and implement a comprehensive caching strategy using Redis to reduce database load and improve response times for frequently accessed data.", "status": "Pending", "startDate": "03/28/2025", "endDate": "04/05/2025", "hours": 20, "category": "Performance", "icon": "server", "complexity": "High"}]}