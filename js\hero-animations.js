/**
 * Enhanced Hero Animations
 * Modern, interactive, and beautifully animated hero section
 */

document.addEventListener("DOMContentLoaded", function () {
  // Initialize all hero animations and effects
  initHeroAnimations();
});

function initHeroAnimations() {
  // Initialize counter animation for stat numbers
  initCounterAnimation();

  // Initialize 3D parallax effect on hero shapes
  initParallaxEffect();

  // Make scroll indicator functional with ripple effect
  initScrollIndicator();

  // Initialize tilt effect on stat cards
  initTiltEffect();

  // Initialize particle effects for stat icons
  initIconParticles();

  // Initialize entrance animations
  initEntranceAnimations();
}

// Animate count up for statistics with celebration effect
function initCounterAnimation() {
  const statNumbers = document.querySelectorAll(".stat-number[data-counter]");

  function startCounting(entry) {
    if (entry.isIntersecting) {
      const element = entry.target;
      const target = parseInt(element.getAttribute("data-counter"));
      animateCounter(element, target);
    }
  }

  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach(startCounting);
    },
    {
      threshold: 0.5,
    }
  );

  statNumbers.forEach((number) => {
    observer.observe(number);
  });
}

function animateCounter(element, target) {
  let start = 0;
  const duration = 2500; // 2.5 seconds
  const startTime = performance.now();

  function updateCounter(currentTime) {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);

    // Use easeOutExpo for smoother counting
    const easedProgress = progress === 1 ? 1 : 1 - Math.pow(2, -10 * progress);
    start = Math.floor(easedProgress * target);

    element.innerText = start;

    if (start < target) {
      requestAnimationFrame(updateCounter);
    } else {
      // Add celebration effect when counter reaches target
      element.classList.add("counter-complete");
      element.style.animation = "number-celebrate 0.5s ease-out";

      // Create particle burst effect
      createCounterParticles(element);
    }
  }

  requestAnimationFrame(updateCounter);
}

// Create particle burst effect when counter completes
function createCounterParticles(element) {
  const rect = element.getBoundingClientRect();
  const centerX = rect.left + rect.width / 2;
  const centerY = rect.top + rect.height / 2;

  for (let i = 0; i < 12; i++) {
    // Reduced particle count for cleaner effect
    const particle = document.createElement("div");
    particle.className = "counter-particle";

    // Refined particle styling for more elegant look
    const size = Math.random() * 5 + 3;
    const angle = Math.random() * Math.PI * 2;
    const distance = Math.random() * 50 + 20;
    const duration = Math.random() * 1.2 + 0.8;

    // Use brand colors from CSS variables if possible
    const hue = Math.random() * 30 + 220; // More consistent blue palette

    particle.style.width = `${size}px`;
    particle.style.height = `${size}px`;
    particle.style.background = `hsl(${hue}, 85%, 65%)`;
    particle.style.borderRadius = "50%";
    particle.style.position = "fixed";
    particle.style.zIndex = "9999";
    particle.style.opacity = "0.85";
    particle.style.pointerEvents = "none";
    particle.style.boxShadow = "0 0 6px rgba(255, 255, 255, 0.3)";

    // Set initial position at the center of the counter
    particle.style.left = `${centerX}px`;
    particle.style.top = `${centerY}px`;

    // Calculate end position
    const endX = centerX + Math.cos(angle) * distance;
    const endY = centerY + Math.sin(angle) * distance;

    // Add to document
    document.body.appendChild(particle);

    // Animate with GSAP if available, otherwise use basic animation
    if (typeof gsap !== "undefined") {
      gsap.to(particle, {
        x: endX - centerX,
        y: endY - centerY,
        opacity: 0,
        scale: 0.5,
        duration: duration,
        ease: "power2.out",
        onComplete: () => particle.remove(),
      });
    } else {
      // Fallback to basic CSS animation
      particle.style.transition = `all ${duration}s ease-out`;
      setTimeout(() => {
        particle.style.transform = `translate(${endX - centerX}px, ${
          endY - centerY
        }px) scale(0.5)`;
        particle.style.opacity = "0";
      }, 10);

      setTimeout(() => {
        particle.remove();
      }, duration * 1000 + 100);
    }
  }
}

// Add 3D parallax effect on mouse movement
function initParallaxEffect() {
  const heroSection = document.querySelector(".hero");
  const heroShapes = document.querySelectorAll(".hero-shape");
  const heroContent = document.querySelector(".hero-content");
  const heroGlow = document.querySelector(".hero-glow");
  const heroTitle = document.querySelector(".hero-title");
  const heroDescription = document.querySelector(".hero-description");
  const ctaButtons = document.querySelector(".cta-buttons");

  if (!heroSection) return;

  heroSection.addEventListener("mousemove", (e) => {
    const x = e.clientX / window.innerWidth;
    const y = e.clientY / window.innerHeight;

    // Move shapes based on mouse position with 3D effect
    heroShapes.forEach((shape, index) => {
      const speed = 0.05 + index * 0.01;
      const xOffset = (x - 0.5) * 80 * speed;
      const yOffset = (y - 0.5) * 80 * speed;
      const zOffset = 20 + index * 10;

      shape.style.transform = `translate3d(${xOffset}px, ${yOffset}px, ${zOffset}px)`;
    });

    // Move glow effect
    if (heroGlow) {
      heroGlow.style.transform = `translate(${(x - 0.5) * -30}%, ${
        (y - 0.5) * -30
      }%)`;
    }

    // Subtle movement of content with 3D effect
    if (heroContent) {
      heroContent.style.transform = `translate3d(${(x - 0.5) * -15}px, ${
        (y - 0.5) * -15
      }px, 0)`;
    }

    // Enhanced depth effect for title and description
    if (heroTitle) {
      heroTitle.style.transform = `translate3d(${(x - 0.5) * -25}px, ${
        (y - 0.5) * -15
      }px, 10px)`;
    }

    if (heroDescription) {
      heroDescription.style.transform = `translate3d(${(x - 0.5) * -20}px, ${
        (y - 0.5) * -10
      }px, 5px)`;
    }

    if (ctaButtons) {
      ctaButtons.style.transform = `translate3d(${(x - 0.5) * -15}px, ${
        (y - 0.5) * -5
      }px, 15px)`;
    }
  });
}

// Make scroll indicator work with ripple effect
function initScrollIndicator() {
  const scrollArrow = document.querySelector(".scroll-arrow");
  const overviewSection = document.getElementById("overview");

  if (scrollArrow && overviewSection) {
    // Add click event
    scrollArrow.addEventListener("click", () => {
      overviewSection.scrollIntoView({ behavior: "smooth" });
    });

    // Add hover effect
    scrollArrow.addEventListener("mouseenter", () => {
      scrollArrow.style.transform = "translateY(-5px)";
    });

    scrollArrow.addEventListener("mouseleave", () => {
      scrollArrow.style.transform = "translateY(0)";
    });
  }
}

// Initialize tilt effect on stat cards
function initTiltEffect() {
  if (typeof VanillaTilt !== "undefined") {
    VanillaTilt.init(document.querySelectorAll(".stat-card"), {
      max: 12, // Reduced tilt angle for more subtlety
      speed: 500, // Slightly slower for smoother feeling
      glare: true,
      "max-glare": 0.2, // Reduced glare for elegance
      scale: 1.03, // Subtle scale effect
      perspective: 1200, // Increased perspective for better 3D effect
      transition: true,
      easing: "cubic-bezier(0.25, 0.1, 0.25, 1)", // Smoother easing function
      gyroscope: true,
      gyroscopeMinAngleX: -15,
      gyroscopeMaxAngleX: 15,
      gyroscopeMinAngleY: -15,
      gyroscopeMaxAngleY: 15,
    });
  }
}

// Initialize particle effects for stat icons
function initIconParticles() {
  const iconContainers = document.querySelectorAll(".icon-particles");

  iconContainers.forEach((container) => {
    const parent = container.closest(".stat-icon");
    if (!parent) return;

    // Create initial particles
    createIconParticles(container, 2); // Reduced for subtlety

    // Add more particles on hover
    parent.addEventListener("mouseenter", () => {
      createIconParticles(container, 5); // Reduced for cleaner look
    });
  });
}

// Create particles for icon containers
function createIconParticles(container, count) {
  for (let i = 0; i < count; i++) {
    setTimeout(() => {
      const particle = document.createElement("span");
      particle.className = "icon-particle";

      // Random properties
      const size = Math.random() * 4 + 2;
      const posX = Math.random() * 100;
      const posY = Math.random() * 100;
      const duration = Math.random() * 1.5 + 0.5;
      const delay = Math.random() * 0.5;

      // Set styles
      particle.style.width = `${size}px`;
      particle.style.height = `${size}px`;
      particle.style.position = "absolute";
      particle.style.backgroundColor = "rgba(255, 255, 255, 0.8)";
      particle.style.borderRadius = "50%";
      particle.style.top = `${posY}%`;
      particle.style.left = `${posX}%`;
      particle.style.opacity = "0";
      particle.style.transform = "scale(0)";
      particle.style.animation = `floatParticle ${duration}s ease-out ${delay}s forwards`;

      // Add to container
      container.appendChild(particle);

      // Remove after animation
      setTimeout(() => {
        particle.remove();
      }, (duration + delay) * 1000 + 100);
    }, i * 100);
  }
}

// Initialize entrance animations
function initEntranceAnimations() {
  const animatedElements = document.querySelectorAll(".animate-in");

  // Function to check if element is in viewport
  function isInViewport(element) {
    const rect = element.getBoundingClientRect();
    return (
      rect.top <=
        (window.innerHeight || document.documentElement.clientHeight) * 0.85 &&
      rect.bottom >= 0
    );
  }

  // Function to add visible class to elements in viewport
  function checkElements() {
    animatedElements.forEach((element) => {
      if (isInViewport(element)) {
        element.classList.add("visible");
      }
    });
  }

  // Run on page load
  checkElements();

  // Run on scroll
  window.addEventListener("scroll", checkElements);
}
