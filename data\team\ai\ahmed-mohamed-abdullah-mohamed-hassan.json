{"id": "ah<PERSON>-moh<PERSON>-<PERSON><PERSON><PERSON><PERSON>-moh<PERSON>-<PERSON>san", "name": "<PERSON>", "role": "AI Research Engineer", "team": "ai", "contact": {"phone": "+201006013561", "email": "<EMAIL>"}, "bio": "<PERSON> is an AI research engineer focused on developing innovative machine learning solutions. His expertise includes deep learning models and computer vision applications.", "education": ["M.S. Artificial Intelligence, Alexandria University, 2023", "B.S. Computer Science, Cairo University, 2021"], "skills": [{"name": "Python", "level": 95}, {"name": "TensorFlow", "level": 90}, {"name": "Computer Vision", "level": 85}, {"name": "Natural Language Processing", "level": 80}, {"name": "Data Analysis", "level": 85}], "projects": [{"name": "Object Detection System", "description": "Implemented a real-time object detection system with 95% accuracy using YOLOv5 architecture."}, {"name": "Sentiment Analysis Tool", "description": "Developed an advanced sentiment analysis tool for social media monitoring with multi-language support."}], "social": {"github": "https://github.com/ahmedalmohamdy", "linkedin": "https://www.linkedin.com/in/ahmedalmohamdy/", "twitter": "", "instagram": "", "facebook": ""}, "tasks": [{"title": "Develop Image Recognition Model", "description": "Create and train a deep learning model for accurate image recognition with 95%+ accuracy on the test dataset.", "status": "Completed", "startDate": "02/01/2025", "endDate": "02/15/2025", "hours": 45, "category": "Machine Learning", "icon": "image", "complexity": "High"}, {"title": "Implement NLP Pipeline", "description": "Design and implement a natural language processing pipeline for text classification and sentiment analysis with multi-language support.", "status": "Completed", "startDate": "02/20/2025", "endDate": "03/05/2025", "hours": 40, "category": "NLP", "icon": "language", "complexity": "High"}, {"title": "Optimize Model for Edge Devices", "description": "Optimize and compress machine learning models for deployment on resource-constrained edge devices while maintaining acceptable accuracy.", "status": "In Progress", "startDate": "03/10/2025", "endDate": "03/25/2025", "hours": 35, "category": "Optimization", "icon": "microchip", "complexity": "Medium"}, {"title": "Research Reinforcement Learning Approaches", "description": "Investigate and evaluate various reinforcement learning algorithms for potential application in autonomous decision-making systems.", "status": "Pending", "startDate": "03/28/2025", "endDate": "04/15/2025", "hours": 50, "category": "Research", "icon": "brain", "complexity": "High"}]}