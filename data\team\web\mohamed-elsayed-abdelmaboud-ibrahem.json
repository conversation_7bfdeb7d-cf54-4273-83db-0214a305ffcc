{"id": "mohamed-elsay<PERSON>-a<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "name": "<PERSON>", "role": "Backend Developer", "team": "web", "contact": {"phone": "+201029054588", "email": "<EMAIL>"}, "bio": "<PERSON> is a backend developer with expertise in server architecture and database optimization. He specializes in building efficient and scalable web services.", "education": ["B.S. Computer Science, Zagazig University, 2022", "Backend Development Certification, 2021"], "skills": [{"name": "Java", "level": 90}, {"name": "Spring Boot", "level": 85}, {"name": "SQL", "level": 90}, {"name": "RESTful APIs", "level": 85}, {"name": "Microservices", "level": 80}], "projects": [{"name": "Microservices Architecture", "description": "Designed and implemented a microservices architecture that improved system scalability and maintainability."}, {"name": "Database Performance Tuning", "description": "Optimized database performance through query optimization and indexing, reducing response times by 60%."}], "social": {"github": "https://github.com/Mohamedzonkol", "linkedin": "https://www.linkedin.com/in/mohamed-elsayed-265328249", "twitter": "", "instagram": "", "facebook": ""}}