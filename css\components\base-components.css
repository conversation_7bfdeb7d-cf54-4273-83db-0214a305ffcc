/* Base Component Styles */

/* Project Overview Styles */
.project-overview {
  background-color: var(--bg-white);
  padding: 40px;
  margin-bottom: 50px;
  box-shadow: var(--card-shadow);
  position: relative;
  transition: var(--transition);
  overflow: hidden;
  background-image: linear-gradient(
    120deg,
    rgba(240, 248, 255, 0.6) 0%,
    rgba(255, 255, 255, 0.9) 100%
  );
}

.project-overview::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%232346e8' fill-opacity='0.02' fill-rule='evenodd'/%3E%3C/svg%3E");
  pointer-events: none;
  z-index: 0;
}

.project-overview:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.12);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  position: relative;
}

.overview-badge {
  background: linear-gradient(
    135deg,
    var(--primary-light) 0%,
    var(--primary) 100%
  );
  color: white;
  padding: 6px 14px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  letter-spacing: 0.5px;
  box-shadow: 0 3px 10px rgba(67, 97, 238, 0.2);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(67, 97, 238, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0);
  }
}

.section-title {
  font-size: 28px;
  margin-bottom: 20px;
  color: var(--primary-dark);
  position: relative;
  display: inline-block;
  padding-bottom: 10px;
  z-index: 3;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary), var(--secondary));
  border-radius: 2px;
  transition: width 0.3s ease;
}

section:hover .section-title::after {
  width: 100%;
}

.overview-container {
  display: grid;
  grid-template-columns: 3fr 2fr;
  gap: 30px;
  margin-bottom: 30px;
}

.overview-intro {
  font-size: 1.1rem;
  margin-bottom: 20px;
  line-height: 1.8;
  color: var(--text-dark);
}

.overview-detail {
  color: var(--text-light);
  line-height: 1.7;
  margin-bottom: 25px;
}

.highlight-text {
  color: var(--primary);
  font-weight: 600;
  position: relative;
  display: inline-block;
}

.highlight-text::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--primary-light);
}

.overview-cta {
  margin-top: 25px;
}

.overview-btn {
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 10px;
}

.overview-btn:hover .btn-icon {
  transform: translateX(5px);
}

.btn-icon {
  transition: transform 0.3s ease;
}

.overview-metrics {
  display: flex;
  flex-direction: column;
  gap: 20px;
  background: rgba(255, 255, 255, 0.9);
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(232, 232, 232, 0.8);
  align-self: center;
}

.metrics-title {
  text-align: center;
  margin: 0 0 20px;
  font-size: 1.4rem;
  color: var(--text-dark);
  font-weight: 600;
  position: relative;
  padding-bottom: 12px;
}

.metrics-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 3px;
  background: var(--primary);
  border-radius: 2px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 15px 10px;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.metric-item:hover {
  background: rgba(240, 242, 255, 0.5);
  transform: translateY(-5px);
}

.circle-progress {
  position: relative;
  width: 120px;
  height: 120px;
  margin-bottom: 15px;
}

.progress-ring-circle-bg {
  fill: transparent;
  stroke: rgba(0, 0, 0, 0.05);
  stroke-width: 8px;
}

.progress-ring-circle {
  fill: transparent;
  stroke: var(--primary);
  stroke-width: 8px;
  stroke-linecap: round;
  transform: rotate(-90deg);
  transform-origin: 50% 50%;
  transition: stroke-dashoffset 1s ease-in-out;
}

.progress-ring-circle.water {
  stroke: #3498db;
}

.progress-ring-circle.disease {
  stroke: #27ae60;
}

.progress-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.metric-icon {
  font-size: 1.6rem;
  color: var(--primary);
  margin-bottom: 5px;
}

.metric-value {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--text-dark);
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.metric-label {
  font-size: 1rem;
  color: var(--text-dark);
  font-weight: 600;
  margin-bottom: 4px;
}

.metric-info {
  font-size: 0.85rem;
  color: var(--text-light);
  line-height: 1.4;
}

.overview-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 45px 0 35px;
  position: relative;
}

.divider-line {
  flex: 1;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(0, 0, 0, 0.1), transparent);
}

.divider-content {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 20px;
}

.divider-icon {
  font-size: 0.9rem;
  color: var(--primary);
  animation: rotate 6s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.divider-text {
  position: relative;
  background: white;
  padding: 0 10px;
  font-size: 1.3rem;
  color: var(--primary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Features Grid */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  margin: 30px 0;
}

.feature-card {
  background-color: var(--bg-white);
  border-radius: var(--border-radius);
  padding: 30px;
  box-shadow: var(--card-shadow);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(0, 0, 0, 0.05);
  text-align: center;
  position: relative;
  overflow: hidden;
  z-index: 1;
  transform-style: preserve-3d;
}

.feature-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 0;
  background: linear-gradient(to bottom, rgba(67, 97, 238, 0.05), transparent);
  transition: height 0.4s ease;
  z-index: -1;
}

.feature-card:hover {
  transform: translateY(-10px) rotateX(5deg) rotateY(-5deg);
  box-shadow: 0 20px 30px rgba(0, 0, 0, 0.1);
  border-color: var(--primary-light);
}

.feature-card:hover::before {
  height: 100%;
}

.feature-icon {
  font-size: 2rem;
  color: var(--primary);
  margin-bottom: 20px;
  background: rgba(67, 97, 238, 0.1);
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin: 0 auto 20px;
  transition: all 0.3s ease;
}

.feature-card:hover .feature-icon {
  background: var(--primary);
  color: white;
  transform: scale(1.1);
}

.feature-card h3 {
  font-size: 1.2rem;
  margin-bottom: 15px;
  color: var(--text-dark);
  transition: color 0.3s ease;
}

.feature-card:hover h3 {
  color: var(--primary);
}

.feature-card p {
  color: var(--text-light);
  font-size: 0.95rem;
  margin-bottom: 20px;
}

.feature-details {
  margin-top: auto;
  overflow: hidden;
  max-height: 0;
  transition: max-height 0.4s ease-in-out;
}

.feature-card:hover .feature-details {
  max-height: 150px;
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 0;
  text-align: left;
}

.feature-list li {
  margin-bottom: 8px;
  font-size: 0.9rem;
  color: var(--text-light);
  display: flex;
  align-items: center;
}

.feature-list li i {
  color: var(--success);
  margin-right: 8px;
  font-size: 0.85rem;
}

.overview-timeline {
  margin: 40px 0 30px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(232, 232, 232, 0.8);
}

.timeline-label {
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--text-dark);
  text-align: center;
}

.timeline-progress {
  position: relative;
}

.timeline-track {
  height: 6px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  position: relative;
  margin-bottom: 12px;
}

.timeline-fill {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 60%;
  background: linear-gradient(to right, var(--primary-light), var(--primary));
  border-radius: 3px;
  transition: width 1s ease;
}

.timeline-milestones {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-top: 10px;
}

.milestone {
  flex: 1;
  text-align: center;
  font-size: 0.85rem;
  font-weight: 500;
  color: var(--text-light);
  position: relative;
  padding-top: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.milestone::before {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 10px;
  height: 10px;
  background: #d4d4d4;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.milestone.active {
  color: var(--primary);
}

.milestone.active::before {
  background: var(--primary);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.3);
}

.milestone:hover {
  transform: translateY(-3px);
}

.overview-conclusion {
  text-align: center;
  color: var(--text-light);
  max-width: 800px;
  margin: 30px auto 0;
  font-style: italic;
  line-height: 1.7;
}

/* Utility classes */
.glass-card {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.stat-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
}

.text-center {
  text-align: center;
}

.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-3 {
  margin-bottom: 1rem;
}
.mb-4 {
  margin-bottom: 2rem;
}
.mb-5 {
  margin-bottom: 3rem;
}

.mt-1 {
  margin-top: 0.25rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-3 {
  margin-top: 1rem;
}
.mt-4 {
  margin-top: 2rem;
}
.mt-5 {
  margin-top: 3rem;
}

.p-1 {
  padding: 0.25rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-3 {
  padding: 1rem;
}
.p-4 {
  padding: 2rem;
}
.p-5 {
  padding: 3rem;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .overview-container {
    grid-template-columns: 1fr;
  }

  .overview-metrics {
    order: -1;
  }

  .tech-ribbon {
    margin: 0 -20px 30px;
    padding: 15px 20px;
  }
  
  .tech-ribbon-content {
    animation: slide 18s linear infinite;
  }
}

@media (max-width: 768px) {
  .project-overview {
    padding: 30px 20px;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .overview-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .overview-badge {
    margin-top: 10px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .divider-content {
    gap: 8px;
  }
  
  .divider-text {
    font-size: 1.1rem;
  }
  
  .award-banner {
    flex-direction: column;
    text-align: center;
    padding: 15px;
  }
}
